# Use Node.js 18 on Alpine Linux
FROM node:18-alpine

# Set the working directory
WORKDIR /src

# Copy package.json, package-lock.json, and .npmrc for GSAP authentication
COPY package*.json .npmrc ./

# Ensure GSAP authentication before installing dependencies
RUN if [ -f .npmrc ]; then echo "GSAP authentication found"; else echo "Missing .npmrc! Add your authentication."; exit 1; fi

# Install dependencies and resolve peer conflicts
RUN npm install --omit=dev --legacy-peer-deps

# Install sharp separately to avoid Alpine Linux issues
RUN apk add --no-cache libc6-compat && npm install sharp

# Copy the rest of the application code
COPY . .

# Build the Next.js application
RUN npm run build

# Expose the port
# EXPOSE 3000

# Start the application in production mode
CMD ["npm", "start"]