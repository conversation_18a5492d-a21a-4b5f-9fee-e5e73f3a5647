import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import ListSection from "@/components/author/ListSection";
import LatestSection from "@/components/author/LatestSection";
import {
  getAuthor,
  getAuthorLatest,
  getAuthorStories,
} from "@/pages/api/AuthorApi";
import { Const } from "@/utils/Constants";
import style from "@/components/author/Author.module.css";
import InfoSection from "@/components/author/InfoSection";

const AuthorDetails = ({
  info,
  initialData,
  initialCount,
  initialLatest,
  meta,
}) => {
  const router = useRouter();
  const [data, setData] = useState(initialData);
  const [count, setCount] = useState(initialCount);
  const [latest, setLatest] = useState(initialLatest);
  const [offset, setOffset] = useState(Const.Offset);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    setData(initialData);
    setCount(initialCount);
    setLatest(initialLatest);
    setOffset(Const.Offset);
    setHasMore(initialData.length < initialCount);
  }, [router.query.slug, initialData, initialCount]);

  const handleShowMore = async () => {
    const newOffset = offset + Const.Limit;
    setOffset(newOffset);

    const payload = {
      slug: `/${router.query.slug}`,
      limit: Const.Limit,
      offset: newOffset,
    };

    try {
      const response = await getAuthorStories(payload);
      const newItems = response?.data ?? [];
      const totalItems = response?.totalCounts ?? 0;

      setData((prev) => [...prev, ...newItems]);
      setOffset(newOffset);
      setCount(totalItems);

      // Determine if there is more data to load
      if (newItems.length === 0 || newOffset + Limit >= totalItems) {
        setHasMore(false);
      }
    } catch (e) {
      console.error("Error fetching more categories:", e);
    }
  };
  return (
    <>
      <Layout>
        <SeoHeader meta={meta} />
        <BreadcrumbSchema />
        <section id="author-section" className={style.authorSection}>
          <div className="container">
            <div className="row">
              <InfoSection data={info} />
              <div className="col-sm-12 col-md-12 col-lg-7 col-xl-8">
                <ListSection
                  name={info?.name || ""}
                  data={data}
                  handleShowMore={handleShowMore}
                  hasMore={hasMore}
                />
              </div>
              <div className=" col-sm-12 col-md-12 col-lg-5 col-xl-4 ln-cont">
                <LatestSection data={latest} />
              </div>
            </div>
          </div>
        </section>
      </Layout>
    </>
  );
};

export default AuthorDetails;

export async function getServerSideProps(context) {
  const url = `/${context.params.slug}`;
  const payload = {
    slug: url,
    limit: Const.Limit,
    offset: Const.Offset,
  };

  try {
    const [authorRes, authorStoriesRes, authorLatestRes] = await Promise.all([
      getAuthor(url),
      getAuthorStories(payload),
      getAuthorLatest(url),
    ]);

    if (authorRes.status !== "success") {
      return {
        notFound: true,
      };
    }

    const info = {
      name: authorRes?.data?.name || "",
      subheading: authorRes?.data?.subheading || "",
      image: authorRes?.data?.image || "",
      aboutus: authorRes?.data?.aboutus || "",
      social: authorRes?.data?.social || {},
      timestamp: authorRes?.data?.timestamp || "",
    };

    return {
      props: {
        info: info ?? {},
        initialCount: authorStoriesRes?.totalCounts ?? 0,
        initialData: authorStoriesRes?.data ?? [],
        initialLatest: authorLatestRes?.data ?? [],
        meta: authorRes?.data?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching category data:", error);
    return { notFound: true };
  }
}
