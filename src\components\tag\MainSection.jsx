import React, { useEffect } from "react";
import Image from "next/image";
import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Link from "next/link";
import { formatDateAndTime, getAuthorText } from "@/utils/Util";

const MainSection = ({ title, data, hasMore, handleShowMore }) => {
  // useEffect(() => {
  //   gsap.registerPlugin(ScrollTrigger);
  //   gsap.to(".image-zoom", {
  //     scale: 1,
  //     duration: 0.3,
  //     ease: "none",
  //     scrollTrigger: {
  //       trigger: ".image-zoom-parent",
  //       start: "top 60%",
  //       // markers: true,
  //     },
  //   });
  // }, []);

  return (
    <>
      {data && data.length > 0 ? (
        <>
          <div className="row">
            <div className="col-md-12">
              <span className="heading-title">More From: <h1>{title}</h1></span>
            </div>
            {/* <div className="col-md-12 zoom-cont">
              <Link href={data[0]?.slug ?? "/"} className="cat-card">
                <div className="image">
                  <Image
                    src={data[0]?.image ?? ""}
                    fill
                    className="imgcover"
                    alt="Oppenheimer Christopher Nolan"
                  />
                </div>
                <div className="content">
                  <h4>{data[0]?.title ?? ""}</h4>
                  <p>{data[0]?.description ?? ""}</p>
                </div>
              </Link>
            </div> */}
          </div>
          <div className="row secmt ln-count">
            <div className="col-md-12">
              {data?.map((item, index) => {
                return (
                  <Link
                    key={`tag-stories-item-${index}`}
                    className="cat-card"
                    href={item && item.slug ? item.slug : "/"}
                  >
                    <div className="image">
                      <Image
                        className="imgcover"
                        src={
                          item?.croppedImg
                            ? item?.croppedImg
                            : item?.image
                            ? item?.image
                            : ""
                        }
                        alt={item && item.altName ? item.altName : ""}
                        fill
                      />
                    </div>
                    <div className="content">
                      <div className="d-flex flex-columnn align-items-center gap-3 mb-2">
                        <span className="category">
                          {item && item.subcategory ? item.subcategory : ""}
                        </span>
                        <span className="timeline">
                          {item && item.timeline
                            ? formatDateAndTime(item?.timestamp)
                            : ""}
                        </span>
                      </div>
                      <h2>{item && item.title ? item.title : ""}</h2>
                      <p>{item && item.description ? item.description : ""}</p>
                      <span className="author">
                        {getAuthorText("BY", item?.author, item?.contributor)}
                      </span>
                    </div>
                  </Link>
                );
              })}
            </div>
            {hasMore && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <button
                  className="btn btn-outline out-btn4"
                  style={{ width: "118px" }}
                  onClick={handleShowMore}
                >
                  <div className="middle-btn"></div>
                  Show More
                </button>
              </div>
            )}
          </div>
        </>
      ) : (
        <>
          <div className="row">
            <div className="col-md-12">
              <h4>No stories to show</h4>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default MainSection;
