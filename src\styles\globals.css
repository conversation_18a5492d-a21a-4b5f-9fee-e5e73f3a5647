@import url("https://fonts.googleapis.com/css2?family=Karla:ital,wght@0,200..800;1,200..800&display=swap");

@font-face {
	font-family: font1;
	src: url(/wrapped/font/font1.woff2);
}
@font-face {
	font-family: font2;
	src: url(/wrapped/font/font2.woff);
}

@font-face {
	font-family: "Kepler-Std Regular Display";
	font-style: normal;
	font-weight: normal;
	src: local("Kepler Std Display"), url("../assets/fonts/KeplerStd-ScnDisp.otf") format("otf");
}

@font-face {
	font-family: "Kepler-Std Bold Semicondensed Display";
	font-style: normal;
	font-weight: normal;
	src: local("Kepler Std Bold Semicondensed Display"),
		url("../assets/fonts/KeplerStd-BoldScnDisp.woff") format("woff");
}

@font-face {
	font-family: "Kepler Std Bold Semicondensed Display Italic";
	font-style: normal;
	font-weight: normal;
	src: url("../assets/fonts/KeplerStd-ScnDisp.otf");
}

/* thr wrapped css start*/
::selection {
	background-color: #fff;
	color: #000;
}
/* ::-webkit-scrollbar {
  background-color: black;
  width: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 50px;
} */

#wrappedSection {
	padding-bottom: 50px;
}

#thr-main {
	position: relative;
}
#thr-wrap-loader {
	height: 100vh;
	width: 100%;
	background-color: white;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 99;
	display: flex;
	align-items: center;
	justify-content: center;
}
#thr-wrap-loader img {
	width: 15%;
}
#thr-wrap-loader h1 {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #de1f26;
	font-family: font2;
	font-weight: 600 !important;
	font-size: 3vw !important;
	line-height: 1;
	opacity: 0;
	text-align: center;
}
#thr-bg-vid {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	width: 100%;
}
#thr-bg-vid video {
	height: 100%;
	width: 100%;
	object-fit: cover;
}
#thr-wrap-container {
	position: relative;
	z-index: 5;
}
#section1w {
	position: relative;
	height: 100svh;
	width: 100%;
	color: white;
	padding: 3vw 0;
	padding-bottom: 0;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	overflow: hidden;
}
.hero-text-wrap {
	height: 5.4vw;
	overflow: hidden;
}
.hero-text-wrap h4 {
	font-size: 5.5vw;
	font-family: font2;
	text-align: center;
	line-height: 0.9;
	transform: translateY(120%);
}

#section1w h5 {
	font-size: 1vw;
	width: 45%;
	margin: 0 auto;
	margin-top: 1vw;
	text-align: center;
	font-weight: 400;
	font-family: font1 !important;
	opacity: 0;
}
#hero-text-wrap2 h2 {
	font-size: 18vw !important;
	font-weight: 500 !important;
	text-transform: uppercase;
	text-align: center;
	line-height: 0.8;
	transform: translateY(120%);
}

#hero-text-wrap2 {
	height: 15vw;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

#section2w {
	position: relative;
	min-height: 100vh;
	width: 100%;
	padding: 10vw 0;
	color: white;
}
#section2w .wrap-slide {
	width: 100%;
	display: flex;
	align-items: start;
	position: relative;
}

#section2w .wrap-slide .wrap-sides h5 {
	font-size: 1.5vw;
	font-family: font1 !important;
}
#section2w .wrap-slide .wrap-sides h3 {
	font-size: 3.5vw;
	margin: 2vw 0;
}
#section2w .wrap-slide .wrap-sides .readbtn {
	position: relative;
	width: fit-content;
	display: inline-block;
	padding: 0.7vw 1.5vw;
	border-radius: 50px;
	border: 1px solid #fff;
	display: flex;
	align-items: center;
	overflow: hidden;
}
#section2w .wrap-slide .wrap-sides .readbtn::after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #fff;
	top: 100%;
	left: 0;
	transition: all ease 0.3s;
}
#section2w .wrap-slide .wrap-sides .readbtn:hover:after {
	top: 0;
	border-radius: 0;
}
#section2w .wrap-slide .wrap-sides .readbtn h5 {
	position: relative;
	color: white;
	font-family: font1 !important;
	font-size: 1vw;
	line-height: 1;
	margin: 0;
	transition: all ease 0.3s;
	z-index: 9;
}
.border-wrap {
	border-bottom: 1px solid white;
}
.wrap-center {
	border-left: 1px solid white;
	border-right: 1px solid white;
	overflow: hidden;
	position: relative;
}
.wrap-inner-heading,
.wrap-inner-list {
	height: 100%;
	padding: 4vw 3vw;
}
.wrap-list-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 0.5vw;
}
.wrap-list-item .wrap-list-dot {
	width: 0.4vw;
	height: 0.4vw;
	display: inline-block;
	background-color: #fff;
	border-radius: 50%;
	white-space: wrap;
}
.wrap-list-item h4 {
	font-family: font1;
	font-size: 1.2vw;
	width: 95%;
}
#section2w .wrap-slide #wrap-line-top {
	width: 100%;
	height: 1px;
	background-color: #fff;
	position: absolute;
	top: 0;
	left: 50%;
	transform: translateX(-50%);
}
#section2w .wrap-slide .wrap-sides .readbtn:hover h5 {
	color: black;
}
#section2w .wrap-slide .wrap-inner {
	width: calc(100% / 3);
	height: 100%;
}
#section2w .wrap-slide .wrap-inner img {
	width: 100%;
}
#footer-w {
	position: relative;
	height: 100vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	color: white;
	padding: 2vw;
}
#footer-text h1 {
	font-size: 18vw;
	text-transform: uppercase;
	text-align: center;
	line-height: 0.8;
}

#footer-w h5 {
	font-size: 1vw;
	width: 45%;
	margin: 0 auto;
	text-align: center;
	font-weight: 400;
	font-family: font1 !important;
	white-space: nowrap;
}
#footer-tp h5 {
	width: 60%;
	white-space: normal;
}
#footer-w span {
	font-size: 1vw;
	width: 45%;
	margin: 0 auto;
	text-align: center;
	font-weight: 400;
	font-family: font1 !important;
	white-space: nowrap;
	color: white;
	background-color: black;
	padding: 0.3vw 1vw;
}

#footer-btm {
	display: flex;
	gap: 2.5vw;
	align-items: center;
	justify-content: center;
	width: fit-content;
	margin: 0 auto;
	margin-top: 2vw;
}

.wrap-text-container-center {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
	width: 100%;
	height: 100%;
	background: linear-gradient(to bottom, transparent 10%, rgba(0, 0, 0, 0.718));
	color: white;
	display: none;
	flex-direction: column;
	justify-content: end;
	padding: 4vw 3vw;
}
.wrap-text-container-center h5 {
	color: white !important;
	font-family: font1 !important;
	font-size: 2.8vw;
	margin: 0;
}

.readbtn-mb {
	position: relative;
	width: fit-content;
	display: inline-block;
	padding: 0.7vw 2.4vw;
	border-radius: 50px;
	border: 1px solid #fff;
	display: flex;
	align-items: center;
	overflow: hidden;
}
.readbtn-mb::after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background-color: #fff;
	top: 100%;
	left: 0;
	transition: all ease 0.3s;
}
.readbtn-mb:hover:after {
	top: 0;
	border-radius: 0;
}
.readbtn-mb:hover h5 {
	color: black !important;
}
.readbtn-mb h5 {
	position: relative;
	z-index: 5;
}

#wrapped-banner-container {
	max-width: 1320px;
	margin: auto;
	margin-top: 1vw;
	padding: 0 12px;
	text-align: center;
}
#wrapped-banner-vid-container {
	position: relative;
	height: 100%;
	width: 100%;
	overflow: hidden;
	height: 200px;
}
#wrapped-banner-vid-container video {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
}
#wrapped-banner-text-container {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
#wrapped-banner-text-container h2 {
	margin: 0;
	color: white;
	font-weight: 400 !important;
}

#wrapped-banner-button {
	position: relative;
	padding: 7.2px 1.5vw;
	border: 1px solid white;
	border-radius: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: fit-content;
	cursor: pointer;
	overflow: hidden;
	margin: 0 auto;
	margin-top: 1vw;
}
#wrapped-banner-button::after {
	content: "";
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: white;
	top: 100%;
	left: 0;
	border-radius: 50%;
	transition: all ease 0.3s;
}

#wrapped-banner-button:hover:after {
	top: 0;
	border-radius: 0;
}

#wrapped-banner-button h5 {
	position: relative;
	z-index: 3;
	font-size: 0.8vw;
	font-family: font1 !important;
	color: white;
	font-weight: 500 !important;
	text-transform: uppercase;
	margin: 0;
	white-space: nowrap;
}
#wrapped-banner-button:hover h5 {
	color: #000;
}

#topstories hr {
	display: none !important;
	border: none !important;
}

.video-block-section .image-sec {
	aspect-ratio: 16/9;
	height: unset !important;
}

@keyframes blink {
	0% {
		opacity: 1;
	}
	50% {
		opacity: 0.3;
	}
	100% {
		opacity: 1;
	}
}

/* Newsletter Footer Styles */
.newsletter-footer {
	background-color: var(--primary-color);
	color: #fff;
	padding: 60px 0;
	width: 100%;
}

.newsletter-footer .container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 15px;
}

.newsletter-footer .newsletter-content {
	max-width: 800px;
	margin: 0 auto;
	text-align: center;
}

.newsletter-footer h2 {
	font-size: 36px;
	font-weight: 600;
	margin-bottom: 15px;
	color: #fff;
}

.newsletter-footer p {
	font-size: 18px;
	/* margin-bottom: 30px; */
	font-family: var(--font-family-secondary) !important;
	color: #fff;
}

.newsletter-footer .newsletter-features {
	display: flex;
	justify-content: center;
	gap: 40px;
	margin-bottom: 30px;
}

.newsletter-footer .feature-item {
	display: flex;
	align-items: center;
	gap: 10px;
	font-size: 18px;
	color: #fff;
	font-family: var(--font-family-secondary);
}

.newsletter-footer .feature-icon {
	font-size: 18px;
	color: var(--primary-color);
	font-size: 18px;
	height: 20px;
	width: 20px;
	background-color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.newsletter-footer .newsletter-form {
	margin-bottom: 20px;
}

.newsletter-footer .form-group {
	display: flex;
	max-width: 700px;
	font-size: 20px;
	margin: 0 auto;
	font-family: "kepler-std", serif !important;
}

.newsletter-footer .form-control {
	flex: 1;
	height: 50px;
	padding: 10px 20px;
	border: none;
	border-radius: 0;
	font-size: 16px;
}

.newsletter-footer .sign-up-btn {
	background-color: var(--color);
	color: #fff;
	border: none;
	padding: 10px 40px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	height: 50px;
	border-radius: 0px;
}
.under-line {
	text-decoration: underline !important;
}
.newsletter-footer .sign-up-btn:hover {
	background-color: #000;
	color: #de1f26;
}

.newsletter-footer .success-message {
	color: #4bb543;
	margin-top: 10px;
	font-weight: 500;
}

.newsletter-footer .newsletter-disclaimer {
	font-size: 14px;
	color: #000;
	max-width: 600px;
	margin: 0 auto;
	font-family: var(--font-family-secondary) !important;
	margin-top: 20px;
}

.newsletter-footer .newsletter-disclaimer a {
	color: #000;
	text-decoration: underline;
}

.sponsored-tag {
	color: #f3f3f3;
	width: fit-content;
	padding: 2px 8px;
	background-color: var(--primary-color) !important;
	font-family: var(--font-family-secondary) !important;
	text-transform: uppercase;
	font-size: 14px;
}

/* reviews */
.reviewInnerContainer {
	display: flex;
	width: calc(100% - 35px);
	margin-top: 1rem;
	background-color: #fff6f0;
	border-radius: 6px;
	overflow: hidden;
	border: 1px solid #f0eae0;
	margin-bottom: 1rem;
}

.reviewInnerContainer .container1 {
	width: 30%;
	padding: 20px 25px 35px 25px;
	position: relative; /* Needed for absolute positioning of the line */
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

/* Custom dashed vertical line */
.reviewInnerContainer .container1::after {
	content: "";
	position: absolute;
	top: 15px;
	right: 0;
	height: 85%;
	width: 1px;
	border: 0.1px dashed #3c3c3ca6;
}

.reviewInnerContainer .container1 h4 {
	font-family: var(--font-family-secondary);
	margin-bottom: 10px;
	font-weight: 700;
	font-size: 1.5rem;
}

.descriptionText h5 {
	font-size: 20px;
	font-weight: 700;
	font-family: var(--font-family-secondary);
	color: var(--primary-color);
	margin-bottom: 4px;
}

.descriptionText p {
	font-family: var(--font-family-desc) !important;
	font-style: italic;
	font-size: 16px;
	margin: 0;
	letter-spacing: 0.5px;
}

.reviewInnerContainer .container2 {
	width: 70%;
	padding: 20px 30px;
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.reviewText {
	display: flex;
	align-items: flex-start;
}

.reviewText .h5 {
	font-size: 16px;
	font-weight: 700;
	font-family: var(--font-family-secondary);
	margin: 0;
	white-space: nowrap;
}

.reviewText span:nth-child(2) {
	padding-left: 5px;
}
.reviewText p {
	font-family: var(--font-family-secondary) !important;
	font-size: 16px;
	margin: 0;
	padding-left: 5px;
}

/* Responsive styles for newsletter footer */
@media (max-width: 768px) {
	.newsletter-footer {
		padding: 40px 0;
	}

	.newsletter-footer h2 {
		font-size: 28px;
	}

	.newsletter-footer p {
		font-size: 16px;
	}

	.newsletter-footer .newsletter-features {
		flex-direction: row;
		gap: 8px;
		align-items: center;
	}
	.newsletter-footer .feature-item {
		font-size: 16px;
	}

	/* .newsletter-footer .form-group {
    flex-direction: column;
    gap: 10px;
  } */

	/* .newsletter-footer .sign-up-btn {
    width: 100%;
  } */
	.story-container .mob-hidden {
		display: none;
	}

	.reviewInnerContainer {
		margin-bottom: 0rem;
	}
	.reviewInnerContainer .container1 {
		width: 100%;
	}
	.reviewInnerContainer .container2 {
		width: 100%;
		padding: 15px 20px;
	}
	.reviewInnerContainer .container1 {
		padding: 20px 25px 20px 25px;
	}
	.reviewInnerContainer .container1::after {
		top: unset;
		bottom: 0px;
		right: 0;
		height: 1px;
		left: 20px;
		width: 90%;
		text-align: center;
	}
	.reviewInnerContainer {
		flex-direction: column;
	}
	.reviewInnerContainer .container1 h4 {
		font-size: 1.5rem;
	}
	.descriptionText h5 {
		font-size: 18px;
	}
	.descriptionText p {
		font-size: 16px;
	}
}

/* thr wrap responsive */

@media (max-width: 600px) {
	.wrap-text-container-center {
		display: flex;
	}
	.newsletter-footer .newsletter-features {
		flex-direction: row;
		gap: 8px;
		align-items: center;
	}
	.newsletter-footer .form-group {
		flex-direction: column;
		gap: 10px;
	}

	.newsletter-footer .sign-up-btn {
		width: 100%;
	}
	.newsletter-footer .feature-item {
		font-size: 16px;
		gap: 2px;
		line-height: 1;
		margin-top: 30px;
	}
	.wrap-text-container-center h5 {
		font-size: 2.8vw;
		white-space: nowrap !important;
	}
	.wrap-center {
		border-left: none;
	}
	#thr-wrap-loader img {
		width: 45%;
	}
	#thr-wrap-loader h1 {
		font-weight: 900;
		font-size: 8vw !important;
	}
	.hero-text-wrap {
		height: 12.5vw;
	}
	.hero-text-wrap h4 {
		font-size: 12vw;
		line-height: 0.9;
	}
	.wrap-left-remove {
		display: none;
	}
	#section2w .wrap-slide .wrap-inner {
		width: calc(100% / 2);
		/* overflow: hidden; */
	}
	.wrap-list-item {
		margin-bottom: 0.5vw;
		gap: 1vw;
	}
	.wrap-list-item .wrap-list-dot {
		width: 1.5vw;
		height: 1.5vw;
	}
	.wrap-list-item h4 {
		font-size: 2.8vw;
		font-family: 400 !important;
		width: 92%;
	}
	#section1w h5 {
		font-size: 3.5vw;
		width: 80%;
		margin-top: 4vw;
		font-weight: 300;
	}
	#hero-text-wrap2 {
		height: 50.5vw;
	}
	#hero-text-wrap2 h2 {
		font-weight: 500 !important;
		font-size: 25vw !important;
	}
	#section1w {
		padding: 10vw 0;
		padding-bottom: 2vw;
	}
	#footer-text h1 {
		font-size: 25vw;
	}
	#footer-w h5 {
		font-size: 3.5vw;
		width: 100%;
		margin: 0 auto;
		white-space: normal !important;
	}
	#footer-btm {
		display: flex;
		gap: 2.5vw;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-direction: column-reverse;
		margin-top: 8vw;
	}
	#footer-tp h5 {
		font-size: 3.5vw;
		width: 80%;
		margin-top: 8vw;
		font-weight: 300;
	}
	#footer-w span {
		font-size: 3.5vw;
		padding: 0.8vw 1.5vw;
	}
	#footer-w {
		padding-top: 10vw;
		padding-bottom: 6vw;
	}
}

@media (min-width: 601px) and (max-width: 767px) {
	#thr-wrap-loader img {
		width: 35%;
	}
	#thr-wrap-loader h2 {
		font-size: 6vw !important;
	}
	.hero-text-wrap {
		height: 12.5vw;
	}
	.hero-text-wrap h4 {
		font-size: 12vw;
	}
	#section1w h5 {
		font-size: 2.2vw;
		width: 65%;
		margin-top: 1.5vw;
	}
	.wrap-list-item h4 {
		font-family: font1;
		font-size: 1.8vw;
		width: 95%;
	}
	#section2w .wrap-slide .wrap-sides .readbtn h5 {
		font-size: 1.8vw;
	}
	#section2w .wrap-slide .wrap-sides h5 {
		font-size: 1.8vw !important;
		font-family: font1 !important;
	}
	#footer-tp h5 {
		font-size: 2.2vw;
		width: 65%;
		margin-top: 1.5vw;
	}
	#footer-w h5 {
		font-size: 2.2vw;
		width: 65%;
		white-space: normal;
	}
	#footer-w span {
		font-size: 2.2vw;
		padding: 0.3vw 1.5vw;
	}
	#footer-btm {
		gap: 4vw;
		margin-top: 6vw;
	}
	#footer-w {
		padding: 4vw 0;
	}
}

@media (min-width: 768px) and (max-width: 1023px) {
	#thr-wrap-loader img {
		width: 28%;
	}
	#thr-wrap-loader h2 {
		font-size: 5vw !important;
	}
	.hero-text-wrap {
		height: 9.8vw;
	}
	.hero-text-wrap h4 {
		font-size: 9.5vw;
	}
	#section1w h5 {
		font-size: 1.8vw;
		width: 70%;
		font-weight: 300;
	}
	#footer-tp h5 {
		font-size: 1.8vw;
		width: 70%;
		white-space: normal;
	}
	#footer-w h5 {
		font-size: 1.8vw;
		width: 70%;
		font-weight: 300;
		white-space: normal;
	}
	#footer-w span {
		font-size: 1.8vw;
		padding: 0.3vw 1.5vw;
	}
	#footer-btm {
		gap: 5vw;
		margin-top: 4vw;
	}
	.wrap-list-item h4 {
		font-size: 1.8vw;
	}
	#section2w .wrap-slide .wrap-sides .readbtn h5 {
		font-size: 1.5vw;
	}
}

@media (min-width: 1024px) and (max-width: 1279px) {
	#thr-wrap-loader img {
		width: 22%;
	}
	#thr-wrap-loader h2 {
		font-size: 4vw !important;
	}
	.hero-text-wrap {
		height: 8.2vw;
	}
	.hero-text-wrap h4 {
		font-size: 8vw;
	}
	#section1w h5 {
		font-size: 1.3vw;
		width: 70%;
	}
	#footer-w h5 {
		font-size: 1.3vw;
		width: 70%;
		white-space: normal;
	}
	#footer-w span {
		font-size: 1.3vw;
	}
	#footer-btm {
		gap: 4vw;
		margin-top: 3vw;
	}
	.wrap-list-item h4 {
		font-size: 1.5vw;
	}
}

/* thr wrapped css end*/

:root {
	--global-height: 190px;
	--global-width: 432px;
	--gray: rgba(0, 0, 0, 0.5);
	--black: rgba(0, 0, 0, 1);
}

.image-div {
	/* width: var(--global-width);
  height: var(--global-height); */
	width: 100% !important;
	height: auto !important;
	aspect-ratio: 16/9;
}

.AlignCenter {
	align-items: center !important;
}

h2,
h3 {
	font-family: "kepler-std-semicondensed-dis", serif;
}

.h100 {
	height: 100%;
	width: 100px;
	min-width: 100px;
}

.h-par {
	height: 140px;
}

:root {
	--font-family-secondary: "Karla", sans-serif;
	--font-family-primary: "kepler-std-semicondensed-dis", serif;
	--font-family-desc: "Kepler Std Display", "serif";
	--font-family-kepler-std: "kepler-std", serif;
	--font-mono: ui-monospace, Menlo, Monaco, "Cascadia Mono", "Segoe UI Mono", "Roboto Mono",
		"Oxygen Mono", "Ubuntu Monospace", "Source Code Pro", "Fira Mono", "Droid Sans Mono",
		"Courier New", monospace;
	--color: #000;
	--background-color: #fff;
	--primary-color: #db242a;
	--secondary-color: #636363;
	--swiper-navigation-size: 16px !important;
	--swiper-theme-color: #db242a !important;
	--bs-link-color-rgb: #000;
}

* {
	box-sizing: border-box;
	padding: 0;
	margin: 0;
}

html,
body {
	max-width: 100vw;
}

body {
	font-family: var(--font-family-primary) !important;
	color: var(--color);
	background: var(--background-color);
	font-weight: 400;
	overflow-x: hidden;
}

h2 {
	font-size: 50px !important;
	margin-bottom: 20px;
	font-weight: 700 !important;
}
.heading-title {
	display: block;
	font-size: 50px !important;
	margin-bottom: 20px;
	font-weight: 700 !important;
}
.heading-title h1 {
	display: inline;
	font-size: 50px !important;
	font-weight: 700 !important;
	margin-bottom: 0px !important;
}
span.first-char-color {
	color: var(--primary-color);
}

p {
	font-size: 18px;
	font-family: "kepler-std-display", serif !important;
	font-weight: 300;
	font-style: normal;
	/* font-family: var(--font-family-secondary); */
}

h4 {
	font-family: var(--font-family-secondary);
}

.btn-outline {
	background-color: #fff;
	color: var(--color);
	font-size: 16px;
	font-weight: 500;
	border-radius: 20px !important;
	padding: 5px 20px;
	border: 1px solid var(--color);
	/* margin-bottom: 10px; */
	margin-bottom: 50px;
	font-family: var(--font-family-secondary) !important;
}

/* .btn-outline:hover {
  background-color: var(--color);
  color: #fff;
  border: 1px solid var(--color);
}*/
hr {
	color: var(--color);
	opacity: 0.5 !important;
	margin: 1rem 0 !important;
}
.ad-flex-all {
	display: flex;
	align-items: center;
	justify-content: center;
}
.ad-parent-leaderboard {
	margin: 30px auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

/* .ad-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
} */

.Home_reviews__JJohB .Home_card__lTbqS .Home_image__jCdNc {
	aspect-ratio: 2;
	/* width: 320px !important;
  min-width: 320px !important; */
}

/* @media only screen and (max-width: 375px) {
  .Home_reviews__JJohB .Home_card__lTbqS .Home_image__jCdNc {
    width: 130px !important;
    min-width: 130px !important;
    height: 85px !important;
  }
  .Home_reviews__JJohB .Home_card__lTbqS .Home_image__jCdNc img {
    object-fit: fill;
  }
} */

.ad-parent-halfpage {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 600px;
	margin: 20px 0;
}

.ad-parent-medium-rectangle {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 250px;
	margin: 20px 0;
}

.ad-text::before {
	color: #8c8c8c;
	content: "ADVERTISEMENT";
	display: block;
	font-family: Arial, sans-serif;
	font-size: 9px;
	font-weight: 400;
	letter-spacing: 1px;
	line-height: 1.2;
	margin: 5px auto;
	text-align: center;
	text-transform: uppercase;
	-webkit-font-smoothing: antialiased;
}

.ad-anime {
	width: 100%;
	position: relative;
	z-index: 55;
	height: 100%;
	transform: translateY(-100%);
	transition: all 1s ease;
}

.takeover-block {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: gainsboro;
	z-index: 9;
	/* display: none; */
	background: rgba(0, 0, 0, 0.8);
	transition: all 1s ease;
}

.takeover-ad {
	width: 100vw;
	/* height: 43vw; */
	aspect-ratio: 970/250;
	transition: all 1s ease;
}
/* .takeover-ad img {
  object-position: 0px -150px;
} */
.takeover-p {
	height: 50px;
	width: 100%;
	font-size: 17px;
	cursor: pointer;
	/* background-color: rgba(255, 255, 255, 1); */
	letter-spacing: 1px;
	color: white;
	/* color: white; */
	transition: all 0.3 s ease;
	font-family: var(--font-family-secondary);
}

.takeover-p:hover {
	/* background-color: black; */
	/* color: white; */
}

.takeover-rest {
	width: 100%;
	height: 15%;
	background-color: black;
	background: linear-gradient(180deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
}

.takeover-close {
	width: 60px;
	height: 60px;
	background-color: white;
	border-radius: 50%;
	color: black;
	margin: auto 0px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.takeover-close svg {
	font-size: 30px;
	margin-left: -2px;
}

.takeover-close:hover {
	transform: rotate(180deg);
}

.mp-margin {
	margin: 30px 0px 5px 0px;
}

.row-featurevideo {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 35px;
	margin-bottom: 20px;
}

.swiper-wrapper {
	margin-bottom: 50px;
}

#featuredvideo .swiper-wrapper {
	margin-bottom: 20px;
}

.swiper-button-prev,
.swiper-button-next {
	top: var(--swiper-navigation-top-offset, 35%) !important;
	width: 30px !important;
	height: 30px !important;
	padding: 5px;
	background-color: var(--color);
	color: #fff !important;
	border-radius: 50%;
}

.btn-swiper {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	width: 40px !important;
	height: 40px !important;
	padding: 5px !important;
	background-color: #fff !important;
	color: var(--color) !important;
	border: 1px solid var(--color) !important;
	border-radius: 50% !important;
}

.btn-swiper:hover {
	background-color: var(--color) !important;
	color: #fff !important;
	border: 1px solid var(--color) !important;
}

.btn-swiper .icon {
	width: 25px;
	height: 25px;
}

.btn-scrollable {
	position: fixed;
	right: 3.6%;
	bottom: 5%;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	padding: 5px;
	background-color: var(--primary-color);
	color: #fff;
	border: 1px solid var(--primary-color);
	border-radius: 50%;
	cursor: pointer;
	opacity: 0;
	pointer-events: none;
	transition: all 0.3s ease;
	z-index: 5;
}

.t3-scroll {
	opacity: 1;
	pointer-events: all;
}

.btn-scrollable:hover {
	background-color: #fff;
	color: var(--primary-color);
	border: 1px solid var(--primary-color);
}

.btn-scrollable svg {
	width: 25px;
	height: 25px;
}

.ad-space {
	width: 100vw;
	margin: 20px auto !important;
	height: 150px;
	/* background-color: black; */
	font-size: 100px;
	/* color: white; */
	font-family: var(--font-family-secondary);
	font-optical-sizing: auto;
	font-weight: 400;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.ad-space-side {
	width: 100%;
	/* margin: 20px auto !important; */
	/* height: 30vw; */
	/* background-color: #f3f4f5; */
	font-size: 100px;
	color: #000;
	font-family: var(--font-family-secondary);
	font-optical-sizing: auto;
	font-weight: 400;
	font-style: normal;
	font-variation-settings: "wdth" 100;
}

.ad-cont-slide {
	position: relative;
	margin: 0 auto;
	transform: scale(1);
}

.ad-cont-slide img {
	object-fit: contain;
}

.ad-cont {
	position: relative;
	margin: 0 auto;
	width: 100%;
	height: 130px;
	transform: scale(1.8);
}

.ad-cont2 {
	position: relative;
	margin: 0 auto;
	width: 728px;
	height: 90px;
	transform: scale(1.8);
}

.ad-cont img {
	object-fit: contain;
}

.flex-all {
	display: flex;
	justify-content: center;
	align-items: center;
}

.tn-nav {
	/* position: sticky; */
	top: 15px;
	width: 90vw;
	/* overflow: hidden; */
	/* height: 165px; */
	transition: all 0.3s ease;
	border-radius: 35px;
	/* background-color: white; */
	margin: auto;
	z-index: 4;
}

.hidden {
	display: none;
}

.nav-small {
	position: absolute;
	top: 0px;
	border-radius: 0;
	transition: none;
	opacity: 0;
	pointer-events: none;
}

.small-logo {
	height: 70px;
}

.navsmall-main-div {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-inline: 30px;
}

.trans {
	position: sticky;
	top: 0;
	opacity: 1;
	pointer-events: all;
}

.tn-nav-rel {
	position: relative;
	width: 100%;
	z-index: 8;
}

.nav-stik {
	position: sticky;
	top: 0;
}

.tn-stik {
	position: sticky;
	top: 0;
}

.tn {
	display: flex;
	padding: 0px 30px;
	z-index: 1;
	position: relative;
	justify-content: space-between;
	align-items: center;
	height: 100px;
}

.tn-logo {
	position: absolute;
	top: 60%;
	left: 50%;
	display: flex;
	align-items: center;
	transform: translate(-50%, -50%);
	height: 62%;
}

.tn-elm {
	cursor: pointer;
	font-size: 21px;
	color: #d40f16 !important;
}

.xsvg {
	height: 31px;
	width: 31px;
	/* width: 30px; */
}

.visihidden {
	visibility: hidden;
}

.tn-elm:hover {
	color: #d40f16;
}

.ssrh {
	width: 120px;
	border-radius: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 40px;
	position: absolute;
	bottom: 15px;
	background-color: #d92128;
	color: white;
	cursor: pointer;
	/* transition: co .3s ease; */
	gap: 8px;
}

.ssrh:hover {
	border: 1px solid #d40f16;
	background-color: white;
	color: #d40f16;
}

.out-btn2 {
	position: relative !important;
	width: 73px;
	height: 29px !important;
	display: flex !important;
	border-radius: 20px !important;
	border: 1px solid black !important;
	align-items: center !important;
	justify-content: center !important;
	overflow: hidden !important;
	padding: 0 !important;
}

.out-btn3 {
	position: relative !important;
	width: 110px;
	height: 35px !important;
	display: flex !important;
	border-radius: 20px !important;
	border: 1px solid black !important;
	align-items: center !important;
	justify-content: center !important;
	overflow: hidden !important;
	padding: 0 !important;
	margin: 15px auto;
}

.tn-logo img {
	height: 110%;
	transform: scale(2.4);
	cursor: pointer;
}

.parent-nav {
	height: 100vh;
	width: 100vw;
	position: fixed;
	transition: all 0.3s ease-in;
	top: 0;
	z-index: 7;
	pointer-events: none;
}

.video-div {
	width: 100%;
	position: relative;
}

.blur {
	background: rgba(0, 0, 0, 0.7);
	z-index: 7;
}

.nonpointer {
	pointer-events: none;
}

.tn-left {
	display: flex;
	gap: 18px;
	align-items: center;
}

.tn-sub {
	color: #db242a;
	font-weight: 600;
}

.tn-right {
	position: relative;
	font-family: var(--font-family-secondary);

	display: flex;
	/* gap: 5px; */
	/* padding: 5px 20px; */
	height: 35px;
	align-items: center;
	color: #db242a;
	font-weight: 600;
	font-size: 15px;
	cursor: pointer;
}

.sdiv {
	display: flex;
	gap: 5px;
	border-radius: 30px;
	transition: all 0.3s ease;
	padding: 5px 20px;
}

.sdiv:hover {
	background-color: #ffdada;
}

.tn-si {
	/* position: absolute;
    bottom: 0;
    right: 5px; */
	/* width: 32px;
    height: 32px; */
	font-size: 20px;
}

.tn-right input {
	width: 180px;
	height: 32px;
	border-radius: 30px;
	font-size: 14px;
	border: 1px solid gray;
	outline: none;
	transition: all 0.4s ease;
	padding: 0px 20px;
}

.t3 {
	transition: all 0.4s ease;
}

.tn-right input:hover {
	border: 1px solid #db242a;
}

.tn-right input:hover {
}

.search {
	position: fixed;
	font-family: var(--font-family-secondary);
	/* top: 60px; */
	top: 70px;
	height: 100vh;
	width: 100%;
	pointer-events: none;
	z-index: 9;
	/* background-color: rgba(0, 0, 0, .5); */
}

.search-div {
	transform: translateY(-100%);
	background-color: white;
	padding: 81px;
	width: 100%;
	display: flex;
	gap: 15px;
	flex-direction: column;
	/* box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px; */
	/* box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px; */
}

.search-input-div {
	width: 100%;
	height: 40px;
	border: none;
	border-radius: 20px;
	padding-inline: 5px;
}

.search-input-div:focus-visible {
	outline: none;
}

.search-main-div {
	border: 1px solid #d40f16;
	color: #d40f16;
	display: flex;
	border-radius: 20px;
	align-items: center;
	justify-content: space-between;
	padding-inline: 10px;
}

.search-input {
	width: 100%;
	height: 20vh;
	/* background-color: red; */
	border: none;
	outline: none;
	background: none;
	font-size: 70px;
}

.search-textarea {
	width: 100%;
	height: 100%;
	resize: none;
	border: none;
	outline: none;
	font-size: 40px;
}

.search-input::placeholder {
	color: rgba(0, 0, 0, 0.2);
	opacity: 1;
	/* Firefox */
}

.search-input::-ms-input-placeholder {
	/* Edge 12 -18 */
	color: rgba(0, 0, 0, 0.2);
}

.search-close {
	position: absolute;
	right: 3vw;
	width: 3.5vw;
	height: 3.5vw;
	border-radius: 50%;
	box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
	font-size: 26px;
	cursor: pointer;
}

.search-close:hover {
	transform: rotate(180deg);
}

.popr {
	margin-top: 10vh;
	color: rgba(0, 0, 0, 0.8);
	display: flex;
}

.popr-t {
	display: flex;
	align-items: center;
	gap: 10px;
}

.poprs {
	display: flex;
	gap: 10px;
}

.pop-main {
	padding: 10px 10px;
	height: 50px;
	display: flex;
	background-color: rgb(165 160 160 / 20%);
	gap: 10px;
	align-items: center;
	border-radius: 25px;
}

.play-btn {
	position: absolute;
	/* color: white; */
	/* opacity: 0.7; */
	top: 87%;
	font-size: 60px;
	z-index: 1;
	left: 7%;
	transform: translate(-50%, -50%);
	height: 80px;
	width: 80px;
	background-color: #5a5a5a;
	border-radius: 50px;
	border: 4px solid white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-btn-only {
	position: absolute;
	/* color: white; */
	/* opacity: 0.7; */
	top: 82%;
	font-size: 60px;
	z-index: 1;
	left: 10%;
	transform: translate(-50%, -50%);
	height: 20px;
	width: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-btn-svg {
	font-size: 45px;
	color: white;
	font-weight: 700;
	position: relative;
	left: 39px !important;
}

.play-btn-small {
	position: absolute;
	top: 81%;
	font-size: 56px;
	z-index: 1;
	left: 11%;
	transform: translate(-50%, -50%);
	height: 30px;
	width: 30px;
	background-color: #5a5a5a;
	border-radius: 50px;
	border: 3px solid white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-btn-svg-small {
	font-size: 11px;
	color: white;
	font-weight: 700;
	position: relative;
	left: 16px;
}

.pop-main:hover {
	background-color: rgb(165 160 160 / 30%);
	cursor: pointer;
}

.btn2 {
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.2) !important;
	display: inline-block;
	background-color: #000;
	position: absolute;
	vertical-align: middle;
	line-height: 99px;
	height: 80px;
	width: 80px;
	color: #fff;
	text-align: center;
	transition: all 0.8s;
	cursor: pointer;
	-webkit-user-select: none;
	transition-timing-function: cubic-bezier(0.4, 0.08, 0, 0.97);
	z-index: 2;
}

.btn2:hover {
	background: rgba(0, 0, 0, 0.4);
	letter-spacing: 2px;
}

.btn2 svg {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.circle {
	transform: rotate(-90deg);
	transform-origin: center;
	stroke-dasharray: 301.59px 301.59px;
	stroke-dashoffset: 301.59px;
	transition: all 0.8s;
	transition-timing-function: cubic-bezier(0.4, 0.08, 0, 0.97);
}

.c-play-btn__fill_transparent-black {
	fill: rgba(0, 0, 0, 0.7);
}

.btn2:hover .circle {
	stroke-dashoffset: 0;
}

.btn2small {
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.2) !important;
	display: inline-block;
	background-color: #000;
	position: absolute;
	vertical-align: middle;
	line-height: 99px;
	height: 30px;
	width: 30px;
	color: #fff;
	text-align: center;
	transition: all 0.8s;
	cursor: pointer;
	-webkit-user-select: none;
	transition-timing-function: cubic-bezier(0.4, 0.08, 0, 0.97);
	z-index: 5;
}

.btn2small:hover {
	background: rgba(0, 0, 0, 0.4);
	letter-spacing: 2px;
}

.btn2small svg {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.circlesmall {
	transform: rotate(-90deg);
	transform-origin: center;
	stroke-dasharray: 301.59px 301.59px;
	stroke-dashoffset: 301.59px;
	transition: all 0.8s;
	transition-timing-function: cubic-bezier(0.4, 0.08, 0, 0.97);
}

.c-play-btn__fill_transparent-black {
	fill: rgba(0, 0, 0, 0.7);
}

.btn2small:hover .circlesmall {
	stroke-dashoffset: 0;
}

.pop-img {
	min-width: 33px;
	min-height: 33px;
	width: 33px;
	height: 33px;
	border-radius: 50%;
}

.pop-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 50%;
}

.pop-title {
	font-size: 13px;
}

.nav {
	/* margin-top: 30px; */
	display: flex;
	z-index: 9;
	position: relative;
	justify-content: center;
	/* padding: 10px !important; */
	gap: 0px;
	height: auto;
}

.nav-items {
	cursor: pointer;
	/* color: black ; */
	transition: all 0.3s ease;
	/* width: 180px; */
	font-family: var(--font-family-secondary);
	/* height: 40px; */
	padding: 0 20px;
	display: flex;
	justify-content: center;
	font-weight: 600;
	/* background-color: yellow; */
	color: rgba(0, 0, 0, 0.35);
	align-items: center;
	height: 100%;
	padding-bottom: 0.5rem;
	inset: 0;
}

.nav-items:hover,
.nav-items:focus {
	color: black;
}

.nav-items:focus ~ .nav-items,
.nav-items:hover ~ .nav-items {
	color: rgba(0, 0, 0, 0.35);
}

.nav:not(:focus-within):not(:hover) .nav-items {
	color: black;
}

.white-nav-small {
	/* display: none; */
	/* padding-top: 30px; */
	width: 90vw;
	z-index: 2;
	position: absolute;
	transition: all 0.5s ease;
	top: 63px;
	left: 0;
	width: 100%;
	overflow: hidden;
	/* padding-top: 200px; */
	height: calc(100% + 500px);
	/* border-radius: 7px; */
	height: 0px;
	background-color: white;

	margin: 0 auto;
}

.white-nav {
	/* display: none; */
	/* padding-top: 30px; */
	width: 90vw;
	z-index: 9;
	position: absolute;
	transition: all 0.5s linear;
	top: 0;
	left: 0;
	width: 100%;
	overflow: hidden;
	/* padding-top: 200px; */
	height: calc(100% + 500px);
	/* border-radius: 7px; */
	height: 0px;
	background-color: white;
	margin: 0 auto;
	clip-path: inset(0 0 100% 0);
}

.mainsection-tag {
	width: 75% !important;
}

.featured-videos-swiper .swiper-pagination-bullet {
	transition: width 0.3s ease, border-radius 0.3s ease;
	width: 8px;
	height: 8px;
	background: #999;
	opacity: 1;
}

.featured-videos-swiper .swiper-pagination-bullet-active {
	background: #db242a;
}

.featured-videos-swiper .swiper-pagination-bullet-active.expanded {
	width: 20px !important;
	border-radius: 10px !important;
}

.highlight-maga-swiper .swiper-pagination-bullet {
	transition: width 0.3s ease, border-radius 0.3s ease;
	width: 8px;
	height: 8px;
	background: #999;
	opacity: 1;
}

.highlight-maga-swiper .swiper-pagination-bullet-active {
	background: #db242a;
}

.highlight-maga-swiper .swiper-pagination-bullet-active.expanded {
	width: 20px !important;
	border-radius: 10px !important;
}

.most-popular-swiper .swiper-pagination-bullet {
	transition: width 0.3s ease, border-radius 0.3s ease;
	width: 8px;
	height: 8px;
	background: #999;
	opacity: 1;
}

.most-popular-swiper .swiper-pagination-bullet-active {
	background: #db242a;
}

.most-popular-swiper .swiper-pagination-bullet-active.expanded {
	width: 20px !important;
	border-radius: 10px !important;
}

.awards-season-swiper .swiper-pagination-bullet {
	transition: width 0.3s ease, border-radius 0.3s ease;
	width: 8px;
	height: 8px;
	background: #999;
	opacity: 1;
}

.awards-season-swiper .swiper-pagination-bullet-active {
	background: #db242a;
}

.awards-season-swiper .swiper-pagination-bullet-active.expanded {
	width: 20px !important;
	border-radius: 10px !important;
}

.white-expand {
	height: 580px;
	/* box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px; */
}

.white-expand2 {
	height: 650px;
	/* height: auto; */
	clip-path: inset(0 0 0 0);
	z-index: 9;

	/* box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px; */
}
@media only screen and (max-width: 1300px) {
	.mbf-artboard-container .bo-right-wrapped {
		height: 25vw;
	}
	.white-expand2 {
		height: 550px;
	}
}
@media only screen and (max-width: 1024px) {
	.mbf-artboard-container .bo-right-wrapped {
		height: 25vw;
	}
	.white-expand2 {
		height: 450px;
	}
	.search {
		top: 60px;
	}
}

.white-scrolled-expand {
	height: 485px;
	/* box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px; */
}

.bottom-options {
	height: 580px;
	overflow: hidden;
	transition: all 0.4s ease;
	display: flex;
	justify-content: space-between;
	gap: 160px;
	padding-top: 170px;
	margin: auto;
	width: 60%;
}

.bottom-options2 {
	height: 100%;
	overflow: hidden;
	transition: all 0.4s ease;
	display: flex;
	justify-content: space-between;
	/* gap: 160px; */
	padding-top: 170px;
	margin: auto;
	width: 100%;
	display: flex;
	justify-content: center;
}

.MuiLinearProgress-root {
	position: relative;
	overflow: hidden;
	display: block;
	height: 4px;
	z-index: 0;
	background-color: rgb(167, 202, 237);
}

.MuiLinearProgress-bar1 {
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 0;
	top: 0;
	-webkit-transition: -webkit-transform 0.2s linear;
	transition: transform 0.2s linear;
	transform-origin: left;
	background-color: #db242a;
	width: auto;
	-webkit-animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
	animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
}

@keyframes animation-1 {
	0% {
		left: -35%;
		right: 100%;
	}

	60% {
		left: 100%;
		right: -90%;
	}

	100% {
		left: 100%;
		right: -90%;
	}
}

@keyframes animation-2 {
	0% {
		left: -200%;
		right: 100%;
	}

	60% {
		left: 107%;
		right: -8%;
	}

	100% {
		left: 107%;
		right: -8%;
	}
}

.MuiLinearProgress-bar2 {
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 0;
	top: 0;
	-webkit-transition: -webkit-transform 0.2s linear;
	transition: transform 0.2s linear;
	transform-origin: left;
	--LinearProgressBar2-barColor: #db242a;
	background-color: var(--LinearProgressBar2-barColor, currentColor);
	width: auto;
	-webkit-animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;
	animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;
}

.bo-items {
	display: flex;
	font-size: 32px;
	color: rgba(0, 0, 0, 0.15);
	align-items: center;
	font-weight: 600;
	width: 100%;
	transition: all 0.4s ease;
	cursor: pointer;
	/* margin: 25px 0; */
	height: 60px;
	display: -webkit-box;
	max-width: 100%;
	position: relative;
	-webkit-line-clamp: 2;
	/* -webkit-box-orient: vertical; */
	overflow: hidden;
}

.bo-svg {
	height: 3px;
	width: 0px;
	position: relative;
	transition: all 0.4s ease;
	margin-right: 10px;
	top: 37%;
}

.bo-left {
	width: 25%;
	/* padding-left: 7vw; */
}

.bo-left-large {
	width: 45%;
	/* padding-left: 7vw; */
}
.mbf-artboard-container {
	display: flex;
	flex-direction: column;
	justify-content: start;
	margin-bottom: 2vw;
}
.mbf-thr-wrapped-container {
	width: 85%;
	height: 40%;
	display: flex;
	flex-direction: column;
	margin: 0 auto;
	justify-content: start;
	margin-bottom: 2vw;
}

.bo-right {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	/* width: 450px; */
	width: 30%;
	height: fit-content;
	/* justify-content: flex-start; */
	justify-content: flex-end;
}

.bo-right-wrapped {
	position: relative;
	width: 100%;
	height: 50%;
}
.bo-right-wrapped video {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.bo-right-wrapped-text {
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
}
.bo-right-wrapped-text h2 {
	font-size: 2.6vw !important;
	font-weight: 500 !important;
	margin: 0;
}

.mbf-artboard-container .bo-right-wrapped {
	position: relative;
	width: 18vw;
	height: 20vw;
	margin-bottom: 10px;
	overflow: hidden;
}
.mbf-artboard-container .bo-right-wrapped img {
	object-fit: contain;
}
.mbf-artboard-container .bo-right-wrapped-text {
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
}
.mbf-artboard-container .bo-right-wrapped-text h2 {
	font-size: 2.6vw !important;
	font-weight: 500 !important;
	margin: 0;
}
.bo-right-click-text-link {
	width: 100%;
	animation-name: blink;
	animation-duration: 1s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
}

.bo-right-click-text {
	font-size: 1.4vw;
	width: 100%;
	text-align: center;
	line-height: none;
}
.bo-items:hover .bo-svg {
	width: 30px;
	height: 3px;
	background-color: black;
}

.bo-items:hover,
.bo-items:focus {
	color: black;
}

.bo-items:focus ~ .bo-items,
.bo-items:hover ~ .bo-items {
	color: rgba(0, 0, 0, 0.15);
}

.bo-left:not(:focus-within):not(:hover) .bo-items {
	color: black;
}

.home-carousal {
	position: relative;
	width: 100vw;
	position: relative;
	overflow: hidden;
	aspect-ratio: 16/9;
	/* height: 100vh; */
	/* background-color: rgba(0, 0, 0, 0.241); */
	margin-top: 0px;
}

.home-carousal img {
	width: inherit !important;
	height: auto !important;
	object-fit: cover;
	object-position: center;
}

#herosection {
	padding-bottom: 30px;
}

.read-section {
	margin: 50px 0;
	padding: 0 3vw;
}

.read-title-head {
	font-size: 50px;
	font-family: var(--font-family-primary);
}

.red {
	color: #db242a;
}

.reads {
	display: flex;
	margin-top: 30px;
	justify-content: space-between;
}

.read {
	width: 22.5vw;
	position: relative;
	/* border-top-left-radius: 15px;
    border-top-right-radius: 15px; */
	overflow: hidden;
	cursor: pointer;
}

.read-abs {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 50%;
	display: flex;
	align-items: flex-end;
	/* background-color: linear-gradiw; */
	background: rgb(0, 0, 0);
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
	padding: 20px 15px;
	transform: translateY(100%);
}

.read-p {
	color: white;
	font-size: 13px;
	line-height: 20px;
}

.read-img {
	width: 100%;
	position: relative;
	overflow: hidden;
	height: 22.5vw;
}

.read:hover .read-abs {
	transform: translateY(0);
}

.read-img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 10px;
}

.read-title {
	font-size: 22px;
	line-height: 28px;
	color: rgba(0, 0, 0, 0.8);
	/* text-align: justify; */
	margin: 20px 0;
	margin-bottom: 10px;
	/* font-weight: 600; */
	font-family: var(--font-family-primary);
}

.read-by {
	font-weight: 600;
	color: rgba(0, 0, 0, 0.4);
	font-size: 12px;
	margin: 0px 0;
}

.read:hover .read-title {
	color: #db242a;
}

.read:hover .read-by {
	color: #db242a;
}

.hl-sec {
	display: flex;
	width: 100%;
	height: 700px;
}

.hl-div {
	width: 25%;
	height: 100%;
	position: relative;
}

.hl-div img {
	width: 100%;
	transition: all 0.4s ease;
	height: 100%;
	object-fit: cover;
	filter: grayscale(1);
}

.hl-div img:hover {
	filter: grayscale(0);
}

.hl-abs {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 70%;
	background: rgb(0, 0, 0);

	transition: all 0.4s ease;
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
	/* transform: translateY(50%); */
	pointer-events: none;
	z-index: 2;
}

.hl-div:hover .hl-abs {
	/* background: none; */
	height: 50%;
}

.hl-content {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 20px 25px;
	height: 160px;
	z-index: 2;
}

.hl-title {
	color: #db242a;
	font-size: 18px;
	font-weight: 700;
	font-family: var(--font-family-secondary);
}

.hl-p {
	color: white;
	font-size: 24px;
	line-height: 1.2 !important;
	margin-top: 5px;
	font-family: var(--font-family-primary);
	line-height: 25px;
}

a {
	color: inherit;
	text-decoration: none !important;
}

.ln-cont .Home_card__lTbqS {
	box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
	box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
	margin: 20px 0;
	transition: 0.5s ease;
}

.ln-cont .Home_card__lTbqS:hover {
	transform: translateX(5px);
	cursor: pointer;
}

.image-zoom,
.image-zoom2,
.image-zoom3 {
	/* transform: scale(1.04); */
	transition: all 0.5s ease;
}

.imgcover {
	object-fit: cover;
}

.pos-rel {
	position: relative;
}

.zoom-cont {
	cursor: pointer;
}

.zoom-cont:hover .image-zoom {
	transform: scale(1.02) !important;
}

.zoom-cont:hover .image-zoom2 {
	transform: scale(1.02) !important;
}

.social-500 {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20px;
	margin-top: 20px;
}

.zoom-cont:hover .image-zoom3 {
	transform: scale(1.02) !important;
}

.w650 {
	width: 650px;
}

.zoom-cont:hover h3 {
	color: var(--primary-color);
}

.out-btn {
	position: relative !important;
	width: 95px;
	height: 38px !important;
	display: flex !important;
	border-radius: 20px !important;
	border: 1px solid black !important;
	align-items: center !important;
	justify-content: center !important;
	overflow: hidden !important;
	padding: 0 !important;
}

.out-btn4 {
	position: relative !important;
	width: 95px;
	height: 38px !important;
	display: flex !important;
	border-radius: 20px !important;
	border: 1px solid black !important;
	align-items: center !important;
	justify-content: center !important;
	overflow: hidden !important;
	color: white;
	background-color: #000;
	padding: 0 !important;
}

.topsection-subhead {
	display: flex;
	align-items: center;
	gap: 60px;
	color: white;
	text-transform: uppercase;
	border-bottom: 1px solid white;
	width: 100%;
	margin-bottom: 20px;
}

.topsection-subhead-left {
	display: flex;
	align-items: center;
	gap: 20px;
}

.topsection-subhead-right {
	display: flex;
	align-items: center;
}

.topsection-subhead-right:hover {
	color: #d92128;
}

.topsection-subhead p {
	font-family: var(--font-family-secondary) !important;
	font-size: 15px;
	font-weight: 600;
	cursor: pointer;
}

.topsection-subhead p:hover {
	color: #d92128;
}

.topsectionarrow {
	cursor: pointer;
}

.ff1 {
	font-family: var(--font-family-primary);
}

.mpp {
	font-size: 21px !important;
	padding-left: 15px !important;
}

.middle-btn {
	position: absolute;
	top: 0;
	transition: all 0.4s ease;
	left: 50%;
	height: 100%;
	width: 0%;
	transform: translateX(-50%);
	background-color: white;
	mix-blend-mode: difference;
}

.hover-fixed {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	width: 100%;
	height: 100%;
	border-radius: 20px;
	overflow: hidden;
}

.out-btn:hover .middle-btn {
	width: 100%;
}

.out-btn:hover {
	border: 1px solid black !important;
}

.out-btn2:hover .middle-btn {
	width: 100%;
}

.out-btn2:hover {
	border: 1px solid black !important;
}

.out-btn3:hover .middle-btn {
	width: 100%;
}

.out-btn3:hover {
	border: 1px solid black !important;
}

.Home_cardWrapper__M8UHq img {
	transition: all 0.4s ease;
}

.hv-cards {
	position: relative;
	cursor: pointer;
	transition: all 0.4s;
	overflow: hidden;
}

.hv-cards:hover .hv-card-abs {
	height: 50%;
}

.hv-card-abs {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	transition: all 0.4s;

	height: 80%;
	background: rgb(0, 0, 0);
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
	z-index: 1;
	color: white;
	font-family: var(--font-family-primary);
	display: flex;
	align-items: flex-end;
	font-size: 15px;
}

.h-par p {
	font-family: var(--font-family-primary);
	font-size: 16px !important;
}

.h-par .gap-2 {
	font-family: var(--font-family-secondary);
}

.card-text {
	width: 100%;
	padding: 20px 15px;
	height: 100px;
	font-size: 19px;
	line-height: 1.2;
}

.review-card {
	width: 500px;
	height: 130px;
}

.review-card:hover {
	box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
}

.review-card span {
	font-size: 15px !important;
}

.review-img-cont {
	min-width: 110px;
	height: 110px;
	margin: auto;
	overflow: hidden;
	border-radius: 7px;
	margin-left: 10px;
}

.card-subtitle-thrtv {
	color: var(--secondary-color);
	font-weight: 700;
	font-size: 12px !important;
	text-transform: uppercase;
	margin-right: 10px;
	/* display: block; */
	margin-bottom: 2px;
	font-family: var(--font-family-secondary) !important;
}

.fofp {
	font-family: var(--font-family-secondary);
}

.review-p {
	font-size: 14px !important;
	font-size: 16px !important;
	font-family: var(--font-family-primary);
}

.rn-card {
	cursor: pointer;
}

.home-cr-abs {
	position: absolute;
	/* bottom: 0; */
	top: 0;
	left: 0;
	width: 100vw;
	height: 100%;
	background: rgb(0, 0, 0);
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: flex-end;
}

.fv-title-wrapper {
	position: absolute;
	bottom: 15px;
	left: 0;
	height: 60%;
	background: rgb(0, 0, 0);
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
	z-index: 1;
	display: flex;
	justify-content: center;
	align-items: flex-end;
	opacity: 1;
	transition: all 0.1s ease-in;
}

.mob-hero-text {
	text-align: center;
}
.mob-hero-text .hcr-title {
	color: #000;
	/* text-transform: uppercase; */
}
.mob-hero-text .hcr-author {
	color: var(--secondary-color);
}
.hcr-title {
	font-size: 50px;
	font-family: var(--font-family-primary);
	width: 70vw;
	margin: 15px auto;
	line-height: 55px;
	margin-bottom: 20px;
	font-weight: 500 !important;
	color: white;
	text-align: center;
}

.hcr-p {
	color: black;
	background-color: white;
	max-width: 70vw;
	padding: 5px 65px;
	margin: auto;
	text-align: center;
	width: fit-content;
	font-weight: 600;
	border-radius: 3px;
	font-size: 20px;
	font-family: var(--font-family-secondary);
	margin-bottom: 40px;
}

.hcr-author {
	display: block;
	color: white;
	text-transform: uppercase;
	max-width: 70vw;
	margin: 15px auto;
	text-align: center;
	width: fit-content;
	font-weight: 600;
	font-size: 16px;
	font-family: var(--font-family-secondary);
	margin-bottom: 20px;
}

.hcr-img-cont {
	position: absolute;
	/* top: 0; */
	bottom: 0;
	left: 0;
	width: 100%;
	/* height: calc(100% + -31px); */
	height: 100%;
}

.pos-rel-full {
	position: relative;
	width: 100%;
	height: 100%;
}

.hcr1 {
}

.bt-open {
	padding-top: 107px;
}

.blur-nav {
	background-color: white;
	padding-bottom: 10px;
	border-radius: 7px;
	background: rgba(255, 255, 255, 1);
	box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;

	border: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-div {
	background-color: white;
}

.blur-nav .tn {
	height: 75px;
}

/* .blur-nav .tn::before {
  position: absolute;
  bottom: 0;
  width: calc(100% - 6vw);
  left: 3vw;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.3);
  content: "";
} */
.tn-logo img {
	transition: all 0.3s ease;
}

.blur-nav .tn-logo img {
	/* transform: scale(1.8); */
	/* height: 70%; */
}

.cat-video {
	font-size: 13px;
	text-transform: uppercase;
	font-weight: 700;
	color: white;
	font-family: var(--font-family-secondary);
}

.cat-pub {
	display: block;
	font-size: 13px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary);
}

.ln-count .cat-card:hover img {
	transform: scale(1.02);
	cursor: pointer;
}
.ln-count .cat-card:hover h4 {
	color: var(--primary-color);
	/* transform: scale(1.05); */
	/* cursor: pointer; */
}

.cat-card {
	display: flex;
	/* align-items: center; */
	background-color: rgba(0, 0, 0, 0.025);
	/* border-radius: 7px; */
	/* padding: 20px; */
	padding: 10px 0;
	margin-bottom: 20px;
	overflow: hidden;
	transition: 0.5s ease;
	color: inherit;
}

.cat-card .image {
	width: 250px;
	/* height: 40vw; */
	min-width: 250px;
	height: 100%;
	aspect-ratio: 16/9;
	position: relative;
	overflow: hidden;
}

.cat-card .image img {
	/* border-radius: 7px; */
	width: inherit;
	height: inherit;
	object-fit: cover;
	transition: all 0.5s ease;
}

.cat-card .content {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	/* justify-content: space-between; */
	padding: 0px 20px;
	line-height: 1;
}

.cat-card .content span.category {
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--primary-color);
	font-family: var(--font-family-secondary);
}

.cat-card .content span.timeline {
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary);
}

.cat-card .content span.author {
	font-size: 12px;
	font-weight: 700;
	margin-top: -5px;
	text-transform: uppercase;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary);
}

.cat-card .content h2 {
	font-size: 24px !important;
	font-weight: 600 !important;
	line-height: 1.2;
	text-align: left;
	display: -webkit-box;
	max-width: 100%;
	margin-bottom: 0.5rem !important;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-family: var(--font-family-primary) !important;
	transition: all 0.3s ease;
}

.cat-card .content p {
	font-size: 18px !important;
	font-weight: 300;
	line-height: 1.4;
	text-align: left;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-family: var(--font-family-secondary) !important;
	color: var(--secondary-color);
	margin-bottom: 10px;
}

.blur-nav .nav {
	margin-top: 10px;
	margin-bottom: 0px !important;
}

.footer {
	position: relative;
	bottom: 0;
	z-index: 3;
	width: 100vw;
	height: auto;
	background: rgb(0, 0, 0);
	padding: 3rem 0;
}

.footer .ft-pd {
	/* padding: 80px 50px 20px; */
}

.footer .ft-img {
	position: relative;
	width: 18vw;
	height: 20vw;
	overflow: hidden;
}

.footer .ft-img .btn-subscribe {
	position: absolute;
	bottom: 0;
	display: flex;
	margin: auto;
	width: 100%;
	align-items: center;
	justify-content: center;
	color: #fff;
	text-align: center;
	font-size: 14px;
	font-weight: 300;
	letter-spacing: 0.01rem;
	background-color: transparent;
	font-family: var(--font-family-secondary) !important;
	border: none !important;
	z-index: 3;
	opacity: 0;
	transition: all 0.4s ease;
}

.footer .ft-img .btn-subscribe:focus,
.footer .ft-img .btn-subscribe:hover {
	border: none !important;
}

.footer .ft-img img {
	object-fit: contain;
}

.footer .ft-img::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 60px;
	z-index: 2;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	margin: auto;
}

.footer .ft-img:hover .btn-subscribe {
	opacity: 1;
}

.footer .nav-item-title {
	color: #fff;
	font-size: 18px;
	font-weight: 300;
	letter-spacing: 0.01rem;
}

.footer .nav {
	margin-top: 20px;
	gap: 5px;
}

.footer .nav-sec {
	display: flex;
	align-items: flex-start;
	gap: 8vw;
	margin-top: 20px;
}

.footer .nav .nav-item .nav-link {
	font-family: var(--font-family-secondary);
	font-size: 16px;
	color: rgba(255, 255, 255, 0.5) !important;
	padding: 0px;
	text-transform: uppercase;
	transition: all 0.4s ease;
}

.footer .nav .nav-item .nav-link:hover {
	color: #fff !important;
}

.footer .ft-list {
	display: flex;
	align-items: flex-end;
	gap: 15px;
	margin-top: 4vw;
	padding-left: 1.5rem;
	transition: all 0.4s ease;
	list-style: none !important;
}

/* .footer .ft-list::before {
  position: absolute;
  bottom: 85px;
  width: calc(100% - 8vw);
  left: 4vw;
  height: 1px;
  background-color: rgba(255, 255, 255, 50%);
  content: "";
} */
.footer .ft-list .ft-item {
	padding: 5px;
}

.footer .ft-list:hover .ft-item .ft-link,
.footer .ft-list:focus .ft-item .ft-link {
	color: var(--secondary-color);
}

.footer .ft-list .ft-item:hover .ft-link,
.footer .ft-list .ft-item:focus .ft-link {
	color: #fff;
}

.footer .ft-list .ft-item .ft-link {
	display: flex;
	align-items: center;
	gap: 10px;
	color: #fff;
	font-size: 16px;
	font-weight: 300;
	letter-spacing: 0.01rem;
	font-family: var(--font-family-secondary);
	text-decoration: none;
	transition: all 0.4s ease;
}

.footer .ft-list .ft-item .ft-link .ft-icon {
	width: 30px;
	height: 15px;
	position: relative;
	overflow: hidden;
	background-color: #fff;
	border-radius: 50px;
}

.footer .ft-list .ft-item .ft-link .ft-icon img {
	object-fit: fill;
}

.footer .ft-hr {
	color: #fff;
	opacity: 0.5;
}

.sub-footer {
	background-color: rgba(0, 0, 0, 0.85);
}

.sub-footer .ft-pd {
	padding: 10px 50px 10px;
}

.sub-footer .ft-end-sec {
	display: flex;
	align-items: center;
	padding-left: 1.5rem;
}

.category-span {
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--primary-color);
	font-family: var(--font-family-secondary);
}

.author-span {
	font-size: 12px;
	font-weight: 700;
	margin-top: -5px;
	text-transform: uppercase;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary);
}

.sub-footer .ft-end-sec .ft-end-img {
	position: relative;
	width: 180px;
	min-width: 180px;
	height: 5vw;
	overflow: hidden;
}

.sub-footer .ft-end-sec .ft-end-logo {
	position: relative;
	width: 80px;
	min-width: 80px;
	height: 5vw;
	overflow: hidden;
}

.sub-footer .ft-end-sec .ft-end-logo img {
	scale: 1;
	object-fit: contain;
}

.MuiLinearProgress-root {
	background-color: rgb(220, 220, 220) !important;
	height: 2px !important;
}

.MuiBox-root {
	width: 15% !important;
}

.MuiLinearProgress-bar {
	background-color: #db242a !important;
}

.sub-footer .ft-end-sec .ft-end-img img {
	object-fit: contain;
}

.footer .ft-hr {
	position: absolute;
	right: 0;
	left: 0;
	background: #fff;
	opacity: 1;
	width: 100vw;
	height: 1px;
	opacity: 0.7;
	margin: 10px auto;
}

.sub-footer .ft-end-sec .vr {
	background: #fff;
	opacity: 1;
	width: 1px;
	height: 2.75em;
	opacity: 0.7;
	margin: auto 15px;
}

.sub-footer .ft-end-sec .hr-mob {
	background: #fff;
	width: 1px;
	height: 50px;
	opacity: 0.7;
	margin: auto 15px;
}

.embed-frame iframe {
	height: 100% !important;
}

.video-container {
	position: relative;
}

.cover-video {
	width: 900px;
}

.vid-div-main {
	max-width: 400px;
}

.right-videos-main {
	height: 655px;
	overflow-y: scroll;
}
.chart-div:hover .text--large {
	color: red;
}
.right-videos-main::-webkit-scrollbar {
	width: 0px;
	background: transparent;
}

.video-title {
	display: -webkit-box;
	/* max-width: 300px; */
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 22px !important;
	font-weight: 600 !important;
	line-height: 1.2;
	text-align: left;
	font-weight: 300;
	font-style: normal;
	color: black;
}

.sub-footer .ft-end-sec .ft-end-content p {
	color: #fff;
	font-size: 14px;
	line-height: 1.4;
	margin-bottom: 0px;
	opacity: 0.7;
	font-family: var(--font-family-secondary) !important;
}

.btn-outline-ft {
	display: flex !important;
	align-items: center;
	gap: 5px;
	width: 7vw;
	border-radius: 30px !important;
	border: 1px solid #fff !important;
	background-color: transparent !important;
	font-size: 14px !important;
	color: #fff !important;
	padding: 5px 10px !important;
	font-family: var(--font-family-secondary) !important;
	text-transform: uppercase;
	transition: all 0.4s ease;
}

.btn-outline-ft:hover,
.btn-outline-ft:active,
.show.btn-outline-ft {
	border: 1px solid #fff !important;
	background-color: #fff !important;
	color: #000 !important;
	border: 1px solid #fff !important;
}

.footer .dropdown-menu,
.sub-footer .dropdown-menu {
	font-size: 14px;
	color: #fff !important;
	font-family: var(--font-family-secondary);
}

.ft-link .dropdown-menu {
	background-color: #fff;
	color: #000;
}

.ft-dropdown-icons {
	position: relative;
	width: 20px;
	height: 20px;
	overflow: hidden;
}

.ft-dropdown-icons img {
	object-fit: contain;
}

.hv-image {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	width: 100%;
	min-width: 100%;
	height: auto;
	position: relative;
	color: #6442ac;
	/* right: 20px; */
	/* margin-bottom: 10px; */
}

.hv-line {
	display: block;
	width: 100%;
	height: 3px;
	background: linear-gradient(90deg, rgba(100, 66, 172, 1) 0%, rgba(100, 66, 172, 0) 100%);
}

.lf-image {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	width: 100%;
	min-width: 100%;
	height: auto;
	position: relative;
	color: teal;
	right: 0px;
	margin-bottom: 10px;
	gap: 20px;
}

.hv-image svg,
.lf-image svg {
	width: 30vw;
	height: 100%;
	fill: currentColor;
}

.lf-line {
	display: block;
	width: 100%;
	height: 3px;
	background: linear-gradient(90deg, rgba(0, 128, 128, 1) 0%, rgba(0, 128, 128, 0) 100%);
}

.btn-outline-ft.dropdown-toggle::after {
	margin-left: 0px !important;
}

/* Horizantal Card Css */
.card-wrapper {
	position: relative;
	width: 100%;
	overflow: hidden;
	color: inherit;
	transition: all 0.3s ease;
}

.card-wrapper:hover {
	transition: all 0.3s ease;
}

.card-wrapper:hover .content-sec h3.card-title {
	color: var(--primary-color) !important;
}

.card-wrapper:hover .feature-box img {
	transform: scale(1.02);
	transition: all 0.4s ease-in-out;
}

.card-wrapper:hover .feature-box video {
	transform: scale(1.02);
	transition: all 0.4s ease-in-out;
}

.play-btn {
	transition: all 0.3s ease;
}

.feature-box:hover .play-btn {
	opacity: 1;
}

.card-wrapper .feature-box {
	position: relative;
	cursor: pointer;
	width: 100% !important;
	/* height: 180px; */
	height: 100% !important;
	aspect-ratio: 16/9;
	margin-bottom: 0px;
	overflow: hidden;
}

.card-wrapper .feature-box img {
	width: inherit !important;
	height: inherit !important;
	object-fit: cover;
	transition: all 0.3s ease;
}

.card-wrapper .feature-box video {
	width: inherit !important;
	height: inherit !important;
	object-fit: cover;
	transition: all 0.3s ease;
}

.card-wrapper .content-sec {
	padding: 0px 0px 0px;
	text-align: left;
	transition: all 0.3s ease;
}

.card-wrapper .content-sec h3.card-title {
	color: #000;
	font-size: 26px !important;
	line-height: 1 !important;
	margin: 10px 0px !important;
	font-weight: 600;
	transition: all 0.3s ease;
}

.youtube-iframe {
	position: relative;
	width: 100%;
	aspect-ratio: 16/9;
	overflow: hidden;
}
.youtube-iframe iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.instagram-media {
	min-width: 100% !important;
}
.rsme-embed .twitter-tweet {
	margin: auto !important;
}

/* .embed-frame.embed-youtube {
  aspect-ratio: 16/9;
} */

.embed-frame.embed-instagram {
	/* aspect-ratio: 6/9; */
	aspect-ratio: 1;
}

.card-wrapper .content-sec p.card-subtitle {
	color: #000;
	font-size: 17px;
	line-height: 1.2;
	margin-bottom: 10px;
	font-family: var(--font-family-secondary) !important;
	font-weight: 300;
	letter-spacing: -0.025rem;
	font-style: normal;
}

.card-wrapper span.category {
	display: block;
	margin-bottom: 10px;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--primary-color);
	font-family: var(--font-family-secondary) !important;
}

.card-wrapper .content-sec span.author {
	display: block;
	color: var(--secondary-color);
	font-weight: 700;
	font-size: 12px;
	text-transform: uppercase;
	margin-bottom: 2px;
	font-family: var(--font-family-secondary) !important;
}

.card-wrapper .content-sec span.timeline {
	display: block;
	color: var(--secondary-color);
	text-transform: uppercase;
	font-weight: 700;
	font-size: 14px;
	margin-bottom: 2px;
	font-family: var(--font-family-secondary) !important;
}

/* Image And Content Section css */
.image-sec {
	position: relative;
	width: 100%;
	/* height: 500px; */
	height: auto;
	aspect-ratio: 16/9;
	overflow: hidden;
}

.content-sec {
	padding: 15px 0px 15px 0px;
	/* text-align: center; */
}

.content-sec h3 {
	color: #000;
	font-size: 50px;
	font-weight: 500;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	line-height: 1.1;
	margin-bottom: 10px;
	transition: all 0.3s ease;
}

.content-sec p {
	color: var(--secondary-color);
	font-size: 22px;
	line-height: 1.2;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	margin: 5px auto;
	font-family: "kepler-std", serif !important;
	font-weight: 300;
	font-style: normal;
}

.ln-cont .side-card {
	transition: 0.5s ease;
}

.side-card img {
	transition: all 0.5s ease;
}

.ln-cont .side-card:hover img {
	transform: scale(1.02);
	cursor: pointer;
}

.ln-cont .side-card:hover h3.card-title {
	color: var(--primary-color) !important;
}

.side-card {
	display: flex !important;
	flex-direction: row !important;
	align-items: flex-start !important;
	justify-content: flex-start !important;
	border-bottom: 1px dotted rgba(0, 0, 0, 1);
	/* border-bottom: none !important; */
	border-left: none !important;
	border-right: none !important;
	border-radius: 0px !important;
	/* padding: 20px 0px; */
	margin-bottom: 20px;
	overflow: hidden;
	height: fit-content;
	min-height: 122px;
	color: inherit !important;
}

.side-card.updated {
	align-items: center !important;
}

.mob-menu {
	display: none;
}

.card:last-child {
	/* border-bottom: 1px dashed rgba(0, 0, 0, 0.2)!important; */
	border-bottom: none !important;
}

.side-card .image {
	position: relative;
	width: 160px;
	min-width: 160px;
	/* height: 100px !important; */
	height: auto;
	aspect-ratio: 16/9;
	overflow: hidden;
}

.side-card .image img {
	object-fit: cover;
}

.side-card .content {
	padding: 0px 15px;
	display: flex;
	flex-direction: column;
	min-height: 80px;
	/* gap: 10px; */
	gap: 4px;
	line-height: 1;
}

.time-video {
	position: absolute;
	bottom: 12px;
	right: 5px;
	font-family: var(--font-family-secondary) !important;
	height: 23px;
	font-size: 12px;
	z-index: 2;
	background: rgba(0, 0, 0, 0.6);
	width: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 5px;
	color: white;
}

.time-video-big {
	position: absolute;
	bottom: 37px;
	right: 16px;
	font-family: var(--font-family-secondary) !important;
	height: 30px;
	font-size: 16px;
	z-index: 2;
	background: rgba(0, 0, 0, 0.6);
	width: 61px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 5px;
	color: white;
}

.views-big {
	color: var(--primary-color);
	font-weight: 700;
	font-size: 12px !important;
	text-transform: uppercase;
	margin-right: 10px;
	margin-bottom: 2px;
	font-family: var(--font-family-secondary) !important;
}

.video-timestamp {
	position: absolute;
	bottom: 10px;
	right: 8px;
	font-family: var(--font-family-secondary) !important;
	height: 23px;
	font-size: 12px;
	z-index: 2;
	background: rgba(0, 0, 0, 0.6);
	width: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 5px;
	color: white;
}

.single-video-tv {
	cursor: pointer;
}

.playbtnyt {
	position: absolute;
	bottom: 8px;
	left: 7px;
	font-size: 25px;
	color: white;
	z-index: 1;
}

.side-card .content span.category {
	display: block;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--primary-color);
	font-family: var(--font-family-secondary);
	display: -webkit-box;
	max-width: 100px;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.side-card .content span.timeline {
	display: block;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary);
}

.side-card .content h3.card-title {
	display: -webkit-box;
	/* max-width: 300px; */
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 20px !important;
	font-weight: 600 !important;
	line-height: 1;
	text-align: left;
	font-weight: 300;
	font-style: normal;
	color: inherit !important;
	transition: all 0.3s ease;
}

.tn-ham {
	display: none;
}

#cat-navtabs .navs {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 0px;
	padding: 0px 55px;
	border-bottom: 1px solid var(--primary-color);
}

#cat-navtabs .navs:not(:focus-within):not(:hover) .nav-items {
	color: black;
}

#cat-navtabs .navs .nav-items {
	position: relative;
	cursor: pointer;
	font-size: 16px;
	font-family: var(--font-family-secondary);
	padding: 20px 20px !important;
	display: flex;
	justify-content: center;
	text-transform: uppercase;
}

#cat-navtabs .navs .nav-items a {
	color: inherit;
}

#cat-navtabs .navs .nav-items.active::after {
	content: "";
	position: absolute;
	bottom: -2px;
	display: flex;
	align-items: center;
	height: 4px;
	border-radius: 7px;
	width: 100%;
	background: var(--primary-color);
	transition: all 0.3s ease;
}

#youmayalsolike .rn-card .card-wrapper {
	display: block;
	margin-bottom: 50px;
}

#top-videos,
#suggested-videos,
#shorts-videos,
#bollywood-videos {
	padding: 0px 0px 20px;
}

.flex-spacebtn {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

#suggested-videos h2,
#bollywood-videos h2 {
	margin-bottom: 0.5rem !important;
}

/* #top-videos h2{
  margin-bottom: 1.5rem !important;
} */
.yt-content {
	color: #000;
	font-size: 26px !important;
	line-height: 1 !important;
	margin-bottom: 10px !important;
	font-weight: 600 !important;
	transition: all 0.3s ease;
}

.fv-title {
	font-size: 22px;
	font-family: var(--font-family-primary);
	/* width: 70vw; */
	/* margin: 15px auto; */
	line-height: 25px;
	/* margin-bottom: 45px; */
	padding: 3px 6px;
	margin-bottom: 8px;
	color: white;
	text-align: center;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.trans-shorts {
}

.feature-video-master:hover .trans-shorts {
	bottom: 0;
}

.fv-title-views {
	font-size: 12px;
	font-family: var(--font-family-secondary);
	font-weight: 700;
	text-transform: uppercase;
	/* width: 70vw; */
	/* margin: 15px auto; */
	/* line-height: 55px; */
	/* margin-bottom: 45px; */
	padding-bottom: 5px;
	color: white;
	text-align: center;

	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.yt-wrapper {
	display: flex;
	flex-direction: column;
	/* align-items: center; */
	max-width: 330px !important;
	padding-bottom: 20px;
}

#top-videos .rn-card .card-wrapper,
#suggested-videos .rn-card .card-wrapper,
#bollywood-videos .rn-card .card-wrapper {
	margin-bottom: 20px;
}

.card-video-wrapper {
	width: fit-content;
	transition: all 0.3s ease;
	cursor: pointer;
}

.card-featured-box {
	position: relative;
	width: 100%;
	height: 400px;
	background: rgba(163, 163, 163, 0.25);
	margin-bottom: 15px;
}

.card-featured-box video {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.card-video-content {
	margin-top: 10px;
	display: flex;
	flex-direction: column;
	width: 100%;
	max-width: 100%;
}

.card-video-title {
	display: -webkit-box;
	max-width: 300px;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 22px !important;
	font-weight: 600 !important;
	line-height: 1.2;
	text-align: left;
	font-weight: 300;
	font-style: normal;
	margin: 0;
	font-family: "kepler-std-display", serif !important;
	color: #000;
}

.card-video-views {
	font-weight: 300;
	margin: 0;
	font-size: 16px;
	color: #606060 !important;
	font-family: var(--font-family-secondary) !important;
}

.div-flex-between {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 0.5rem !important;
}

.div-flex-between h2 {
	margin-bottom: 0px !important;
}

/* .secmt {
  margin-top: 3rem !important;
} */

/* .sub-footer .col-md-2 .dropdown:nth-child(2) {
  display: none;
} */

.mob-view {
	display: none !important;
}

.nmd-view {
	display: block !important;
}

.desk-view {
	display: block !important;
}

.md-view {
	display: none !important;
}

.thrhead {
	width: 100%;
	height: 68vh;
	position: relative;
	margin-bottom: 30px;
}

.coverimg-thr {
	object-fit: cover;
	object-position: top;
}

.covertitle-div {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100vw;
	height: 50%;
	background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
	z-index: 1;
	display: flex;
	justify-content: flex-start;
	align-items: flex-end;
}
.flex-all-embed {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.covertitle-thr {
	font-family: var(--font-family-primary);
	font-size: 5rem;
	color: var(--background-color);
	/* padding-inline: 20px; */
	margin: 20px 60px;
}

.Home_awardsSeason__iLjW3 .Home_cardItem__1_qTR {
	display: block !important;
}
.videoTopmargin {
	margin-top: 15px;
}
@media (max-width: 600px) {
	.bo-right-click-text {
		font-size: 5.5vw;
		padding-top: 2.5vw;
	}
	.bo-right-wrapped-text h2 {
		font-size: 6.5vw !important;
		font-weight: 500 !important;
	}
}
@media (min-width: 426px) and (max-width: 900px) {
	.side-card .image,
	.cat-card .image {
		width: 175px;
		min-width: 175px;
	}
}
@media (min-width: 601px) and (max-width: 900px) {
	.bo-right-click-text {
		font-size: 3vw;
		padding-top: 1vw;
	}
	.bo-right-wrapped-text h2 {
		font-size: 6vw !important;
		font-weight: 500 !important;
	}
}
@media screen and (min-width: 901px) {
	.videoTopmargin {
		margin-top: 100px;
	}
}
@media screen and (max-width: 375px) {
	.fofp {
		flex-direction: column;
		align-items: start !important;
		gap: 0.1rem !important;
	}

	.h-par .gap-2 {
		flex-direction: column;
		align-items: start !important;
		gap: 0.1rem !important;
	}
}

@media screen and (max-width: 600px) {
	/* .col-md-3 {
    width: 33%;
  } */
	/* .fofp {
    flex-direction: column;
    align-items: start !important;
  }
  .h-par .gap-2 {
    flex-direction: column;
    align-items: start !important;
    gap: 0.1rem !important;
  } */
}

@media only screen and (max-width: 374px) {
	/* .align-items-center {
    align-items: start !important;
  } */
	/* .h-par .gap-2 {
    flex-direction: column;
    align-items: start !important;
    gap: 0.1rem !important;
  } */
	.mainsection-tag {
		width: 100% !important;
	}
}

@media only screen and (min-width: 375px) and (max-width: 400px) {
	/* .h-par .gap-2 {
    flex-direction: column;
    align-items: start !important;
    gap: 0.1rem !important;
  } */
	.mainsection-tag {
		width: 100% !important;
	}
}

@media only screen and (min-width: 401px) and (max-width: 550px) {
	.mainsection-tag {
		width: 100% !important;
	}

	/* .h-par .gap-2 {
    flex-direction: column;
    align-items: start !important;
    gap: 0.1rem !important;
  } */
}

@media only screen and (min-width: 901px) and (max-width: 1023px) {
	/* .cat-card .image {
    height: 13vw;
  } */

	.h-par .gap-2 {
		flex-direction: column;
		align-items: start !important;
		gap: 0.1rem !important;
	}

	.card-wrapper .feature-box {
		width: 100%;
		height: 180px;
	}

	.card-wrapper .feature-box img {
		object-fit: cover !important;
	}

	.card-wrapper .feature-box video {
		object-fit: cover !important;
	}
}

@media only screen and (min-width: 901px) and (max-width: 1200px) {
	.cat-card .image {
		width: 200px !important;
		min-width: 200px !important;
	}

	.h-par .gap-2 {
		flex-direction: column;
		align-items: start !important;
		gap: 0.1rem !important;
	}
}

@media (min-width: 1200px) and (max-width: 1400px) {
	#wrapped-banner-container {
		max-width: 1140px;
	}
	#wrapped-banner-button {
		padding: 8px 1.5vw;
	}
	#wrapped-banner-button h5 {
		font-size: 1vw !important;
	}
}
@media (min-width: 992px) and (max-width: 1199px) {
	#wrapped-banner-container {
		max-width: 960px;
	}
	#wrapped-banner-button {
		padding: 8px 2vw;
	}
	#wrapped-banner-button h5 {
		font-size: 1.1vw !important;
	}
}
@media (min-width: 768px) and (max-width: 991px) {
	#wrapped-banner-container {
		max-width: 720px;
	}
	#wrapped-banner-button {
		padding: 8px 2vw;
	}
	#wrapped-banner-button h5 {
		font-size: 1.3vw !important;
	}
}
@media (max-width: 768px) {
	#herosection {
		padding-bottom: 0;
	}
}
@media (min-width: 769px) and (max-width: 1200px) {
	.reviewInnerContainer .container1 {
		width: 40%;
	}
	.reviewInnerContainer .container2 {
		width: 60%;
	}
}
@media (min-width: 576px) and (max-width: 767px) {
	#wrapped-banner-container {
		max-width: 540px;
	}

	#wrapped-banner-button {
		padding: 8px 2vw;
	}
	#wrapped-banner-button h5 {
		font-size: 1.6vw !important;
	}
}
@media (max-width: 575px) {
	#wrapped-banner-container {
		max-width: 540px;
	}

	#wrapped-banner-button {
		padding: 8.5px 4vw;
		margin-top: 1.5vw;
	}
	#wrapped-banner-button h5 {
		font-size: 12px !important;
	}
}

@media only screen and (min-width: 1200px) and (max-width: 1300px) {
	/* .cat-card .image {
    height: 10vw;
  } */

	.bottom-options {
		/* max-width: 1080px; */
	}

	.h-par .gap-2 {
		flex-direction: column;
		align-items: start !important;
		gap: 0.1rem !important;
	}
}

@media only screen and (min-width: 1301px) and (max-width: 1439px) {
	/* .cat-card .image {
    height: 9vw;
  } */

	.h-par .gap-2 {
		flex-direction: column;
		align-items: start !important;
		gap: 0.1rem !important;
	}
}

@media only screen and (min-width: 1440px) and (max-width: 1600px) {
	/* .cat-card .image {
    height: 8vw;
  } */
}

@media only screen and (min-width: 1601px) and (max-width: 1800px) {
	/* .cat-card .image {
    height: 8vw;
  } */
}

@media only screen and (min-width: 1801px) {
	/* .cat-card .image {
    height: 7vw;
  } */
}

.bor {
	display: flex;
	gap: 10px;
	cursor: pointer;
	align-items: center;
	padding: 10px 5px;
	color: rgba(0, 0, 0, 1);
	flex-direction: column;
}

.bor-img {
	background-color: white;
	position: relative;
	width: calc(0.75 * var(--global-width));
	height: calc(0.75 * var(--global-height));
}

.bor-img img {
	object-fit: cover;
	transition: all 0.4s ease;
}

.bor-text {
	font-family: var(--font-family-primary);
	font-size: 26px !important;
	line-height: 1 !important;
	margin-bottom: 10px !important;
	font-weight: 600;
	transition: all 0.3s ease;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	line-height: 1.1;
	transition: all 0.3s ease;
}

.scroll-more-text {
	display: none;
}

.btn-gap {
	gap: 1rem;
}

.st-caption-pd {
	padding: 0px 30px;
}

.nav-parent {
	display: none;
}

.mob-submenu {
	display: none;
}

.status-label {
	min-width: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	top: 60px;
	left: 75px;
	padding: 4px 8px;
	font-size: 16px;
	margin-top: 20px;
	border-radius: 3px;
}

.status-label.sl-0 {
	background-color: #fe959c;
	border: 1px solid #fe959c;
	color: #ff1014 !important;
}

.status-label.sl-1 {
	background-color: #e6f4ff;
	color: #3b82f6 !important;
}

.status-label.sl-2 {
	background-color: #fe959c;
	color: #ff1014 !important;
}

.status-label.sl-3 {
	background-color: rgb(220, 220, 220);
	color: rgb(100, 100, 100) !important;
}

.status-label.sl-4 {
	background-color: rgb(251 216 186);
	color: rgb(195 111 72) !important;
}

/* thrtv */
.thrtv-main {
	padding: 15px 30px;
}

.videos-div {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	gap: 20px;
	width: 100%;
}

.subhead-thr {
	margin-top: 48px;
}

.videos-parent-div {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30px;
}

.videos-parent-div-shorts {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
	margin-bottom: 30px;
}

.featured-video-single {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.single-video-div {
	max-width: 345px;
	width: 345px;
	cursor: pointer;
}

.thumbnail {
	position: relative !important;
	object-fit: cover;
	object-position: top;
	max-height: 194px;
	transition: all 0.3s ease;
	transform-origin: center center;
}

.thr-video-content {
	position: absolute;
	bottom: 5px;
	margin-top: 10px;
	display: flex;
	justify-content: center;
	width: 100%;
	max-width: 100%;
	color: white !important;
}

.thr-video-main-content {
	display: flex;
	width: 100%;
}

.shorts-main-div {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20px;
}

.video-logo {
	width: 35px;
	height: 35px;
	position: relative;
}

.video-logo-main {
	border-radius: 50%;
}

.feature-video-wrapper {
	width: 100%;
	height: 400px;
	margin-bottom: 15px;
}

.feature-video-master {
	/* width: fit-content; */
	transition: all 0.3s ease;
	cursor: pointer;
}

.main-desc-thr {
	font-size: 22px;
	font-weight: 600;
	font-family: var(--font-family-kepler-std) !important;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	margin: 0;
}

.main-desc-thr-shorts {
	display: -webkit-box;
	max-width: 300px;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	font-size: 22px !important;
	font-weight: 600 !important;
	color: white;
	line-height: 1.2;
	text-align: left;
	font-weight: 300;
	font-style: normal;
	margin: 0;
	color: #000;

	color: rgb(255, 255, 255);
	margin: 0px;
	opacity: 1;
	padding: 0px;
	text-overflow: ellipsis;
	text-shadow: rgba(0, 0, 0, 0.8) 0px 0px 4px;
	transition: opacity 0.3s;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	hyphens: auto;
}

.hcr-text {
	position: relative;
	transition: all 0.2s ease;
}

.feature-video-master:hover .fv-title-wrapper {
	/* opacity: 0; */
	background: transparent;
}

.feature-video-master:hover .hcr-text {
	/* bottom: -60px; */
	transform: translateY(60px);
}

.main-desc-thr-title {
	margin: 0;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--secondary-color) !important;
	font-family: var(--font-family-secondary) !important;
}

.featured-single-video-div {
	width: 20% !important;
}

.featured-single-video-div-thr {
	width: 16% !important;
}

.shorts-div {
	cursor: pointer;
}

.feature-video-row-parent {
	width: 100%;
}

.feature-video-row {
}

.single-video-div {
	transition: all 0.3s ease;
	transform-origin: center center;
}

.hover-title {
	background-color: black;
	color: white;
	padding: 2px 5px;
	position: absolute;
	bottom: -18px;
	left: 0;
	width: 100%;
	font-weight: 600;
	z-index: 9;
	visibility: hidden;
	transform-origin: center center;
}

.magazinesub {
	z-index: 5;
	position: absolute;
	bottom: 0;
	left: 35%;
	background: transparent;
	border: none;
	color: white;
	font-family: var(--font-family-secondary) !important;
	font-size: 14px;
	font-weight: 300;
	transition: all 0.2s ease-in;
	opacity: 0;
}

.desk-view:hover .magazinesub {
	opacity: 1;
}

.small-p {
	font-size: 14px;
}

.hover-title-p {
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
	margin: 0 !important;
	font-size: 16px !important;
}

.single-video-div:hover .thumbnail {
	transform: scale(1.2);
	z-index: 2;
}

.single-video-div:hover .time-video {
	display: none;
}

.single-video-div:hover .main-desc-thr {
	visibility: hidden;
}

.single-video-div:hover .main-desc-thr-title {
	visibility: hidden;
}

.single-video-div:hover .hover-title {
	visibility: visible;
	transform: scale(1.2);
}

.ad-main {
	min-width: 728px;
}

.svg-thrtv {
	width: 80px;
	height: 80px;
}

.result-page-main-div {
	padding: 100px 0px 50px;
}

.view-timeline {
	position: relative;
	top: -5px;
	font-weight: 300;
	margin: 0;
	font-size: 14px;
	color: var(--secondary-color);
	font-weight: 700;
	text-transform: uppercase;
	font-family: var(--font-family-secondary) !important;
}

.res-nav {
	display: none;
}

.embed-frame.embed-twitter {
	aspect-ratio: 10.5/9;
}

#lifestyle .content-sec {
	padding-top: 5px;
}
#lifestyle .card-title {
	margin-top: 5px !important;
}
.row-gap-30 {
	row-gap: 30px;
}
@media screen and (max-width: 425px) {
	#lifestyle .card-title {
		margin-top: 0px !important;
	}
}
@media screen and (max-width: 1497px) {
	.single-video-div {
		max-width: 329px;
		width: 345px;
		cursor: pointer;
	}

	.thumbnail {
		position: relative !important;
		object-fit: cover;
		object-position: top;
		max-height: 186px;
	}

	.search-div {
		padding: 104px 225px 50px !important;
		overflow-y: auto !important;
	}
}

@media screen and (max-width: 1399px) {
	.embed-frame.embed-twitter {
		aspect-ratio: 8.9/9;
	}
}

@media screen and (max-width: 1441px) {
	.single-video-div {
		max-width: 310px;
		width: 345px;
		cursor: pointer;
	}

	.thumbnail {
		position: relative !important;
		object-fit: cover;
		object-position: top;
		max-height: 180px;
	}
}

@media screen and (max-width: 1357px) {
	.single-video-div {
		max-width: 280px;
		width: 345px;
		cursor: pointer;
	}

	.thumbnail {
		position: relative !important;
		object-fit: cover;
		object-position: top;
		max-height: 158px;
	}
}

/* Compters and Big Screens Small Changes*/
@media screen and (min-width: 1600px) and (max-width: 1900px) {
}

/* Compters and Big Screens */
@media screen and (min-width: 1600px) {
}

@media screen and (min-width: 901px) {
	.home-cr-abs {
		bottom: 0 !important;
		height: 100%;
	}
}

/* Laptop and Tablets Small Changes*/
@media screen and (min-width: 901px) and (max-width: 1200px) {
	.embed-frame.embed-twitter {
		aspect-ratio: 7.4/9;
	}
	.ad-cont {
		transform: scale(1.3);
	}

	.home-cr-abs {
		bottom: 0;
	}
}

@media screen and (max-width: 991px) {
	.embed-frame.embed-twitter {
		aspect-ratio: 5.6/9;
	}
	.single-video-tv:nth-child(n + 6) {
		display: none !important;
	}
}

/* Tablets and Mobile */
@media screen and (max-width: 900px) {
	#wrappedSection {
		padding-bottom: 30px;
	}
	#wrapped-banner-text-container {
		width: 80%;
	}
	#wrapped-banner-text-container h2 {
		font-size: 24px !important;
	}
	.nmd-view {
		display: none !important;
	}
	.md-view {
		display: block !important;
	}
	.embed-frame.embed-twitter {
		aspect-ratio: 8.6/9;
	}
	.parent-div-filter {
		position: fixed;
		z-index: 5;
		background-color: white;
		top: 58px;
		left: 0px;
		width: 100% !important;
		/* border: 1px solid; */
		overflow: auto;
		padding-inline: 35px;
		height: calc(100% - 59px);
	}

	.sdiv:hover {
		background-color: transparent;
	}

	.result-page-main-div {
		padding: 30px 0px 50px;
	}

	.btn-filter-div {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.save-filter-btn {
		display: initial;
		margin-top: 20px;
		background-color: #000;
		color: white;
		padding: 2px 10px;
		width: 100%;
		font-family: var(--font-family-secondary) !important;
		transition: all 0.2s ease-in;
		border: 1px solid;
		height: 35px;
	}

	.sort-main-tag {
		display: none;
	}

	.save-filter-btn:hover {
		background-color: #000;
		color: white;
	}

	.showmob {
		display: none;
	}

	.dis {
		display: inherit !important;
		position: sticky;
		top: 0;
	}

	.col-md-8,
	.col-md-4 {
		width: 100%;
	}

	.res-nav {
		display: initial;
		z-index: 9;
	}

	/* .home-carousal{
    margin-top: -18px;
  } */
	.scrollable-div {
		display: flex !important;
		flex-direction: row;
		flex-wrap: nowrap !important;
		overflow-x: scroll;
		padding-bottom: 10px;
	}

	.MuiBox-root {
		width: 20% !important;
	}

	.h3_formob {
		font-size: 22px !important;
		font-weight: 600 !important;
		line-height: 1.2 !important;
		text-align: left !important;
		display: -webkit-box !important;
		max-width: 100% !important;
		-webkit-line-clamp: 2 !important;
		-webkit-box-orient: vertical !important;
		overflow: hidden !important;
		font-family: var(--font-family-primary) !important;
		margin-bottom: 0 !important;
		margin-top: 0.3rem;
	}

	.category-span {
		font-size: 12px !important;
	}

	/* .embed-frame.embed-youtube {
    aspect-ratio: 16/9;
  } */

	.embed-frame.embed-instagram {
		/* aspect-ratio: 5/7.9; */
		aspect-ratio: 4/5;
	}

	.scrollable-div::-webkit-scrollbar {
		display: none;
	}

	.svg-thrtv {
		width: 50px;
		height: 50px;
	}

	.play-btn-svg {
		left: 25px !important;
		font-size: 35px;
	}

	.hidden {
		display: initial;
	}

	.hmsb::-webkit-scrollbar {
		display: none;
	}

	.respo {
		display: none;
	}

	#cat-navtabs .navs::-webkit-scrollbar {
		display: none;
	}

	.ad-main {
		min-width: 425px;
	}

	.btn2 {
		height: 50px;
		width: 50px;
	}

	.scrollable-div .rn-card {
		/* width: 72%; */
		width: 80%;
	}

	/* .col-md-3.rn-card {
    width: 100%;
  } */
	.sdiv {
		padding: 0px;
		margin-right: 10px;
	}

	/* .hcr-img-cont {
    height: calc(100% + 5vh);
  } */

	/* .home-carousal img {
    object-position: left;
  } */

	.hcr-title {
		width: 85vw;
		font-size: 23px;
		font-weight: 600;
		line-height: 1.15;
		margin-bottom: 15px;
	}

	.hcr-author {
		max-width: 85vw;
		margin: 10px auto;
		text-align: center;
		font-weight: 600;
		font-size: 12px;
		margin-bottom: 15px;
	}

	.hcr-p {
		max-width: 95vw;
		line-height: 1.3;
		padding: 5px 13px;
		font-size: 16px;
		margin-bottom: 20px;
	}

	hr {
		margin: 1rem 0rem !important;
	}

	.scroll-more-text {
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: var(--font-family-secondary);
		font-size: 12px;
		color: rgba(0, 0, 0, 0.5);
	}

	h2 {
		text-transform: uppercase;
		font-size: 35px !important;
		/* margin-bottom: 10px !important; */
		font-weight: 600 !important;
		margin-bottom: 0px;
	}

	.heading-title {
		text-transform: uppercase;
		font-size: 35px !important;
		margin-bottom: 0px;
		font-weight: 600 !important;
	}
	.heading-title h1 {
		font-size: 35px !important;
		font-weight: 600 !important;
		margin-bottom: 0px !important;
	}

	/* .image-sec {
    height: 220px;
  } */

	.mpp {
		font-size: 20.4px !important;
		padding-left: 15px !important;
	}

	.content-sec {
		padding: 5px 5px 15px 0px;
	}

	.content-sec h3 {
		font-size: 35px;
		line-height: 1;
	}

	.content-sec p {
		font-size: 19px;
	}

	.card-list-pd {
		padding: 0px 20px 12px;
	}

	.card-wrapper .feature-box {
		height: 196px;
	}

	/* .side-card .image {
    width: 150px;
    min-width: 150px;
    height: 100px !important;
  } */

	.side-card {
		padding: 12px 0px;
		margin-bottom: 0px;
	}

	/* .side-card:first-child {
    padding-top: 0px;
    padding-bottom: 0px;
    min-height: 115px;
  } */
	.side-card:last-child {
		padding-bottom: 0px;
		border-bottom: none;
	}

	.cat-video {
		display: -webkit-box;
		max-width: 75px;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.side-card .content h3.card-title {
		font-size: 24px !important;
		line-height: 1.1;
	}

	.right-videos-main {
		height: 100%;
	}

	.hv-image {
		/* right: 15px; */
		/* margin-bottom: 15px; */
	}

	.lf-image {
		margin-bottom: 15px;
	}

	.hv-image svg,
	.lf-image svg {
		width: 250vw;
	}

	.ad-space {
		height: 80px;
		margin: 0px auto !important;
	}

	.ad-cont {
		transform: scale(1);
	}

	.ad-cont-slide {
		width: 728px;
		height: 90px;
		transform: scale(1);
	}

	.social-div-main {
		display: none;
	}

	iframe {
		width: 100%;
	}

	.btn-gap {
		gap: 0.5rem;
	}

	.awards-row {
		height: 0px !important;
	}

	.awards {
		height: 0px;
		margin: 0px;
	}

	.awards .swiper-nav {
		transform: translateY(103px);
	}

	.podcasts-row {
		height: 0px !important;
	}

	.podcasts {
		height: 0px;
		margin: 0px;
	}

	.podcasts .swiper-nav {
		transform: translateY(86px);
	}

	.hl-div {
		width: 90%;
		height: 550px;
	}

	.hl-content {
		height: 150px;
		padding: 12px 12px;
	}

	.hl-div img {
		filter: grayscale(0);
	}

	.hl-title {
		font-size: 14px;
	}

	.hl-p {
		font-size: 23px;
	}

	/* .footer .ft-pd {
    padding: 20px 20px;
  } */

	.footer .ft-img {
		width: 300px;
		height: 400px;
		margin-top: 30px;
		margin-bottom: 30px;
	}

	.footer .ft-img .btn-subscribe {
		bottom: 10px;
		opacity: 1 !important;
	}

	.footer .nav-sec {
		display: flex;
		align-items: flex-start;
		flex-wrap: wrap;
		gap: 20px 0px;
		margin-top: 20px;
	}

	.footer .col-md-4 {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.footer .nav-sec .nav-item-sec {
		width: 50%;
		padding: 0px 5px;
	}

	.footer .ft-list {
		margin-top: 40px;
		flex-wrap: wrap;
		padding-left: 0px;
		gap: 5px 10px;
		justify-content: flex-start !important;
	}

	.btn-outline-ft {
		width: 100%;
		margin-bottom: 30px;
	}

	.sub-footer .ft-pd {
		padding: 15px 15px;
	}

	.sub-footer .ft-end-sec {
		flex-direction: column;
		padding-left: 0px;
		align-items: flex-start;
	}

	.sub-footer .ft-end-sec .ft-end-img {
		width: 100%;
		min-width: unset;
		height: 50px;
	}
	.sub-footer .ft-end-sec .ft-end-logo {
		width: 100%;
		min-width: unset;
		height: 50px;
	}

	.sub-footer .ft-end-sec .vr {
		height: 1px;
		min-height: unset !important;
		width: 100%;
		margin: 15px auto !important;
	}

	.sub-footer .col-md-2 {
		justify-content: space-between !important;
	}

	.sub-footer .col-md-2 .btn-outline-ft {
		margin-top: 20px;
		margin-bottom: 20px;
		font-size: 12px !important;
	}

	#cat-navtabs .navs {
		padding: 0px 10px;
		overflow-x: scroll;
		overflow-y: hidden;
	}

	#cat-navtabs .navs .nav-items {
		padding: 15px 13px;
		white-space: nowrap;
	}

	.cat-card {
		flex-direction: column;
		padding: 0px;
		background-color: unset;
	}

	.cat-card .image {
		width: 100%;
	}

	.cat-card .content span.category {
		font-size: 14px;
	}

	.cat-card .content span.timeline {
		font-size: 14px;
	}

	.cat-card .content {
		padding: 10px 0px;
	}

	.cat-card .content h2 {
		font-size: 29px !important;
	}

	#storiesherosec .col-md-4 {
		justify-content: flex-start !important;
	}

	.st-caption-pd {
		text-align: center;
		padding: 0px 10px;
	}

	.flexcolumn-reverse {
		flex-direction: column-reverse;
	}

	.nav-menu-top {
		display: none !important;
	}

	.tn-news {
		display: none;
	}

	.tn-sub {
		display: none;
	}

	.tn-ham {
		display: block;
	}

	.tn-right .tn-ham {
		display: none;
	}

	.tn-logo img {
		transform: scale(1.3);
	}

	.blur-nav {
		padding-bottom: 0;
	}

	.tn {
		height: 60px;
		flex-direction: row-reverse;
	}

	.blur-nav .tn {
		height: 60px;
	}

	.blur-nav .tn img {
		/* transform: scale(1.8); */
	}

	.tn-right {
		padding: 0px;
	}

	.tn-right #search {
		display: none;
	}

	.nav-div {
		position: relative;
		z-index: 3;
	}

	.search-div {
		/* height: 30vh; */
		/* min-height: 220px; */

		padding: 25px 30px !important;
		padding-top: 45px !important;
		overflow-y: auto !important;
	}

	.search-input {
		margin-top: 40px;
		height: 100%;

		font-size: 20px;
		/* background-color: rebeccapurple; */
	}

	.search-textarea {
		font-size: 22px;
	}

	.tn-left {
		padding-right: 2px;
	}

	.search-close {
		top: 5%;
		right: 5%;
		width: 30px;
		font-size: 16px;
		height: 30px;
	}

	.popr {
		margin-top: 40px;
		flex-direction: column;
	}

	.tn-logo {
		height: 52px;
	}

	.pop-title {
		font-size: 14px;
		display: -webkit-box;
		max-width: 300px;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.popr-t {
		flex-direction: column;
		align-items: baseline;
		gap: 5px;
	}

	.pop-main {
		width: 100%;
	}

	.poprs {
		display: flex;
		flex-direction: column;
		width: 100%;
	}

	.mob-menu {
		display: block;
		transition: all 0.3s ease;
		border-top: 1px solid rgb(252 177 181);
		background-color: #fff;
		width: 50%;
		height: calc(100vh - 60px);
		overflow-y: scroll;
	}
	.mbf-artboard-container {
		align-items: center;
	}
	.mbf-artboard-container .bo-right-wrapped {
		width: 192px;
		height: 250px;
		margin-bottom: 0px;
	}

	.mob-down-scrolled {
		height: calc(100vh - 60px);
	}

	.first-ad {
		display: none;
	}

	.mob-submenu {
		display: block;
		border-top: 1px solid rgb(252 177 181);
		background-color: #fff;
		width: 50%;
		height: calc(100vh - 60px);
		overflow-y: scroll;
	}

	.mob-subdown-scrolled {
		height: calc(100vh - 60px);
	}

	.footer {
		padding: 0 0 20px;
	}

	.mob-item {
		height: 55px;
		width: 100%;
		background-color: white;
		border-bottom: 0.5px solid #9c9797;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0px 20px;
		font-size: 22px;
		transition: all 0.3s ease;
		font-weight: 500;
		cursor: pointer;
	}

	.mob-item svg {
		font-size: 14px;
	}

	.mi2 {
		height: 0;
		transition: all 0.3s ease;
		overflow: hidden;
	}

	.hms {
		display: flex;
		flex-direction: row;
	}

	.hmsb {
		flex-direction: row !important;
		overflow-y: scroll;
	}

	.hl-div {
		min-width: 80%;
		width: 80vw;
		height: 110vw;
	}

	.mi2-open {
		height: fit-content;
	}

	.nav-parent {
		position: fixed;
		top: 60px;
		left: 0;
		width: 200vw;
		display: flex;
		height: calc(100vh + 60px);
		transition: all 0.3s ease;
		/* background-color: red; */
		z-index: 3;
	}

	.mob-item svg {
		transition: all 0.3s ease;
	}

	.mob-open-0 svg {
		transform: rotate(180deg);
	}

	.mob-open-0 {
		color: #db242a;
	}

	.mi4 {
		display: flex;
		align-items: center;
		height: 60px;
		padding: 0 50px;
		font-size: 25px;
		background-color: white;
		border-bottom: 1px solid #dedcdc;
	}

	.mob-mbody {
		/* padding: 10px 20px; */
	}

	.mob-mbody .mbody-head {
		display: grid;
		height: 55px;
		font-size: 22px;
		font-weight: 500;
		align-items: center;
		background-image: linear-gradient(to bottom, #000 50%, #fff 50%);
		background-size: 100% 200%;
		background-position-y: 100%;
		transition: all 0.4s ease-out;
		color: #000;
		border-bottom: 1px solid #dedcdc;
		grid-template-columns: 2rem auto 2rem;
		padding: 0px 20px;
		position: sticky;
		top: 0;
		z-index: 2;
		cursor: pointer;
	}

	.mob-mbody .mbody-head.tb-head-trans {
		background-position-y: 0%;
		color: #fff;
	}

	.error-head {
		margin-inline: 10px !important;
		margin-top: 10px !important;
	}

	.not-found-div {
		margin-top: 10px !important;
	}

	.head-404 {
		padding-inline: 10px !important;
	}

	.error-p-main {
		font-size: 40px !important;
	}

	.error-p-main3 {
		font-size: 20px !important;
	}

	.error-p-main2 {
		font-size: 35px !important;
		margin-top: -13px !important;
	}

	.mob-mbody .mbody-head .title {
		text-align: center;
	}

	.mob-mbody .mbody-head svg {
		transition: all 0.3s ease;
		font-size: 14px;
	}

	.mob-mbody .m-body-main {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 22px;
		font-weight: 500;
		align-items: left;
		padding: 10px 15px;
	}

	.mob-mbody .m-body-main .title {
		display: block;
		font-size: 22px;
	}

	.text-start {
		padding-left: 5px;
	}

	.mob-mbody .m-body-main .under-line {
		display: block;
		margin: 6px 0px;
		width: 60px;
		height: 1px;
		background-color: #000;
	}

	.mbody-item {
		margin-bottom: 15px;
	}

	.mob-mbody .m-body-main ul {
		list-style: none;
		padding-left: 0px;
	}

	.mob-mbody .m-body-main ul > li:first-child {
		margin-top: 20px;
	}

	.mob-mbody .m-body-main ul > li {
		font-size: 18px;
		font-family: var(--font-family-kepler-std);
		font-weight: 300;
		margin-bottom: 10px;
	}

	.bor-img {
		border-radius: 6px;
		min-width: 65px;
		height: 65px;
	}

	.mbf {
		/* height: 65px; */
		width: 100%;
		background-color: white;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 0px 20px 20px;
		margin-top: 0px;
		margin-bottom: 30px;
		transition: all 0.3s ease;
	}

	.mob-item + .mbf,
	.mob-submenu .mbf {
		margin-top: 20px;
		margin-bottom: 5px;
	}

	.mob-follows {
		font-size: 20px;
		font-weight: 500;
		margin-bottom: 10px;
	}

	.mf-icons {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px;
	}

	.mf-icons svg {
		width: 22px;
		height: 22px;
	}

	.status-label {
		min-width: 100px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		left: 15px;
		padding: 4px 8px;
		font-size: 16px;
		margin-top: 12px;
		border-radius: 3px;
	}

	.takeover-ad {
		aspect-ratio: 300/250;
		/* height: 70vh; */
	}

	.takeover-ad img {
		object-fit: -75px 0;
	}

	.takeover-img {
		/* transform: scale(); */
	}

	.takeover-img img {
		object-fit: cover !important;
	}
	.reviewInnerContainer {
		width: calc(100% - 20px);
		margin-inline: auto;
	}
}
@media screen and (min-width: 900px) and (max-width: 990px) {
	#storiessection .col-md-8 {
		width: 100%;
	}
}
@media screen and (max-width: 767px) {
	.embed-frame.embed-twitter {
		aspect-ratio: 6.5/9;
	}
}
/* Tablets and Mobiles Small Changes */
@media screen and (min-width: 426px) and (max-width: 900px) {
	.awards .swiper-nav {
		transform: translateY(85px);
	}

	.podcasts .swiper-nav {
		transform: translateY(88px);
	}

	.card-wrapper .feature-box {
		height: 275px;
	}

	.hl-div {
		width: 90%;
		height: 600px;
	}

	.hv-image svg,
	.lf-image svg {
		width: 150vw;
	}

	.hcr-title {
		font-size: 28px !important;
	}

	/* .image-sec {
    height: 300px;
  } */

	/* .cat-card .image {
    height: 40vw;
  } */

	.status-label {
		min-width: 100px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		left: 48px;
		padding: 4px 8px;
		font-size: 16px;
		margin-top: 12px;
		border-radius: 3px;
	}
}

@media screen and (max-width: 425px) {
	h2 {
		font-size: 27px !important;
	}
	.heading-title {
		font-size: 27px !important;
	}
	.heading-title h1 {
		font-size: 27px !important;
	}

	.embed-frame.embed-instagram {
		/* aspect-ratio: 5/9; */
		aspect-ratio: 4/5;
	}

	.embed-frame.embed-twitter {
		aspect-ratio: 5.4/9;
	}
	/* iframe {
    width: 328px;
  } */

	.ad-main {
		min-width: 370px;
	}

	.content-sec h3 {
		font-size: 28px;
	}

	.content-sec p {
		font-size: 17px;
		display: none;
	}

	.card-wrapper .content-sec h3.card-title {
		font-size: 23px !important;
		font-weight: 500 !important;
	}

	.card-wrapper .content-sec p.card-subtitle {
		font-size: 15px !important;
		margin-bottom: 7px;
	}

	.card-wrapper .content-sec span.timeline {
		font-size: 12px;
		margin-bottom: 8px !important;
	}

	.card-wrapper .content-sec span.author:last-child {
		display: none;
	}

	.card-wrapper span.category {
		font-size: 12px;
		margin-bottom: 8px !important;
	}

	.mbo {
		margin-top: 20px;
	}

	.out-btn {
		width: 70px !important;
		height: 28px !important;
		font-size: 12px !important;
		border-radius: 15px !important;
	}

	.btn-swiper {
		height: 31px !important;
		width: 31px !important;
	}

	.podcasts .swiper-nav {
		transform: translateY(100px);
	}
	.side-card .image {
		width: 130px;
		min-width: 130px;
		/* margin: 12px; */
	}

	.cat-card .content h2 {
		font-size: 24px !important;
	}

	.cat-card .content p {
		display: none;
		font-size: 15px !important;
	}

	.cat-card .content span.category {
		font-size: 12px;
	}

	.cat-card .content span.timeline {
		font-size: 12px;
	}

	.side-card .content h3.card-title {
		font-size: 23px !important;
		font-weight: 500 !important;
		display: -webkit-box;
		max-width: 100%;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.footer .nav-item-title {
		font-size: 16px;
	}

	.footer .nav .nav-item .nav-link {
		font-size: 14px;
	}

	.footer .nav {
		margin-top: 10px;
	}

	.footer .ft-list {
		gap: 0px 5px;
		margin-top: 15px;
	}

	.footer .ft-list .ft-item .ft-link {
		font-size: 14px;
	}

	.sub-footer .ft-end-sec .ft-end-content p {
		font-size: 12px;
	}

	.sub-footer .ft-end-sec .vr {
		display: none;
	}

	.ft-end-content {
		margin-top: 15px;
	}

	/* .footer .ft-list .dropdown {
    display: none;
  } */
	.sub-footer .col-md-2 .dropdown:nth-child(2) {
		display: flex;
	}

	.mob-view {
		display: block !important;
	}

	.desk-view {
		display: none !important;
	}

	.mob-view p {
		margin-bottom: 10px !important;
	}

	#featuredvideo,
	#featuredpodcasts {
		padding: 15px 0px;
	}

	#featuredvideo .rn-card,
	#lifestyle .rn-card {
		margin-top: 15px !important;
	}

	#highlightmagazine {
		padding: 5px 0px 30px;
	}

	#youmayalsolike .rn-card .card-wrapper {
		margin-bottom: 20px;
	}

	#youmayalsolike .rn-card:last-child .card-wrapper {
		margin-bottom: 0px;
	}

	/* .secmt {
    margin-top: 5px !important;
  } */
	.cat-card {
		margin-bottom: 10px;
	}

	.side-card .content span.category {
		font-size: 12px;
	}

	.side-card .content span.timeline {
		font-size: 12px;
		display: none;
	}

	.btn-scrollable {
		right: 5%;
		bottom: 3.6%;
	}

	.btn-scrollable:hover {
		color: #fff;
		background-color: var(--primary-color);
		border: 1px solid var(--primary-color);
	}

	.status-label {
		min-width: 100px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		left: 15px;
		padding: 4px 8px;
		font-size: 16px;
		margin-top: 12px;
		border-radius: 3px;
	}

	.takeover-p {
		font-size: 14px;
	}

	.takeover-close {
		width: 45px;
		height: 45px;
	}

	.takeover-close svg {
		font-size: 25px;
	}
}

/* @media only screen and (max-width: 991px) {

} */

@media only screen and (min-width: 901px) and (max-width: 1199px) {
	/* .col-md-3 {
    width: 33.33%;
  } */
	.scrollable-div {
		display: flex !important;
		flex-direction: row;
		flex-wrap: nowrap !important;
		overflow-x: scroll;
		padding-bottom: 10px;
	}

	.scrollable-div::-webkit-scrollbar {
		display: none;
	}

	.col-md-3.rn-card {
		/* width: calc(0.55 * var(--global-width)); */
		/* width: min-content; */
	}

	.card-wrapper .feature-box {
		width: calc(0.55 * var(--global-width));
		height: calc(0.55 * var(--global-height));
	}
	.nav-items {
		padding: 0 10px;
	}
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
	.col-md-3.rn-card {
		/* width: calc(0.65 * var(--global-width)); */
	}

	.card-wrapper .feature-box {
		width: calc(0.65 * var(--global-width));
		height: calc(0.65 * var(--global-height));
	}
}

@media only screen and (min-width: 1400px) {
	.col-md-3.rn-card {
		/* width: calc(0.75 * var(--global-width)); */
	}

	.card-wrapper .feature-box {
		height: calc(0.75 * 229px);
	}
}

.tag-head {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-bottom: 30px;
}

.tag-head-p-small {
	font-size: 1rem;
	font-family: var(--font-family-secondary) !important;
}

.tag-head-h2 {
	font-family: var(--font-family-kepler-std) !important;
	letter-spacing: 1.5px;
	font-weight: 500 !important;
}

.search-tag {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.search-div-tag {
	width: 100%;
	border: 1px solid;
}

.input-tag {
	padding: 6.2px 0px;
	width: 100%;
	border: none;
	font-size: 18px;
	border-radius: 40px;
	font-family: var(--font-family-secondary) !important;
}

.input-tag:focus-visible {
	border: none;
	outline: none;
}

.sort-main-tag {
	margin-top: 1rem !important;
	border-right: 1px solid rgb(200, 200, 200);
	width: 25% !important;
	padding-right: 30px;
}

.sort-div2 {
	/* border-bottom: 1px solid rgb(200, 200, 200); */
	/* padding-block: 20px; */
	padding-bottom: 20px;
}

.tag-select {
	width: 100%;
	font-size: 18px;
	border: none;
	font-family: var(--font-family-secondary);
}

.tag-select-div {
	width: 100%;
	height: 40px;
	padding-inline: 5px;
	border: 1px solid black;
	display: flex;
	font-size: 18px;
	font-family: var(--font-family-secondary);
}

.tag-select:focus-visible {
	outline: none;
}

.sort-div {
	/* border-bottom: 1px solid rgb(200, 200, 200); */
	padding-block: 20px;
}

.author-sort {
	width: 100%;
	border-bottom: 1px solid rgb(220, 220, 220);
	padding-block: 20px;
}

.sort-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.sort-clear {
	font-size: 13px;
	color: rgb(120, 120, 120);
	cursor: pointer;
	font-family: var(--font-family-secondary) !important;
}

.row-sort {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.checkbox-filtername {
	display: flex;
	align-items: center;
	gap: 10px;
}

.filter-name-tag {
	font-family: var(--font-family-secondary) !important;
	font-size: 18px;
	cursor: pointer;
	margin: 0 !important;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.filter-qty-tag {
	font-family: var(--font-family-secondary) !important;
	font-size: 17px;
	color: rgb(120, 120, 120);
	margin: 0 !important;
}
.filter-item-wrapper {
	display: block;
	max-height: 290px;
	overflow: auto;
	box-sizing: border-box;
}

.paginator {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.paginator-p {
	font-family: var(--font-family-secondary) !important;
	cursor: pointer;
	font-size: 22px;
}

.paginator-active {
	color: black;
}

.paginator-sq {
	display: flex;
	align-items: center;
	gap: 40px;
	color: rgb(120, 120, 120);
	font-size: 20px;
}

.search-div-tag {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-inline: 22px;
	border-radius: 40px;
}

.react-player {
	background-color: rgba(163, 163, 163, 0.25);
}

.result-loader-div {
	width: 100%;
	margin-block: 22%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.loader-cont {
	position: fixed;
	z-index: 7;
	top: 0;
	width: 100vw;
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: white;
}

.lc {
	display: flex;
	align-items: center;
	justify-content: center;
	display: flex;
	gap: 15px;
}

.loader {
	border: 3px solid #f3f3f3;
	border-radius: 50%;
	border-top: 3px solid red;
	width: 35px;
	height: 35px;
	-webkit-animation: spin 2s linear infinite;
	/* Safari */
	animation: spin 2s linear infinite;
}

.text-lc {
	font-size: 21px;
}

/* Safari */
@-webkit-keyframes spin {
	0% {
		-webkit-transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

/* rounded animated btn */
#button-container {
	display: flex;
	flex-direction: row;
	/* justify-content: center; */
	/* align-items: center; */
	font-family: var(--font-family-secondary) !important;
}

.primary-button {
	position: relative;
	border: 1px solid var(--color);
	border-radius: 500px;
	padding: 5px 20px;
	overflow: hidden;
	background-color: transparent;
	/* text-transform: uppercase; */
	color: var(--color);
	font-size: 14px;
	font-weight: 700;
	transition: all 0.3s linear;
	letter-spacing: 0.1em;
}

.primary-button:hover {
	cursor: pointer;
	/* border: 2px solid var(--color); */
	color: #fff;
}

.primary-button .round {
	border-radius: 50%;
	background-color: var(--color);
	position: absolute;
	top: 5px;
	left: 10px;
	/* width: 40px;
  height: 40px; */
	z-index: -1;
	animation: scale-down 0.2s forwards;
}

.primary-button.animate .round {
	animation: scale-up 0.5s forwards;
}

@keyframes scale-up {
	to {
		transform: scale(350);
	}
}

@keyframes scale-down {
	from {
		transform: scale(350);
	}

	to {
		transform: scale(0);
	}
}

/* rounded animated btn */

.lrv-u-margin-tb-2 {
	margin: 8rem 20rem 6rem 20rem;
}

@media (max-width: 900px) {
	.lrv-u-margin-tb-2 {
		margin: 1rem;
	}
}

@media (min-width: 78.75rem) {
	.u-grid-gap-3\@desktop-xl {
		grid-gap: 3rem;
	}
}

@media (min-width: 62.5rem) {
	.a-cols3\@desktop {
		--cols: 3;
	}
}

@media (min-width: 78.75rem) {
	.u-grid-gap-3\@desktop-xl {
		grid-gap: 3rem;
	}
}

@media (min-width: 62.5rem) {
	.a-cols3\@desktop {
		--cols: 3;
	}
}

@media (min-width: 62.5rem) {
	.a-cols3\@desktop > .a-span2\@desktop {
		flex-basis: 66.6666666667%;
	}
}

@media (min-width: 62.5rem) {
	.a-cols3\@desktop > .a-span2\@desktop {
		flex-basis: 66.6666666667%;
	}
}

@media (min-width: 62.5rem) {
	@supports (display: grid) {
		.a-span2\@desktop {
			grid-column: span 2;
		}
	}
}

.lrv-u-line-height-small {
	line-height: 1.1;
}

.lrv-u-font-weight-normal {
	font-weight: 400;
}

.lrv-u-font-size-50 {
	font-size: 3.125rem;
}

.lrv-u-font-family-secondary {
	font-family: "kepler-std", serif;
}

.lrv-u-line-height-small {
	line-height: 1.1;
}

.lrv-u-line-height-copy {
	line-height: 1.5;
}

@media (min-width: 62.5rem) {
	.lrv-u-font-size-18\@desktop {
		font-size: 1.125rem;
	}
}

.lrv-u-font-size-16 {
	font-size: 1rem;
}

.lrv-u-font-family-body {
	font-family: "kepler-std", serif;
	font-family: var(--font-family-body, kepler-std, serif);
}

.lrv-u-line-height-copy {
	line-height: 1.5;
}

.contact-subhead {
	font-weight: 700;
	letter-spacing: -0.5px;
	font-size: 1.25rem;
	font-family: "Karla", "sans-serif";
}

.contact-p {
	font-size: 1.2rem;
	font-family: "Karla", "sans-serif" !important;
}

.contact-p2 {
	font-size: 1.2rem;
	font-family: kepler-std, serif !important;
	/* font-family: "Karla", "sans-serif" !important; */
}

.heading-masthead {
	font-family: "Karla", "sans-serif" !important;
	margin-right: 5px;
}

a {
	color: unset !important;
}

.not-found-div {
	margin-top: 130px;
}

.error-head {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	margin-top: 150px;
	height: 55vh;
	margin-inline: 250px;
}

.error-p-main {
	font-size: 100px;
	font-weight: 500;
	margin: 0;
}

.error-p-main2 {
	font-size: 50px;
	margin: 0;
	margin-top: -30px;
	font-weight: 500;
}

.error-p-main3 {
	font-size: 25px;
	text-align: center;
	margin: 0;
}

.head-404 {
	padding-inline: 108px;
	margin: 0;
	margin-top: 30px;
	margin-bottom: -25px;
}

.MuiLinearProgress-barColorPrimary {
	background-color: #db242a;
}

.tags-suggest {
	border: 1px solid;
	padding: 5px 13px;
	cursor: pointer;
	font-size: 15px;
	border-radius: 20px;
	transition: all 0.2s ease-in;
}

.tags-sugg-div {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	width: 100%;
}

.sugg-head-p {
	font-family: var(--font-family-secondary) !important;
}

.tags-suggest:hover {
	background-color: black;
	color: white;
}

/* charts */
.Chart_section {
	z-index: 1;
	background-color: #fff;
	position: relative;
}

/* .container {
  z-index: 1;
  width: 100%;
  max-width: 120em;
  margin-left: auto;
  margin-right: auto;
  position: relative;
} */
.container-padding {
	padding: 3rem 0;
}

.column-grid {
	grid-column-gap: 1em;
	grid-row-gap: 1em;
	-ms-grid-rows: auto;
	grid-template-rows: auto;
	-ms-grid-columns: 1fr 1em 1fr 1em 1.25fr;
	grid-template-columns: 1fr 1fr 1.25fr;
	grid-auto-columns: 1fr;
	width: 100%;
	display: -ms-grid;
	display: grid;
}

.column-grid > *:nth-child(1) {
	-ms-grid-row: 1;
	-ms-grid-column: 1;
}

.column-grid > *:nth-child(2) {
	-ms-grid-row: 1;
	-ms-grid-column: 3;
}

.column-grid > *:nth-child(3) {
	-ms-grid-row: 1;
	-ms-grid-column: 5;
}

.column-grid.tablet--smaller {
	-ms-grid-columns: 1fr 1fr 1.25fr;
	grid-template-columns: 1fr 1fr 1.25fr;
}

.column-grid.tablet--smaller.is--flims {
	grid-column-gap: 0em;
	grid-row-gap: 0em;
	-ms-grid-columns: 0.2fr 0em 1.5fr 0em 0.7fr 0em 0.7fr 0em 0.7fr;
	grid-template-columns: 0.2fr 1.5fr 0.7fr 0.7fr 0.7fr;
}

.flims-list__title {
	color: #757575;
	letter-spacing: -0.02em;
	text-transform: capitalize;
	font-size: 0.875em;
	line-height: 1;
	/* text-align: center; */
	/* font-size: 8vw; */
	font-family: var(--font-family-primary);
}

.flims-list__title:nth-child(2) {
	margin-left: 8.5rem;
}

.flims-list {
	width: 100%;
	margin-top: 2em;
}

.flims-item {
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch;
	width: 100%;
	-webkit-transition: opacity 0.2s;
	-o-transition: opacity 0.2s;
	transition: opacity 0.2s;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.flims-item.list--view {
	position: relative;
}

.line-w {
	background-color: var(--gray);
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	width: 100%;
	height: 1px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.line-inner {
	background-color: var(--black);
	width: 0%;
	height: 1px;
	-webkit-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	transition: all 0.3s linear;
}

.w-inline-block {
	max-width: 100%;
	display: inline-block;
}

.flims-item__inner {
	padding-top: 1em;
	padding-bottom: 1em;
	cursor: pointer;
}

.flims-item__inner .rank--text {
	/* text-align: start; */
	margin-bottom: 0 !important;
}

.login-btn {
	margin-top: 20px;
	width: 70px;
	height: 30px;
	background-color: white;
	border: 1px solid;
	border-radius: 20px;
	transition: all 0.2s ease-in;
	font-size: 16px;
	font-family: var(--font-family-secondary);
}
.login-btn:hover {
	background-color: black;
	color: white;
}

.parent-login {
	background: url(/login2.jpg) no-repeat;
	background-size: cover;
	background-position: center;
	height: 100vh;
	width: 100vw;
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
}
.thr-logo {
	height: 60px;
	margin-left: 10px;
}
.login-nav {
	position: absolute;
	top: 0;
	background-color: white;
	width: 100%;
	display: flex;
	align-items: center;
}
.res-h2 {
	color: white;
}
.login-div {
	padding: 20px 20px;
	width: 500px;
	background-color: white;
	border-radius: 20px;
}
.login-input:focus {
	outline: 0;
}
.login-input {
	width: 100%;
	padding: 5px 10px;
	font-family: var(--font-family-secondary);
}
.password-p {
	font-family: var(--font-family-secondary) !important;
}
.flims-list__title {
	text-align: center;
}

.down-arrow {
	text-align: end;
}

.column-grid.tablet--smaller.is--flims {
	grid-column-gap: 0em;
	grid-row-gap: 0em;
	-ms-grid-columns: 0.2fr 1.75fr 0.4fr 0.5fr 0.5fr 0.5fr;
	grid-template-columns: 0.2fr 1.75fr 0.4fr 0.5fr 0.5fr 0.5fr;
	text-align: center;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.column-grid.tablet--smaller.is--flims.streaming {
	-ms-grid-columns: 0.2fr 1.75fr 1fr 1fr 0.5fr;
	grid-template-columns: 0.2fr 1.75fr 1fr 1fr 0.5fr;
}
.column-grid.tablet--smaller.is--flims .flims-list__title:nth-child(2) {
	text-align: start;
}

.flims__left {
	/* justify-content: center; */
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	overflow: hidden;
	/* grid-area: span 1 / span 1 / span 1 / span 1; */
}

.flims__col--img__w {
	border-radius: 0.25em;
	width: 7.5em;
	min-width: 7.5em;
	height: 4.75em;
	margin-right: 1em;
	/* margin-right: 0em; */
	position: relative;
	overflow: hidden;
	background-color: gray;
}

.flims__col--img__w img {
	-o-object-fit: cover;
	object-fit: cover;
	-o-object-position: 50% 50%;
	object-position: 50% 50%;
	width: 100%;
	max-width: none;
	height: 100%;
	-webkit-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	transition: all 0.3s linear;
	/* -webkit-transform: translate3d(-8.5em, 0px, 0px) scale3d(1, 1, 1)
    rotateX(0deg) rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform: translate3d(-8.5em, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d; */
}

.flim--text-cntr {
	position: relative;
	text-align: start;
	line-height: 1;
	-webkit-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	transition: all 0.3s linear;
}

.text--large {
	letter-spacing: -0.02em;
	margin-top: 0;
	margin-bottom: 0;
	font-size: 3rem;
	font-weight: 500;
	font-family: var(--font-family-primary);

	/* line-height: 1; */
	/* transform: translate3d(-2em, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d; */
}

.text--large.is--caps {
	text-transform: capitalize;
}

.text--small {
	letter-spacing: -0.1em;
	margin-top: 0;
	margin-bottom: 0;
	font-size: 1rem;
	font-weight: 500;
	line-height: 1;
	color: red;
	text-transform: uppercase;
	/* font-family: "Kepler-Std Bold Semicondensed Display"; */
	font-family: var(--font-family-secondary);

	/* transform: translate3d(-11em, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform-style: preserve-3d; */
}

.text--font--size {
	font-size: 1.5rem;
	font-family: var(--font-family-secondary);
}

.subhead-charts {
	font-family: var(--font-family-secondary) !important;
	font-weight: 500 !important;
	margin: 0;
	font-size: 14px;
}

a {
	text-decoration: none;
	color: inherit;
}

/* .flims-item__inner:hover .flims__col--img__w img {
  -webkit-transform: translate3d(0em, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
  transform: translate3d(0em, 0px, 0px) scale3d(1, 1, 1) rotateX(0deg)
    rotateY(0deg) rotateZ(0deg) skew(0deg, 0deg);
} */

.flims-item__inner:hover .line-inner {
	width: 100%;
}

.is--faded {
	opacity: 0.5;
}

.font--size {
	margin: 0.2rem 0;
	font-family: var(--font-family-secondary);
	letter-spacing: -0.06em;
}

.summary img {
	border-radius: 8px;
}

.Chart--heading-cntr {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	flex-direction: column;
	margin-bottom: 3em;
	line-height: 1;
}

.Chart--heading-cntr .container-split {
	font-size: 8vw !important;
	font-weight: 400 !important;
	margin-bottom: 0 !important;
	text-transform: uppercase;
	font-family: var(--font-family-primary);
}

.data-source-heading {
	font-size: 34px !important;
	font-weight: 400 !important;
	margin-bottom: 10px !important;
	text-transform: uppercase;
	font-family: var(--font-family-primary);
}

.Chart--heading-cntr .chart--small--title {
	font-size: 3.5vw;
	font-weight: 500;
	line-height: 1.2;
	text-align: center;
	margin: 0.2rem 0;
	letter-spacing: -0.02em;
	font-family: var(--font-family-secondary) !important;
}

@media only screen and (max-width: 499px) {
	.container-padding {
		padding: 1rem;
	}

	.column-grid.tablet--smaller.is--flims {
		-ms-grid-columns: 0.2fr 1.5fr 0fr 0fr 0fr;
		grid-template-columns: 0.2fr 1.5fr 0fr 0fr 0fr;
	}

	.flims-list__title {
		color: #000;
		font-size: 5.5vw;
	}

	.flims-list__title.text-responsive {
		font-size: 3.5vw !important;
		font-family: var(--font-family-secondary);
	}

	.flims-list__title:nth-child(3) {
		display: none;
	}

	.flims-list__title:nth-child(4) {
		display: none;
	}

	.flims-list__title:nth-child(5) {
		display: none;
	}

	.flims__col--img__w {
		display: none;
	}

	.text--large {
		font-size: 5.5vw;
	}

	.text--font--size {
		display: none;
	}

	.summary {
		/* padding: 10px; */
		margin-top: 10px;
		width: 100%;
		overflow: hidden;
		/* Hide overflow */
		-webkit-transition: all 0.5s ease;
		-o-transition: all 0.5s ease;
		transition: all 0.5s ease;
		/* Smooth transition */
		min-height: 33vh;
		/* display: none; */
	}

	.summary img {
		max-width: 100%;
		margin-top: 10px;
	}

	.down-arrow {
		display: block;
		font-size: large;
	}

	.flims-list__title {
		color: #000;
	}

	.flims-list__title:nth-child(2) {
		margin-left: 0rem;
	}

	.column-grid.tablet--smaller.is--flims {
		grid-column-gap: 1rem;
	}

	.column-grid.tablet--smaller.is--flims {
		-ms-grid-columns: 0.5fr 3fr 0.5fr;
		grid-template-columns: 0.5fr 3fr 0.5fr;
	}
	.column-grid.tablet--smaller.is--flims.streaming {
		-ms-grid-columns: 0.5fr 3fr 0.5fr;
		grid-template-columns: 0.5fr 3fr 0.5fr;
	}
	.font--size {
		font-size: 1rem;
	}

	.flims-item__inner .rank--text {
		/* text-align: start; */
		font-size: 10vw;
	}

	.Chart--heading-cntr {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		flex-direction: column;
		margin-bottom: 2em;
		line-height: 1;
	}

	.Chart--heading-cntr .container-split {
		font-size: 8vw !important;
		text-transform: uppercase;
		font-family: var(--font-family-primary);
	}

	.Chart--heading-cntr .chart--small--title {
		font-size: 4.2vw;
		font-family: var(--font-family-secondary);
	}
}

@media only screen and (min-width: 500px) and (max-width: 767px) {
	.container-padding {
		padding: 3rem 1rem;
	}

	.column-grid.tablet--smaller.is--flims {
		grid-column-gap: 1rem;
		-ms-grid-columns: 0.3fr 1.5fr 0.2fr;
		grid-template-columns: 0.3fr 1.5fr 0.2fr;
	}
	.column-grid.tablet--smaller.is--flims.streaming {
		grid-column-gap: 1rem;
		-ms-grid-columns: 0.3fr 1.5fr 0.2fr;
		grid-template-columns: 0.3fr 1.5fr 0.2fr;
	}
	.flims-list__title:nth-child(3) {
		display: none;
	}

	.flims-list__title:nth-child(4) {
		display: none;
	}

	.flims-list__title:nth-child(5) {
		display: none;
	}

	.text--large {
		font-size: 4vw;
	}

	.flims-list__title {
		color: #000;
	}

	.flims-list__title:nth-child(2) {
		margin-left: 0rem;
	}

	.text--font--size {
		display: none;
	}

	.flims__col--img__w {
		display: none;
	}

	.flims-list__title {
		font-size: 5vw;
	}

	.flims-list__title.text-responsive {
		font-size: 4vw !important;
		font-family: var(--font-family-secondary);
	}

	.summary {
		/* padding: 10px; */
		margin-top: 10px;
		width: 100%;
		min-height: 30vh;
		/* display: none; */
	}

	.summary img {
		max-width: 100%;
		width: 100%;
	}

	.font--size {
		font-size: 1.5rem;
	}

	.flims-item__inner .rank--text {
		/* text-align: start; */
		font-size: 7.5vw;
	}

	.Chart--heading-cntr {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		flex-direction: column;
		margin-bottom: 5em;
		line-height: 1;
	}

	.Chart--heading-cntr .container-split {
		font-size: 6.5vw !important;
		text-transform: uppercase;
		font-family: var(--font-family-primary);
	}

	.Chart--heading-cntr .chart--small--title {
		font-size: 3.5vw;
		text-align: center;
		font-family: var(--font-family-secondary);
	}
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
	.container-padding {
		padding: 4rem 0;
	}

	.column-grid.tablet--smaller.is--flims {
		-ms-grid-columns: 0.2fr 1.5fr 0fr 0fr 0fr;
		grid-template-columns: 0.2fr 1.5fr 0.2fr;
		grid-column-gap: 1rem;
	}
	.column-grid.tablet--smaller.is--flims.streaming {
		-ms-grid-columns: 0.2fr 1.5fr 0fr 0fr 0fr;
		grid-template-columns: 0.2fr 1.5fr 0.2fr;
		grid-column-gap: 1rem;
	}

	.flims-list__title {
		color: #000;
	}

	.flims-list__title:nth-child(3) {
		display: none;
	}

	.flims-list__title:nth-child(4) {
		display: none;
	}

	.flims-list__title:nth-child(5) {
		display: none;
	}

	.text--large {
		font-size: 4vw;
	}

	.flims__col--img__w {
		display: none;
	}

	.flims-list__title:nth-child(2) {
		margin-left: 0rem;
	}

	.down-arrow {
		display: block;
		font-size: large;
	}

	.text--font--size {
		display: none;
	}

	.flims-list__title {
		font-size: 4vw;
	}

	.flims-list__title.text-responsive {
		font-size: 3.5vw !important;
		font-family: var(--font-family-secondary);
	}

	.summary {
		/* padding: 10px; */
		margin-top: 10px;
		width: 100%;
		min-height: 30vh;
		/* display: none; */
	}

	.summary img {
		max-width: 100%;
		width: 100%;
		border-radius: 10px;
	}

	.font--size {
		font-size: 1.5rem;
	}

	.flims-item__inner .rank--text {
		/* text-align: start; */
		font-size: 6vw;
	}

	.Chart--heading-cntr {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		flex-direction: column;
		margin-bottom: 5em;
		line-height: 1;
	}

	.Chart--heading-cntr .container-split {
		font-size: 5vw !important;
		text-transform: uppercase;
		font-family: var(--font-family-primary);
	}

	.Chart--heading-cntr .chart--small--title {
		font-size: 2vw;
		text-align: center;
		font-family: var(--font-family-secondary);
	}
}

@media only screen and (min-width: 992px) {
	.down-arrow {
		display: none;
	}

	.summary {
		/* padding: 10px; */
		margin-top: 10px;
		display: none;
	}

	.text--large {
		font-size: 2vw;
	}

	.flims-list__title {
		font-size: 2.5vw;
	}

	.summary {
		display: none;
	}

	.flims-item__inner .rank--text {
		font-size: 3.5vw;
	}

	.Chart--heading-cntr {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		flex-direction: column;
		margin-bottom: 5em;
		line-height: 1;
	}

	.Chart--heading-cntr .container-split {
		font-size: 5vw !important;
		text-transform: uppercase;
		font-family: var(--font-family-primary);
	}

	.Chart--heading-cntr .chart--small--title {
		font-size: 2vw;
		text-align: center;
		font-family: var(--font-family-secondary);
	}
}

@media only screen and (min-width: 1200px) {
	.flims-list__title {
		font-size: 2vw;
	}

	.flims-item__inner {
		padding-top: 1.5em;
		padding-bottom: 1em;
	}

	.flims-item__inner .rank--text {
		font-size: 3.5vw;
	}

	.Chart--heading-cntr {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		flex-direction: column;
		margin-bottom: 3em;
		line-height: 1;
	}

	.Chart--heading-cntr .container-split {
		font-size: 4vw !important;
		text-transform: uppercase;
		font-family: var(--font-family-primary);
	}

	.Chart--heading-cntr .chart--small--title {
		font-size: 1.2vw;
		text-align: center;
		font-family: var(--font-family-secondary);
	}
}

/* charts */
/* Chart Video */
.chart-vedio-wrapper {
	position: relative;
	background-color: var(--black);
	color: var(--background-color);
	padding: 5rem 0;
	overflow: hidden;
}

.chart--vedio--cntr {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	grid-gap: 20px;
}

@media only screen and (max-width: 767px) {
	.chart--vedio--cntr {
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	}
}

@media only screen and (min-width: 768px) and (max-width: 992px) {
	.chart--vedio--cntr {
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
	}
}

.youtube--vedio--cntr {
	position: relative;
	width: 100%;
	padding-top: 56.25%;
}

.youtube--vedio--cntr iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.chart--text-cntr {
	display: flex;
	flex-direction: column;
	justify-content: center;
	flex-wrap: wrap;
}

.chart--video--btn .primary-button {
	color: #fff;
	border: 1px solid var(--background-color);
	text-transform: uppercase;
}

.chart--video--btn .primary-button:hover {
	color: rgb(232, 18, 18);
	background-color: #fff;
}

@media (min-width: 62.5rem) {
	.chart--video--heading {
		letter-spacing: -0.015rem;
		font-size: 1rem;
	}
}

@media screen and (max-width: 320px) {
	.youtube--vedio--cntr iframe {
		max-width: 320px;
	}
}

/* Chart Video */
/* newsfeed */
.newsfeed {
	position: relative;
	padding: 2rem 0;
}

.newsfeed--card {
	width: 100%;
	background-color: rgba(0, 0, 0, 0.025);
	/* border: 1px solid rgba(0, 0, 0, 0.175); */
	padding: 0px;
	margin-bottom: 20px;
	overflow: hidden;
	color: inherit;
	min-height: 124px;
}

.image--cntr {
	position: relative;
	width: 250px;
	min-width: 250px;
	/* height: 160px; */
	/* height: calc(0.75* var(--global-height)); */
	overflow: hidden;
	aspect-ratio: 2;
	/* padding: 20px; */
}

.image--cntr img {
	object-fit: cover;
	object-position: top;
	width: 100%;
}

.newsfeed--content {
	/* padding: 10px 20px; */
	padding: 20px;
	text-align: left;
	min-height: 80px;
	display: flex;
	flex-direction: column;
	gap: 4px;
	line-height: 1;
}

.newsfeed--category {
	display: block;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--primary-color);
	font-family: var(--font-family-secondary);
}

.newsfeed--timeline {
	display: block;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 700;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary);
}

.newsfeed--content h3 {
	font-size: 24px !important;
	font-weight: 500 !important;
	line-height: 1;
	text-align: left;
	font-weight: 300;
	font-style: normal;
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

@media screen and (max-width: 900px) {
	.newsfeed .newsfeed--card .image--cntr {
		width: 150px;
		min-width: 150px;
		height: 100px !important;
		/* margin: 12px; */
	}
	.side-card .content {
		padding: 12px 12px 12px 0;
	}
	.side-card .image {
		margin: 12px 12px 12px 0;
	}
}

@media screen and (max-width: 767px) {
	.data-source-heading {
		font-size: 28px!important;
	}
}

@media screen and (min-width: 426px) and (max-width: 900px) {
	.newsfeed .newsfeed--card .image--cntr {
		width: 150px !important;
		min-width: 150px !important;
		height: 100px !important;
		height: calc(0.5 * var(--global-height)) !important;
	}

	.newsfeed--content h3 {
		-webkit-line-clamp: 2;
	}
}

@media screen and (min-width: 901px) and (max-width: 1200px) {
	.newsfeed .newsfeed--card .image--cntr {
		width: 200px !important;
		min-width: 200px !important;
		height: 120px !important;
		height: calc(0.55 * var(--global-height)) !important;
	}

	.newsfeed--content h3 {
		line-height: 1.2;
	}

	.newsfeed--content h3 {
		-webkit-line-clamp: 2;
	}
}

@media screen and (min-width: 1201px) {
	.newsfeed--content h3 {
		-webkit-line-clamp: 2;
	}

	.newsfeed--content h3 {
		line-height: 1.2;
	}
}
