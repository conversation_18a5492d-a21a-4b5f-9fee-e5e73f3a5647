import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Web Stories Data
export const getWebStories = async (slug) => {
	const res = await fetch(Const.Link + `api/tenant/webstories?slug=${slug}`, new Headers("GET"));
	return ProcessAPI(res);
};

export const getWebStoriesCategory = async (body) => {
	const url =
		Const.Link +
		`api/tenant/category-web-story?slug=${body.slug || ""}&limit=${body.limit || 9}&offset=${
			body.offset || 0
		}`;
	const res = await fetch(url, new Headers("GET"));
	return ProcessAPI(res);
};

// Get Web Stories Sitemap
export const getWebstoriesSitemap = async (slug) => {
	const res = await fetch(Const.Link + "api/tenant/webstories-sitemap", new Headers("GET"));
	return ProcessAPI(res);
};
