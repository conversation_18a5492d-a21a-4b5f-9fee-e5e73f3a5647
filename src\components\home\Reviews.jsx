import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import Image1 from "@/assets/images/image2.png";
import Avtar from "@/assets/images/avtar1.jpg";
import style from "./Home.module.css";
import { formatDateAndTime, getAuthorText } from "@/utils/Util";
import Button from "../common/Button";
import MediumRectangleAd from "../ads/MediumRectangleAd";
import SponsoredTag from "../common/SponsoredTag";

const Reviews = ({
  reviews,
  reviewSeeAll,
  featured_voices,
  featuredVoicesSeeAll,
}) => {
  return (
    <section id="reviews" className={`${style.reviews}`}>
      <div className="container">
        <div className="row">
          <div className="col-md-12 col-lg-7 col-xl-8">
            <div className="col-md-12 d-flex align-items-center justify-content-between AlignCenter">
              <h2>Reviews</h2>
              {/* <Link href={reviewSeeAll} className="btn btn-outline out-btn">
                <div className="middle-btn"></div>
                SEE ALL
              </Link> */}
              {/* <Link href={reviewSeeAll} id="button-container">
                <button className="primary-button">
                  SEE ALL
                  <span className="round" />
                </button>
              </Link> */}
              <Button href={reviewSeeAll}>SEE ALL</Button>
            </div>
            {reviews &&
              reviews.length > 0 &&
              reviews.map((item, i) => {
                return (
                  <Link
                    href={item?.slug ?? "#"}
                    className={`${style.card} d-flex align-items-center justify-content-start`}
                    key={`reviews-${i}`}
                  >
                    <div className={`${style.image}`}>
                      <Image
                        className="imgcover"
                        src={
                          item?.croppedImg
                            ? item?.croppedImg
                            : item?.coverImg
                            ? item?.coverImg
                            : ""
                        }
                        alt={item?.altName ?? ""}
                        fill
                      />
                    </div>
                    <div className={style.content}>
                      <div className="d-flex flex-columnn align-items-start gap-2 mb-1 fofp">
                        {item.isPromotional && (
                          <SponsoredTag
                            customStyle={{
                              marginBottom: "5px",
                              lineHeight: 1,
                              fontWeight: 700,
                            }}
                          />
                        )}
                        <span className={style.category}>
                          {item?.category ?? ""}
                        </span>
                        <span className={style.timeline}>
                          {formatDateAndTime(item?.timestamp ?? "")}
                        </span>
                      </div>
                      <h3>{item?.title ?? ""}</h3>
                      <span className={style.author}>
                        {getAuthorText("BY", item?.author, item?.contributor)}
                      </span>
                    </div>
                  </Link>
                );
              })}
          </div>
          <div className="col-md-12 col-lg-5 col-xl-4">
            <div className="col-md-12">
              <h2>Trending Stories</h2>
            </div>
            <div className={style.featuredVoices}>
              {featured_voices &&
                featured_voices.length > 0 &&
                featured_voices.map((item, i) => {
                  return (
                    <Link
                      href={item?.slug ?? "#"}
                      className={style.reviewCard}
                      key={`featured-voices-${i}`}
                    >
                      <div className={style.imageAvtar}>
                        <Image
                          className="imgcover"
                          src={
                            item?.croppedImg
                              ? item?.croppedImg
                              : item?.coverImg
                              ? item?.coverImg
                              : ""
                          }
                          alt={item?.altName ?? ""}
                          fill
                        />
                      </div>
                      <div className={style.contentSec}>
                        <span className={style.title}>
                          {item.isPromotional && (
                            <SponsoredTag
                              customStyle={{ marginBottom: "5px" }}
                            />
                          )}
                          {getAuthorText("BY", item?.author, item?.contributor)}
                        </span>
                        <h3 className={style.desc}>{item?.title ?? ""}</h3>
                      </div>
                    </Link>
                  );
                })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Reviews;
