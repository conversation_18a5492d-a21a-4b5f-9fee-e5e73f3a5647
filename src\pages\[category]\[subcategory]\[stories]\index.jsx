import { Const } from "@/utils/Constants";
import {
  getStories,
  getStoriesAuthor,
  getLatestStories,
  getYouMayAlsoLike,
  getInfiniteStories,
} from "@/pages/api/ArticleApi";
export const config = { amp: "hybrid" };
import { useAmp } from "next/amp";
import NormalStories from "@/templates/stories/NormalStories";
import AmpStories from "@/templates/stories/AmpStories";

const Stories = ({
  _id,
  data,
  author,
  breadcrumbs,
  latest,
  related,
  tag,
  meta,
  slug,
  nextStories,
}) => {
  const isAmp = useAmp();
  return isAmp ? (
    <AmpStories
      _id={_id}
      data={data}
      author={author}
      breadcrumbs={breadcrumbs}
      latest={latest}
      related={related}
      tag={tag}
      meta={meta}
      slug={slug}
      nextStories={nextStories}
    />
  ) : (
    <NormalStories
      _id={_id}
      data={data}
      author={author}
      breadcrumbs={breadcrumbs}
      latest={latest}
      related={related}
      tag={tag}
      meta={meta}
      slug={slug}
    />
  );
};

export default Stories;

export async function getServerSideProps({ params, res }) {
  const { category, subcategory, stories } = params;
  const url = `/${category}/${subcategory}/${stories}`;
  const LIMIT = 10;
  const storiesUrl = `/${stories}`;
  const latestStoriesPayload = {
    slug: url,
    limit: LIMIT,
  };
  try {
    const [storiesRes, authorRes, latestStoriesRes, youMayLikeRes] =
      await Promise.all([
        getStories(url),
        getStoriesAuthor(storiesUrl),
        getLatestStories(latestStoriesPayload),
        getYouMayAlsoLike(storiesUrl),
      ]);
    if (storiesRes?.data?.isURLCorrect === false) {
      res.writeHead(301, { Location: storiesRes?.data?.correctUrl });
      res.end();
    }
    if (
      !storiesRes ||
      storiesRes.statusCode === Const.NotFound404 ||
      Object.keys(storiesRes.data.articleData).length === 0
    ) {
      return {
        notFound: true,
      };
    }

    const { data: nextStories } = await getInfiniteStories(
      null,
      url,
      null,
      storiesRes?.data?.articleData?._id,
      15
    );
    return {
      props: {
        _id: storiesRes?.data?.articleData?._id ?? "",
        data: storiesRes?.data?.articleData?.data ?? {},
        breadcrumbs: storiesRes?.data?.articleData?.breadcrumbs ?? [],
        author:
          authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
        latest: latestStoriesRes?.data ?? [],
        related: youMayLikeRes?.data ?? [],
        tag: storiesRes?.data?.articleData?.tag ?? [],
        meta: storiesRes?.data?.articleData?.meta ?? {},
        slug: `/${category}/${subcategory}/${stories}`,
        nextStories: nextStories.data,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      notFound: true,
    };
  }
}
