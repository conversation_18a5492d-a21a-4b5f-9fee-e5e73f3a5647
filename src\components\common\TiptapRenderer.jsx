import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { NodeViewWrapper } from "@tiptap/react";
import { hasHtmlTags, getEmbedType, getTwitterUrl } from "@/utils/Util";
import style from "@/components/stories/Stories.module.css";
import { getRelatedStories } from "@/pages/api/ArticleApi";
import { Ads } from "@/components/ads/Ads";
import {
  FacebookEmbed,
  InstagramEmbed,
  XEmbed,
  YouTubeEmbed,
} from "react-social-media-embed";

const renderContent = (content, router, relatedPosts, _id) => {
  if (!content || content.length === 0) return null;
  const renderedContent = [];
  const meaningfulNodes = content.filter((node) => node.type !== "text");
  const totalNodes = meaningfulNodes.length;

  const adPositions = [
    Math.floor(totalNodes / 3),
    Math.floor((2 * totalNodes) / 3),
  ];

  let nonTextIndex = 0;

  content.forEach((node, index) => {
    let element = null;
    switch (node.type) {
      case "paragraph":
        element = (
          <p
            key={index}
            style={{
              ...(node.attrs?.textAlign && { textAlign: node.attrs.textAlign }),
            }}
          >
            {renderContent(node.content)}
          </p>
        );
        break;
      case "text":
        let textStyles = {};
        let isLink = false;
        let linkAttrs = {};

        if (node.marks) {
          node.marks.forEach((mark) => {
            if (mark.type === "textStyle") {
              textStyles.color = mark.attrs?.color || "rgb(0, 0, 0)";
            }
            if (mark.type === "bold") {
              textStyles.fontWeight = "bold";
            }
            if (mark.type === "italic") {
              textStyles.fontStyle = "italic";
            }
            if (mark.type === "link") {
              isLink = true;
              linkAttrs = mark.attrs;
            }
          });
        }
        const textElement = (
          <span key={index} style={textStyles}>
            {node.text}
          </span>
        );
        element = isLink ? (
          <Link
            key={index}
            href={linkAttrs.href}
            target={linkAttrs.target || "_blank"}
            rel={linkAttrs.rel || "noopener noreferrer"}
            style={textStyles}
          >
            {textElement}
          </Link>
        ) : (
          textElement
        );
        break;
      case "hardBreak":
        element = <br key={`hard-break-${index}`} />;
        break;
      case "horizontalRule":
        element = <hr key={`horizantalLine-${index}`} />;
        break;
      case "imageBlock":
        element = (
          <div
            key={index}
            style={{
              display: "flex",
              justifyContent: node.attrs.align,
              width: "100%",
              margin: "20px 0",
            }}
          >
            <div className={style.image} style={{ width: node.attrs.width }}>
              <Image
                src={node.attrs.src}
                alt={node.attrs.alt}
                layout="responsive"
                style={{ width: "100%", height: "100%" }}
                width={700}
                height={475}
                objectFit="cover"
              />
              <div className={style.mainCaption}>
                {node.attrs.caption && hasHtmlTags(node.attrs.caption) ? (
                  <span
                    className={style.caption}
                    dangerouslySetInnerHTML={{ __html: node.attrs.caption }}
                  />
                ) : (
                  <span className={style.caption}>{node.attrs.caption}</span>
                )}
                {node.attrs.courtesy && hasHtmlTags(node.attrs.courtesy) ? (
                  <span
                    className={style.courtesy}
                    dangerouslySetInnerHTML={{ __html: node.attrs.courtesy }}
                  />
                ) : (
                  <span className={style.courtesy}>{node.attrs.courtesy}</span>
                )}
              </div>
            </div>
          </div>
        );
        break;
      case "videoBlock":
        element = (
          <div
            key={index}
            style={{
              display: "flex",
              justifyContent: node.attrs.align,
              width: "100%",
              margin: "20px 0",
            }}
          >
            <div className={style.video} style={{ width: node.attrs.width }}>
              <video
                src={node.attrs.src}
                alt={node.attrs.alt}
                controls
                muted
                width={"100%"}
                height={"100%"}
                style={{ objectFit: "cover" }}
              />
              <div className={style.mainCaption}>
                {node.attrs.caption && hasHtmlTags(node.attrs.caption) ? (
                  <span
                    className={style.caption}
                    dangerouslySetInnerHTML={{ __html: node.attrs.caption }}
                  />
                ) : (
                  <span className={style.caption}>{node.attrs.caption}</span>
                )}
                {node.attrs.courtesy && hasHtmlTags(node.attrs.courtesy) ? (
                  <span
                    className={style.courtesy}
                    dangerouslySetInnerHTML={{ __html: node.attrs.courtesy }}
                  />
                ) : (
                  <span className={style.courtesy}>{node.attrs.courtesy}</span>
                )}
              </div>
            </div>
          </div>
        );
        break;
      case "orderedList":
        element = (
          <ol
            key={index}
            start={node?.attrs?.start || 1}
            className={style.listStyle}
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderContent(item.content)}</li>
            ))}
          </ol>
        );
        break;
      case "bulletList":
        element = (
          <ul
            key={index}
            start={node?.attrs?.start || 1}
            className={style.listStyle}
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderContent(item.content)}</li>
            ))}
          </ul>
        );
        break;
      case "heading":
        const HeadingTag = `h${node.attrs.level}`;
        element = (
          <HeadingTag
            key={index}
            style={{
              ...(node.attrs?.textAlign && { textAlign: node.attrs.textAlign }),
              fontSize:
                node.attrs.level === 1
                  ? "42px!important"
                  : node.attrs.level === 2
                  ? "34px!important"
                  : "34px!important",
            }}
          >
            {renderContent(node.content)}
          </HeadingTag>
        );
        break;
      case "embed":
        const embedType = getEmbedType(node.attrs.embed);
        element = (
          <>
            <div
              key={index}
              style={{
                width: node.attrs.width,
                margin: "20px 0",
              }}
            >
              <div
                className="flex-all-embed"
                style={{ justifyContent: node.attrs.align }}
              >
                {embedType === "youtube" && (
                  <YouTubeEmbed
                    url={node.attrs.embed}
                    width={"100%"}
                    height={"100%"}
                  />
                )}
                {embedType === "instagram" && (
                  <InstagramEmbed
                    url={node.attrs.embed.replace("embed", "")}
                    width={"100%"}
                  />
                )}
                {embedType === "twitter" && (
                  <XEmbed
                    url={getTwitterUrl(node.attrs.embed)}
                    width={"100%"}
                    style={{ maxWidth: "550px" }}
                  />
                )}
                {embedType === "facebook" && (
                  <FacebookEmbed url={node.attrs.embed} width={"100%"} />
                )}
              </div>
            </div>
            <div className={style.mainCaption}>
              {node.attrs.caption && hasHtmlTags(node.attrs.caption) ? (
                <span
                  className={style.caption}
                  dangerouslySetInnerHTML={{ __html: node.attrs.caption }}
                />
              ) : (
                <span className={style.caption}>{node.attrs.caption}</span>
              )}
              {node.attrs.courtesy && hasHtmlTags(node.attrs.courtesy) ? (
                <span
                  className={style.courtesy}
                  dangerouslySetInnerHTML={{ __html: node.attrs.courtesy }}
                />
              ) : (
                <span className={style.courtesy}>{node.attrs.courtesy}</span>
              )}
            </div>
          </>
        );
        break;
      case "relatedPosts":
        const key = node?.attrs?.blockId || node?.attrs?.postIds?.join(",");
        const posts = relatedPosts?.[key] || [];
        element = (
          <div
            key={index}
            style={{
              display: "flex",
              justifyContent: node.attrs.align,
              width: "100%",
              margin: "20px 0",
            }}
          >
            <div className={style.relatedPostWrapper}>
              <h2 className={style.relatedPostHeading}>
                {node?.attrs?.title || ""}
              </h2>
              <div
                className={style[node?.attrs?.layout ?? "grid"]}
                style={{ width: node?.attrs?.width ?? "100%" }}
              >
                {posts?.map((post, idx) => (
                  <Link
                    key={idx}
                    className={style.relatedPostCard}
                    href={`${post?.slug}`}
                    target="_blank"
                    passHref
                  >
                    <div className={style.relatedPostImage}>
                      <Image
                        className="imgcover"
                        src={post?.coverImg || ""}
                        alt={post?.altName || ""}
                        fill
                        objectFit="cover"
                      />
                    </div>
                    <div className="w-100 bg-red-100">
                      <h3 className={`${style.category} mt-0 mb-1`}>
                        {post?.category}
                      </h3>
                      <h3 className={`${style["card-title"]} mb-0`}>
                        {post?.title}
                      </h3>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        );
        break;
      default:
        break;
    }
    if (element) {
      renderedContent.push(element);
      if (node.type !== "text" && node.type !== "hardBreak") {
        if (adPositions.includes(nonTextIndex)) {
          renderedContent.push(
            <div key={`ad-${nonTextIndex}`}>
              <Ads
                style={{ marginBottom: "20px" }}
                id={`div-gpt-ad-stories-middle-${_id}-${nonTextIndex}`}
                adUnits={[
                  {
                    adUnit: "/23290324739/THRI-Desktop-Middle-300",
                    sizes: [[300, 350]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[300, 350]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.[1] || null],
                  "sub-section": [router?.[2] || null],
                }}
              />
            </div>
          );
        }
        nonTextIndex++;
      }
    }
  });
  return renderedContent;
};

// TiptapRendered component
const TiptapRendered = ({ content, router, _id }) => {
  const [relatedPosts, setRelatedPosts] = useState([]);
  useEffect(() => {
    const fetchAllRelatedPosts = async () => {
      const allRelatedPosts = {};

      const relatedPostNodes = content?.filter(
        (node) => node.type === "relatedPosts"
      );

      await Promise.all(
        relatedPostNodes.map(async (node) => {
          const key = node?.attrs?.blockId || node?.attrs?.postIds?.join(",");
          if (!key) return;

          try {
            const postIds = node.attrs.postIds;
            const payload = { _id: postIds };
            const response = await getRelatedStories(payload);

            if (response.status === "success") {
              allRelatedPosts[key] = response.data || [];
            }
          } catch (err) {
            console.error(`Error loading posts for block ${key}:`, err.message);
          }
        })
      );

      setRelatedPosts(allRelatedPosts); // object: { [key]: postData[] }
    };

    fetchAllRelatedPosts();
  }, [content]);
  return (
    <NodeViewWrapper className={style.tip}>
      {renderContent(content, router, relatedPosts, _id)}
    </NodeViewWrapper>
  );
};

export default TiptapRendered;
