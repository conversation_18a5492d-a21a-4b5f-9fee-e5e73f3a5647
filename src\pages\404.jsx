import React, { Suspense } from "react";
import Layout from "@/components/layout/Layout";
import SeoHeader from "@/components/seo/SeoHeader";
import { getCustomLatestStories } from "@/pages/api/ArticleApi";
import LatestStories from "@/components/stories/LatestStories";

const Custom404 = ({ meta, data }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <div className="not-found-div">
        <div className="error-head">
          <p className="error-p-main">404</p>
          <p className="error-p-main2">Not Found</p>
          <p className="error-p-main3">
            The page you are looking for could not be located. Please
            double-check the URL for any errors. In the meantime, feel free to
            scroll down and explore our latest stories.
          </p>
        </div>
      </div>
      <LatestStories title="Latest Stories" data={data}/>
    </Layout>
  );
};

export default Custom404;

export async function getStaticProps() {
  const metaData = {
    title:
      "The Hollywood Reporter - Movie news, TV news, awards news, lifestyle news, business news and more from The Hollywood Reporter.",
    desctiption:
      "Movie news, TV news, awards news, lifestyle news, business news and more from The Hollywood Reporter.",
    keywords:
      "Movie news, TV news, awards news, lifestyle news, business news and more from The Hollywood Reporter.",
    author: "THR",
    robots: "noindex,nofollow"
  };
  try {
    const LIMIT = 8;
    const payload = new URLSearchParams({ limit: LIMIT });
    const response = await getCustomLatestStories(payload);

    return {
      props: {
        meta: metaData ?? {},
        data: response?.data || [],
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        meta: metaData,
      },
    };
  }
}
