import React from "react";
import { formatDateToLong, formatMinutesToDuration } from "@/utils/Util";

const ReviewWidget = ({ data = {} }) => {
  const { title, summary, publishDate, cast, director, screenWriter, runtime } =
    data;

  const hasContent = title || cast || director || screenWriter || runtime;

  if (!hasContent) return null;
  return (
    <div className="reviewInnerContainer">
      <div className="container1">
        {title && <h4>{title}</h4>}
        {summary && (
          <div className="descriptionText">
            <h5>THE BOTTOM LINE</h5>
            <p>{summary}</p>
          </div>
        )}
      </div>

      <div className="container2">
        {publishDate && (
          <div className="reviewText">
            <p>
              <span className="h5">Release date:</span>
              <span>{formatDateToLong(publishDate)}</span>
            </p>
          </div>
        )}
        {cast && (
          <div className="reviewText">
            <p>
              <span className="h5">Cast:</span>
              <span>{cast}</span>
            </p>
          </div>
        )}
        {director && (
          <div className="reviewText">
            <p>
              <span className="h5">Director:</span>
              <span>{director}</span>
            </p>
          </div>
        )}
        {screenWriter && (
          <div className="reviewText">
            <p>
              <span className="h5">Screenwriter:</span>
              <span>{screenWriter}</span>
            </p>
          </div>
        )}
        {runtime && (
          <div className="reviewText">
            <p>
              <span className="h5">Duration:</span>
              <span>{formatMinutesToDuration(runtime)}</span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewWidget;
