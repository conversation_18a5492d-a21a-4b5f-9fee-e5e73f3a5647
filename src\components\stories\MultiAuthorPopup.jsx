import React from "react";
import style from "./Stories.module.css";
import Link from "next/link";
import { SlSocialFacebook } from "react-icons/sl";
import { FaInstagram } from "react-icons/fa6";
import { RiTwitterXLine, RiLinkedinLine } from "react-icons/ri";
import Outside<PERSON><PERSON><PERSON>and<PERSON> from "react-outside-click-handler";
const AuthorPopUp = ({
  index,
  name,
  subheading,
  aboutus,
  slug,
  social,
  related,
  setOpen,
  data,
}) => {
  return (
    <div className={`${style.dropdownauthor}`}>
      <OutsideClickHandler
        onOutsideClick={() => {
          setOpen(null);
        }}
      >
        <div>
          <div>
            <p className={style.authordesignation}>{data[index].aboutus}</p>

            {Object.keys(data[index].social).length > 0 && (
              <div className={style.authorpopsocial}>
                <span className={style.followp}>Follow :</span>
                {data[index].social?.instagram && (
                  <Link
                    href={`https://www.instagram.com/${data[index].social?.instagram}`}
                    target="_blank"
                  >
                    <FaInstagram className={style.sociallogo} />
                  </Link>
                )}
                {data[index].social?.facebook && (
                  <Link
                    href={`https://www.facebook.com/${data[index].social?.facebook}`}
                    target="_blank"
                  >
                    <SlSocialFacebook className={style.sociallogo} />
                  </Link>
                )}
                {data[index].social?.twitterX && (
                  <Link
                    href={`https://twitter.com/${data[index].social?.twitterX}`}
                    target="_blank"
                  >
                    <RiTwitterXLine className={style.sociallogo} />
                  </Link>
                )}
                {data[index].social?.linkedIn && (
                  <Link
                    href={`https://in.linkedin.com/${data[index].social?.linkedIn}`}
                    target="_blank"
                  >
                    <RiLinkedinLine className={style.sociallogo} />
                  </Link>
                )}
              </div>
            )}
            <span className={style.morestories}>
              Stories by:
              <span>{data[index]?.name ?? ""}</span>
            </span>
            {data && data.length > 0 && (
              <>
                {data[index].relatedStories &&
                  data[index].relatedStories.map((oo, k) => (
                    <>
                      <hr className={style.hr} />
                      <Link href={oo.slug ?? "#"} className={style.panddot}>
                        <div className={style.dotauthor}></div>
                        <p className={style.relatedstoryauthor}>{oo.title}</p>
                      </Link>
                    </>
                  ))}
              </>
            )}
          </div>
          <Link
            href={data[index].slug}
            className="btn btn-outline out-btn3"
            style={{
              marginBottom: "0px",
              fontSize: "14px",
              backgroundColor: "#fafafb",
            }}
          >
            <div className="hover-fixed">
              <div className="middle-btn"></div>
              SHOW MORE
            </div>
          </Link>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default AuthorPopUp;
