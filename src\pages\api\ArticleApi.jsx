import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Article Data
export const getStories = async (slug) => {
  const res = await fetch(
    Const.Link + `api/tenant/stories?slug=${slug}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Infinite Article Data
export const getInfiniteStories = async (
  cursor,
  slug,
  promotionalCursor,
  baseStory,
  limit
) => {
  const res = await fetch(
    Const.Link +
      `api/tenant/infinite-stories?cursor=${cursor ?? ""}&slug=${
        slug ?? ""
      }&promotionalCursor=${promotionalCursor ?? ""}&baseStory=${
        baseStory ?? ""
      }&limit=${limit ?? 2}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Related Posts Article
export const getRelatedStories = async (body) => {
  const res = await fetch(
    Const.Link + "api/tenant/related-stories",
    new Headers("POST", body)
  );
  return ProcessAPI(res);
};

// Get Article Data
export const getStoriesAuthor = async (slug) => {
  const res = await fetch(
    Const.Link + `api/tenant/stories-author-details?slug=${slug}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

export const getLatestStories = async (body) => {
  const res = await fetch(
    Const.Link +
      `api/tenant/latest-stories?slug=${body.slug}&limit=${body.limit}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

export const getCustomLatestStories = async (queryParams) => {
  const res = await fetch(
    Const.Link + `api/tenant/latest-stories-custom?${queryParams.toString()}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get View Article
export const viewArticle = async (slug) => {
  const res = await fetch(
    Const.Link + `api/tenant/view-stories?slug=${slug}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get You May Also Like
export const getYouMayAlsoLike = async (slug) => {
  const res = await fetch(
    Const.Link + `api/tenant/you-may-also-like?slug=${slug}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Post Site Map
export const getPostSitemap = async (slug) => {
  const res = await fetch(
    Const.Link + "api/tenant/post-sitemap",
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get You May Also Like
export const getNewsSitemap = async (slug) => {
  const res = await fetch(
    Const.Link + "api/tenant/news-sitemap",
    new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Image Gallery Sitemap
export const getImageGallerySitemap = async (slug) => {
  const res = await fetch(
    Const.Link + "api/tenant/image-gallery-sitemap",
    new Headers("GET")
  );
  return ProcessAPI(res);
};
