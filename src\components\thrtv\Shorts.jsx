import React, { useRef } from "react";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "@/components/common/SlideNavButton";
import "swiper/css";
import "swiper/css/navigation";

const data = [
  {
    videosrc: "./video1.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video2.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video3.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video4.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video1.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video2.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video1.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
  {
    videosrc: "./video4.mp4",
    title: "How to Make $300 in just 30 days and enjoy your rest of your life",
    views: "34k",
  },
];

const Shorts = () => {
  const videoRefs = useRef([]);

  const handleMouseEnter = (index) => {
    const video = videoRefs.current[index];
    if (video && video.play) {
      video.play();
    }
  };

  const handleMouseLeave = (index) => {
    const video = videoRefs.current[index];
    if (video && video.pause) {
      video.pause();
      video.currentTime = 0;
    }
  };

  return (
    <section id="shorts-videos">
      <div className="container">
        <div className="row">
          <div className="col-md-12 div-flex-between">
            <h2>Shorts</h2>
            <div>
              <SlideNavButton prev={"shortPrev"} next={"shortNext"} />
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-md-12">
            <Swiper
              autoplay={{
                delay: 2500,
                disableOnInteraction: false,
              }}
              pagination={{
                clickable: true,
              }}
              modules={[Navigation, Pagination, Keyboard]}
              spaceBetween={20}
              slidesPerView={5}
              navigation={{
                prevEl: ".shortPrev",
                nextEl: ".shortNext",
              }}
              loop={true}
              breakpoints={{
                320: {
                  slidesPerView: 1,
                  spaceBetween: 10,
                },
                480: {
                  slidesPerView: 2,
                  spaceBetween: 10,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 15,
                },
                1024: {
                  slidesPerView: 5,
                  spaceBetween: 20,
                },
              }}
            >
              {data.map((item, i) => (
                <SwiperSlide key={`shorts-${i}`}>
                  <div
                    className="feature-video-master"
                    onMouseEnter={() => handleMouseEnter(i)}
                    onMouseLeave={() => handleMouseLeave(i)}
                  >
                    <div
                      className="card-featured-box"
                      style={{ borderRadius: "10px", position: "relative" }}
                    >
                      <video
                        ref={(el) => (videoRefs.current[i] = el)}
                        src={item.videosrc}
                        loop
                        muted
                        playsInline
                      />
                    </div>
                    <div className="fv-title-wrapper">
                      <div className="hcr-text">
                        <div className="fv-title">{item.title}</div>
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Shorts;
