import Link from "next/link";
import React, { useEffect, useRef } from "react";

const Button = ({ children, href = "#", onClick }) => {
  // Default href to '#'
  const primary = useRef(null);
  const round = useRef(null);

  useEffect(() => {
    const enterHandler = (event) => {
      primary.current.classList.add("animate");

      const buttonX = event.offsetX;
      const buttonY = event.offsetY;

      round.current.style.left = `${buttonX}px`;
      round.current.style.width = "1px";
      round.current.style.height = "1px";

      if (buttonY < 24) {
        round.current.style.top = "0px";
      } else if (buttonY > 30) {
        round.current.style.top = "48px";
      }
    };

    const leaveHandler = (event) => {
      primary.current.classList.remove("animate");

      const buttonX = event.offsetX;
      const buttonY = event.offsetY;

      round.current.style.left = `${buttonX}px`;

      if (buttonY < 24) {
        round.current.style.top = "0px";
      } else if (buttonY > 30) {
        round.current.style.top = "48px";
      }
    };

    const primaryButton = primary.current;

    primaryButton.addEventListener("mouseenter", enterHandler);
    primaryButton.addEventListener("mouseleave", leaveHandler);

    // Cleanup event listeners on unmount
    return () => {
      primaryButton.removeEventListener("mouseenter", enterHandler);
      primaryButton.removeEventListener("mouseleave", leaveHandler);
    };
  }, []);

  return (
    <Link href={href} id="button-container">
      <div onClick={onClick} ref={primary} className="primary-button">
        {children}
        <span ref={round} className="round" />
      </div>
    </Link>
  );
};

export default Button;
