import React from "react";
import style from "./Stories.module.css";
import Link from "next/link";
import { SlSocialFacebook } from "react-icons/sl";
import { FaInstagram } from "react-icons/fa6";
import { RiTwitterXLine, RiLinkedinLine } from "react-icons/ri";
import Outside<PERSON><PERSON><PERSON>and<PERSON> from "react-outside-click-handler";
import Button from "../common/Button";
const AuthorPopUp = ({
  index,
  name,
  subheading,
  aboutus,
  slug,
  social,
  related,
  setOpen,
}) => {
  return (
    <div className={`${style.dropdownauthor}`}>
      <OutsideClickHandler
        onOutsideClick={() => {
          setOpen(null);
        }}
      >
        <div>
          <div>
            <p className={style.authordesignation}>{aboutus ?? ""}</p>
            {(social?.instagram ||
              social?.facebook ||
              social?.twitterX ||
              social?.linkedIn) && (
              <div className={style.authorpopsocial}>
                <span className={style.followp}>Follow :</span>
                {social?.instagram && (
                  <Link
                    href={`https://www.instagram.com/${social?.instagram}`}
                    target="_blank"
                  >
                    <FaInstagram className={style.sociallogo} />
                  </Link>
                )}
                {social?.facebook && (
                  <Link
                    href={`https://www.facebook.com/${social?.facebook}`}
                    target="_blank"
                  >
                    <SlSocialFacebook className={style.sociallogo} />
                  </Link>
                )}
                {social?.twitterX && (
                  <Link
                    href={`https://twitter.com/${social?.twitterX}`}
                    target="_blank"
                  >
                    <RiTwitterXLine className={style.sociallogo} />
                  </Link>
                )}
                {social?.linkedIn && (
                  <Link
                    href={`https://in.linkedin.com/${social?.linkedIn}`}
                    target="_blank"
                  >
                    <RiLinkedinLine className={style.sociallogo} />
                  </Link>
                )}
              </div>
            )}

            <span className={style.morestories}>Stories by {name} :</span>
            {related && related.length > 0 && (
              <>
                {related.map((item, i) => {
                  return (
                    <>
                      <hr className={style.hr} />
                      <Link href={item.slug ?? "#"} className={style.panddot}>
                        <div className={style.dotauthor}></div>
                        <p className={style.relatedstoryauthor}>{item.title}</p>
                      </Link>
                    </>
                  );
                })}
              </>
            )}
          </div>
          {/* <Link
            href={slug}
            className="btn btn-outline out-btn3"
            style={{
              marginBottom: "0px",
              fontSize: "14px",
              backgroundColor: "#fafafb",
            }}
          >
            <div className="hover-fixed">
              <div className="middle-btn"></div>
              SHOW MORE
            </div>
          </Link> */}
          {/* <Link href={slug} className={style.buttonContainer}>
            <button className={style.primaryButton}>
              SHOW MORE
              <span className={style.round} />
            </button>
          </Link> */}
          <hr className="Stories_hr"></hr>
          <Button href={slug}>SHOW MORE</Button>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default AuthorPopUp;
