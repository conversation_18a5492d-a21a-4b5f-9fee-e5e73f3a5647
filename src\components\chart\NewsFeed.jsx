import React from "react";
import Image from "next/image";
import Link from "next/link";
import { formatDateAndTime } from "@/utils/Util";

const NewsFeed = ({ title, data }) => {
  if(data.length===0){
    return null;
  }
  return (
    <section id="newsfeed" className="newsfeed">
      <div className="container">
        <div className="row">
          <div className="col-md-2"></div>
          <div className="col-md-8">
            <div className="col-md-12 d-flex align-items-center justify-content-center AlignCenter">
              <h2>{title}</h2>
            </div>
            {data &&
              data.length > 0 &&
              data.map((item, i) => {
                return (
                  <>
                    <Link
                      href={item?.slug ?? "#"}
                      className="newsfeed--card d-flex align-items-start justify-content-start"
                      key={`news-feed-${i}`}
                    >
                      <div className="image--cntr">
                        <Image
                          src={item?.coverImg ?? ""}
                          fill
                          className="imgcover"
                          alt={item?.altName ?? ""}
                        />
                      </div>
                      <div className="newsfeed--content">
                        <div className="d-flex flex-columnn align-items-start gap-2 mb-1 fofp">
                          <span className="newsfeed--category">
                            {item?.category ?? ""}
                          </span>
                          <span className="newsfeed--timeline">
                            {formatDateAndTime(item?.timestamp ?? "")}
                          </span>
                        </div>
                        <h3>{item?.title ?? ""}</h3>
                      </div>
                    </Link>
                  </>
                );
              })}
          </div>
          <div className="col-md-2"></div>
        </div>
      </div>
    </section>
  );
};

export default NewsFeed;
