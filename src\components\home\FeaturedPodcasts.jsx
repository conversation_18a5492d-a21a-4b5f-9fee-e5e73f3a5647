import React from "react";
import Link from "next/link";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "../common/SlideNavButton";
import style from "./Home.module.css";
import Image from "next/image";

const FeaturedPodcasts = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <section id="featuredpodcasts" className={style.featuredPodcasts}>
          <div className="container">
            <div className="row podcasts-row desk-view">
              <div className="col-md-12 d-flex align-items-start justify-content-end mb-4 podcasts">
                <SlideNavButton prev={"fpPrev"} next={"fpNext"} />
              </div>
            </div>
            <div className="row d-flex align-items-start">
              <div className="col-md-12 mob-view">
                <div className="d-flex align-items-center justify-content-between">
                  <h2>Featured Podcasts</h2>
                  <Link href="" className="btn btn-outline out-btn">
                    SEE ALL
                    <div className="middle-btn"></div>
                  </Link>
                </div>
                <p>
                  All the awards show coverage leading up to the 2024 Oscars
                </p>
              </div>
              <div className="col-md-2 desk-view">
                <h2>Featured Podcasts</h2>
                <p>
                  All the news from the Berlinale and European Film Market 2024
                </p>
                <Link href="" className="btn btn-outline out-btn">
                  <div className="middle-btn"></div>
                  SEE ALL
                </Link>
              </div>
              <div className="col-md-10 desk-view">
                <Swiper
                  autoplay={{
                    delay: 2500,
                    disableOnInteraction: false,
                  }}
                  //   pagination={{
                  //     clickable: true,
                  //   }}
                  modules={[Navigation, Pagination, Keyboard]}
                  navigation={{
                    prevEl: ".fpPrev",
                    nextEl: ".fpNext",
                  }}
                  // navigation={true}
                  loop={true}
                  spaceBetween={15}
                  slidesPerView={1}
                  breakpoints={{
                    425: { slidesPerView: 1 },
                    600: { slidesPerView: 1 },
                    1200: { slidesPerView: 4 },
                  }}
                >
                  {data.map((item, i) => {
                    return (
                      <>
                        <SwiperSlide key={`podcasts-${i}`}>
                          <div className={style.cardItem}>
                            <Link
                              className={style.cardWrapper}
                              href={item?.slug ?? "#"}
                            >
                              <div className={style.featureBox}>
                                <Image
                                  src={
                                    item?.croppedImg
                                      ? item?.croppedImg
                                      : item?.coverImg
                                      ? item?.coverImg
                                      : ""
                                  }
                                  alt={item?.altName ?? ""}
                                  fill
                                />
                              </div>
                              <h3>{item?.title ?? ""}</h3>
                            </Link>
                          </div>
                        </SwiperSlide>
                      </>
                    );
                  })}
                </Swiper>
              </div>
              <div className="col-md-12 mob-view">
                <div className="row scrollable-div">
                  {data.map((item, i) => {
                    return (
                      <>
                        <div
                          className="col-md-3 rn-card"
                          key={`podcasts-mob-${i}`}
                        >
                          <div className={style.cardItem}>
                            <Link
                              className={style.cardWrapper}
                              href={item?.slug ?? "#"}
                            >
                              <div className={style.featureBox}>
                                <Image
                                  src={
                                    item?.croppedImg
                                      ? item?.croppedImg
                                      : item?.coverImg
                                      ? item?.coverImg
                                      : ""
                                  }
                                  alt={item?.altName ?? ""}
                                  fill
                                />
                              </div>
                              <h3>{item?.title ?? ""}</h3>
                            </Link>
                          </div>
                        </div>
                      </>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default FeaturedPodcasts;
