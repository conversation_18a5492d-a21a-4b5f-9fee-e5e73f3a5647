import React from "react";
import SeoHeader from "@/components/seo/SeoHeader";
import Layout from "@/components/layout/Layout2";
import TopSection from "@/components/thrtv/TopSection";
import Youtube from "@/components/thrtv/Youtube";
import { getVideo, getVideoRecent } from "@/pages/api/VideoApi";
import { getFlaggedPage } from "@/pages/api/HomeApi";

const VideoStories = ({ meta, top, mostRecent, sections }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <TopSection main={top} mostRecent={mostRecent} />
      {sections &&
        sections?.length > 0 &&
        sections?.map((section, index) => {
          if (section.stories.length > 0)
            return (
              <>
                <Youtube
                  key={`thr-videos-sec-${index}`}
                  heading={section?.title ?? ""}
                  data={section?.stories ?? []}
                  seeAll={section?.seeAll ?? "/thr-video"}
                />
              </>
            );
        })}
    </Layout>
  );
};

export default VideoStories;

export async function getServerSideProps(context) {
  try {
    const { id } = context.params;
    const url = `/${id}`;
    const response = await getVideo(url);
    if (response.status !== "success") {
      return {
        props: {
          notFound: true,
        },
      };
    }
  
    const [recentRes, sectionRes] = await Promise.all([
      getVideoRecent(url),
      getFlaggedPage("/thr-video"),
    ]);
    const top = response?.data?.data ?? {};
    const { sections } = sectionRes.data;
    return {
      props: {
        meta: response?.data?.meta ?? {},
        top: top,
        mostRecent:
          recentRes && recentRes?.data && recentRes?.data?.length > 0
            ? recentRes?.data
            : [],
        sections: sections && sections.length > 0 ? sections : [],
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        meta: {},
        top: {},
        mostRecent: [],
        data: {},
      },
    };
  }
}
