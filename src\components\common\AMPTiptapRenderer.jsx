import React, { useEffect, useState } from "react";
import Link from "next/link";
import { NodeViewWrapper } from "@tiptap/react";
import { hasHtmlTags, getEmbedType, getTwitterUrl } from "@/utils/Util";
import { FacebookEmbed } from "react-social-media-embed";

export const config = { amp: true };

const renderContent = (content, router, relatedPosts, _id) => {
  if (!content || content.length === 0) return null;
  const renderedContent = [];
  const meaningfulNodes = content.filter((node) => node.type !== "text");
  const totalNodes = meaningfulNodes.length;

  const adPositions = [
    Math.floor(totalNodes / 3),
    Math.floor((2 * totalNodes) / 3),
  ];

  let nonTextIndex = 0;

  content.forEach((node, index) => {
    let element = null;
    switch (node.type) {
      case "paragraph":
        element = (
          <p
            key={index}
            style={{
              marginTop: 0,
              marginBottom: "1rem",
              fontSize: "20px",
              fontWeight: 300,
              lineHeight: 1.4,
              fontFamily: "kepler-std, serif",
              ...(node.attrs?.textAlign && { textAlign: node.attrs.textAlign }),
            }}
          >
            {renderContent(node.content)}
          </p>
        );
        break;
      case "text":
        let textStyles = {};
        let isLink = false;
        let linkAttrs = {};

        if (node.marks) {
          node.marks.forEach((mark) => {
            if (mark.type === "textStyle") {
              textStyles.color = mark.attrs?.color || "rgb(0, 0, 0)";
            }
            if (mark.type === "bold") {
              textStyles.fontWeight = "bold";
            }
            if (mark.type === "italic") {
              textStyles.fontStyle = "italic";
            }
            if (mark.type === "link") {
              isLink = true;
              linkAttrs = mark.attrs;
            }
          });
        }
        const textElement = (
          <span
            key={index}
            style={{ ...textStyles, color: isLink ? "#db242a" : "rgb(0,0,0)" }}
          >
            {node.text}
          </span>
        );
        element = isLink ? (
          <Link
            key={index}
            href={linkAttrs.href}
            target={linkAttrs.target || "_blank"}
            rel={linkAttrs.rel || "noopener noreferrer"}
            style={{ ...textStyles, textDecoration: "none" }}
          >
            {textElement}
          </Link>
        ) : (
          textElement
        );
        break;
      case "hardBreak":
        element = <br key={`hard-break-${index}`} />;
        break;
      case "horizontalRule":
        element = <hr key={`horizantalLine-${index}`} />;
        break;
      case "imageBlock":
        element = (
          <div
            key={index}
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: node.attrs.align,
              width: "100%",
              margin: "20px 0",
            }}
          >
            {/* <div className={style.image} style={{ width: node.attrs.width }}> */}
            <amp-img
              src={node.attrs.src}
              alt={node.attrs.alt}
              width="900"
              height="600"
              layout="responsive"
            ></amp-img>

            <div className={"mainCaption"}>
              {node.attrs.caption && hasHtmlTags(node.attrs.caption) ? (
                <span
                  className={"caption"}
                  dangerouslySetInnerHTML={{ __html: node.attrs.caption }}
                />
              ) : (
                <span className={"caption"}>{node.attrs.caption}</span>
              )}
              {node.attrs.courtesy && hasHtmlTags(node.attrs.courtesy) ? (
                <span
                  className={"courtesy"}
                  dangerouslySetInnerHTML={{ __html: node.attrs.courtesy }}
                />
              ) : (
                <span className={"courtesy"}>{node.attrs.courtesy}</span>
              )}
            </div>
            {/* </div> */}
          </div>
        );
        break;
      case "videoBlock":
        element = (
          <div
            key={index}
            style={{
              display: "flex",
              justifyContent: node.attrs.align,
              width: "100%",
              margin: "20px 0",
            }}
          >
            <div className={"video"} style={{ width: node.attrs.width }}>
              <amp-video
                src={node.attrs.src}
                alt={node.attrs.alt}
                controls
                muted
                layout="responsive"
                width={"100%"}
                height={"100%"}
                style={{ objectFit: "cover" }}
              />
              <div className={"mainCaption"}>
                {node.attrs.caption && hasHtmlTags(node.attrs.caption) ? (
                  <span
                    className={"caption"}
                    dangerouslySetInnerHTML={{ __html: node.attrs.caption }}
                  />
                ) : (
                  <span className={"caption"}>{node.attrs.caption}</span>
                )}
                {node.attrs.courtesy && hasHtmlTags(node.attrs.courtesy) ? (
                  <span
                    className={"courtesy"}
                    dangerouslySetInnerHTML={{ __html: node.attrs.courtesy }}
                  />
                ) : (
                  <span className={"courtesy"}>{node.attrs.courtesy}</span>
                )}
              </div>
            </div>
          </div>
        );
        break;
      case "orderedList":
        element = (
          <ol
            key={index}
            start={node?.attrs?.start || 1}
            className={"listStyle"}
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderContent(item.content)}</li>
            ))}
          </ol>
        );
        break;
      case "bulletList":
        element = (
          <ul
            key={index}
            // start={node?.attrs?.start || 1}
            className={"listStyle"}
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderContent(item.content)}</li>
            ))}
          </ul>
        );
        break;
      case "heading":
        const HeadingTag = `h${node.attrs.level}`;
        element = (
          <HeadingTag
            key={index}
            style={{
              ...(node.attrs?.textAlign && { textAlign: node.attrs.textAlign }),
              fontSize:
                node.attrs.level === 1
                  ? "42px"
                  : node.attrs.level === 2
                  ? "34px"
                  : "34px",
              fontFamily: "kepler-std-semicondensed-dis,serif",
              marginTop: 0,
              marginBottom: " .5rem",
              fontWeight: 500,
              lineHeight: 1.2,
            }}
          >
            {renderContent(node.content)}
          </HeadingTag>
        );
        break;
      case "embed":
        const embedType = getEmbedType(node.attrs.embed);
        element = (
          <>
            <div
              key={index}
              style={{
                width: node.attrs.width,
                margin: "20px 0",
              }}
            >
              <div
                style={{
                  justifyContent: node.attrs.align ?? "center",
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                {embedType === "youtube" && (
                  <div style={{ width: "100%", margin: "20px 0" }}>
                    <amp-iframe
                      src={node.attrs.embed}
                      layout="responsive"
                      width="16"
                      height="9"
                      sandbox="allow-scripts allow-same-origin allow-presentation"
                      frameborder="0"
                    >
                      <div
                        placeholder
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          backgroundColor: "#f0f0f0",
                          height: "100%",
                          width: "100%",
                        }}
                      >
                        Loading video...
                      </div>
                    </amp-iframe>
                  </div>
                )}

                {embedType === "instagram" && (
                  <div style={{ width: "100%" }}>
                    <amp-instagram
                      data-shortcode={node.attrs.embed
                        .replace("embed", "")
                        .split("/")
                        .at(-2)}
                      layout="responsive"
                      width="1"
                      height="1"
                    />
                  </div>
                )}
                {embedType === "twitter" && (
                  <div style={{ width: "100%" }}>
                    <amp-twitter
                      width="1"
                      height="1"
                      layout="responsive"
                      data-tweetid={node.attrs.embed
                        .split("=")[1]
                        .split("/")
                        .at(-1)}
                    ></amp-twitter>
                  </div>
                  // <XEmbed
                  //   url={getTwitterUrl(node.attrs.embed)}
                  //   width={"100%"}
                  //   style={{ maxWidth: "550px" }}
                  // />
                )}
                {embedType === "facebook" && (
                  <FacebookEmbed url={node.attrs.embed} width={"100%"} />
                )}
              </div>
            </div>
            <div className={"mainCaption"}>
              {node.attrs.caption && hasHtmlTags(node.attrs.caption) ? (
                <span
                  className={"caption"}
                  dangerouslySetInnerHTML={{ __html: node.attrs.caption }}
                />
              ) : (
                <span className={"caption"}>{node.attrs.caption}</span>
              )}
              {node.attrs.courtesy && hasHtmlTags(node.attrs.courtesy) ? (
                <span
                  className={"courtesy"}
                  dangerouslySetInnerHTML={{ __html: node.attrs.courtesy }}
                />
              ) : (
                <span className={"courtesy"}>{node.attrs.courtesy}</span>
              )}
            </div>
          </>
        );
        break;
      // case "relatedPosts":
      //   const key = node?.attrs?.blockId || node?.attrs?.postIds?.join(",");
      //   const posts = relatedPosts?.[key] || [];
      //   element = (
      //     <div
      //       key={index}
      //       style={{
      //         display: "flex",
      //         justifyContent: node.attrs.align,
      //         width: "100%",
      //         margin: "20px 0",
      //       }}
      //     >
      //       <div className={style.relatedPostWrapper}>
      //         <h2 className={style.relatedPostHeading}>
      //           {node?.attrs?.title || ""}
      //         </h2>
      //         <div
      //           className={style[node?.attrs?.layout ?? "grid"]}
      //           style={{ width: node?.attrs?.width ?? "100%" }}
      //         >
      //           {posts?.map((post, idx) => (
      //             <Link
      //               key={idx}
      //               className={style.relatedPostCard}
      //               href={`${post?.slug}`}
      //               target="_blank"
      //               passHref
      //             >
      //               <div className={style.relatedPostImage}>
      //                 <Image
      //                   className="imgcover"
      //                   src={post?.coverImg || ""}
      //                   alt={post?.altName || ""}
      //                   fill
      //                   objectFit="cover"
      //                 />
      //               </div>
      //               <div className="w-100 bg-red-100">
      //                 <h3 className={`${style.category} mt-0 mb-1`}>
      //                   {post?.category}
      //                 </h3>
      //                 <h3 className={`${style["card-title"]} mb-0`}>
      //                   {post?.title}
      //                 </h3>
      //               </div>
      //             </Link>
      //           ))}
      //         </div>
      //       </div>
      //     </div>
      //   );
      //   break;
      default:
        break;
    }
    if (element) {
      renderedContent.push(element);
      if (node.type !== "text" && node.type !== "hardBreak") {
        if (adPositions.includes(nonTextIndex)) {
          renderedContent.push(
            <div key={`ad-${nonTextIndex}`}>
              {/* Add add components */}
              {/* <Ads
                style={{ marginBottom: "20px" }}
                id={`div-gpt-ad-stories-middle-${_id}-${nonTextIndex}`}
                adUnits={[
                  {
                    adUnit: "/23290324739/THRI-Desktop-Middle-300",
                    sizes: [[300, 350]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[300, 350]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.[1] || null],
                  "sub-section": [router?.[2] || null],
                }}
              /> */}
            </div>
          );
        }
        nonTextIndex++;
      }
    }
  });
  return renderedContent;
};

// TiptapRendered component
const AMPTiptapRendered = ({ content, router, _id }) => {
  return (
    <div>
      <NodeViewWrapper className={"tip"}>
        {renderContent(content, router, _id)}
      </NodeViewWrapper>
    </div>
  );
};

export default AMPTiptapRendered;
