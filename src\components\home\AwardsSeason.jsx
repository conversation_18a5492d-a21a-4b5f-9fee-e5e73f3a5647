import React, { useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "../common/SlideNavButton";
import style from "./Home.module.css";
import Button from "../common/Button";

const AwardsSeason = ({ title, description, data, seeAll }) => {
  // useEffect(() => {
  //   if (typeof window !== "undefined" && window.googletag) {
  //     window.googletag.cmd.push(function () {
  //       window.googletag.display("div-gpt-ad-1722954099008-0");
  //     });
  //   }
  // }, []);
  const swiperRef = useRef(null);

  const swiperClass = "awards-season-swiper";

  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper) {
      const swiper = swiperRef.current.swiper;

      const handleSlideChange = () => {
        const bullets = document.querySelectorAll(
          `.${swiperClass} .swiper-pagination-bullet`
        );

        bullets.forEach((bullet, index) => {
          bullet.classList.remove("expanded");

          if (index === swiper.realIndex) {
            setTimeout(() => {
              bullet.classList.add("expanded");
            }, 50);
          }
        });
      };

      swiper.on("slideChange", handleSlideChange);

      handleSlideChange();

      return () => {
        swiper.off("slideChange", handleSlideChange);
      };
    }
  }, []);
  return (
    <>
      {data && data.length > 0 && (
        <section id="awardseason" className={style.awardsSeason}>
          <div className="container">
            <div className="row awards-row desk-view">
              <div className="col-md-12 d-flex align-items-start justify-content-end mb-4 awards">
                <SlideNavButton prev={"awardPrev"} next={"awardNext"} />
              </div>
            </div>
            <div className="row d-flex align-items-start">
              <div className="col-md-12 mob-view">
                <div className="d-flex align-items-center justify-content-between">
                  <h2>{title}</h2>
                  <Button href={seeAll}>SEE ALL</Button>
                </div>
                <p>{description}</p>
              </div>
              <div className="col-md-4 col-lg-3 col-xl-2 desk-view">
                <h2>{title}</h2>
                <p>{description}</p>
                <Button href={seeAll}>SEE ALL</Button>
              </div>
              <div className="col-md-8 col-lg-9 col-xl-10 desk-view">
                <Swiper
                  ref={swiperRef}
                  className={swiperClass}
                  autoplay={{
                    delay: 2500,
                    disableOnInteraction: false,
                  }}
                  pagination={{
                    clickable: true,
                  }}
                  modules={[Navigation, Pagination, Keyboard]}
                  navigation={{
                    prevEl: ".awardPrev",
                    nextEl: ".awardNext",
                  }}
                  // navigation={true}
                  loop={true}
                  spaceBetween={15}
                  slidesPerView={1}
                  breakpoints={{
                    425: { slidesPerView: 1 },
                    600: { slidesPerView: 1 },
                    1200: { slidesPerView: 4 },
                  }}
                >
                  {data.map((item, i) => {
                    return (
                      <SwiperSlide key={`awards-${i}`}>
                        <div className={style.cardItem}>
                          <Link
                            className={style.cardWrapper}
                            href={item?.slug ?? "#"}
                          >
                            <div className={style.featureBox}>
                              <Image
                                src={
                                  item?.croppedImg
                                    ? item?.croppedImg
                                    : item?.coverImg
                                    ? item?.coverImg
                                    : ""
                                }
                                alt={item?.altName ?? ""}
                                fill
                              />
                            </div>
                            <h3>{item?.title ?? ""}</h3>
                          </Link>
                        </div>
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </div>
            </div>
            <div className="col-md-12 mob-view">
              <div className="row scrollable-div">
                {data.map((item, i) => {
                  return (
                    <div className="col-md-3 rn-card" key={`awards-mob-${i}`}>
                      <div className={style.cardItem}>
                        <Link
                          className={style.cardWrapper}
                          href={item?.slug ?? "#"}
                        >
                          <div className={style.featureBox}>
                            <Image
                              src={
                                item?.croppedImg
                                  ? item?.croppedImg
                                  : item?.coverImg
                                  ? item?.coverImg
                                  : ""
                              }
                              alt={item?.altName ?? ""}
                              fill
                            />
                          </div>
                          <h3>{item?.title ?? ""}</h3>
                        </Link>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            <hr />
          </div>
        </section>
      )}
    </>
  );
};

export default AwardsSeason;
