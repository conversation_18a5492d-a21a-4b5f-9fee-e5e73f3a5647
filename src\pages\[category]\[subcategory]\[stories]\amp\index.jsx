import { Const } from "@/utils/Constants";
import {
  getStories,
  getStoriesAuthor,
  getLatestStories,
  getYouMayAlsoLike,
  getInfiniteStories,
} from "@/pages/api/ArticleApi";
export const config = { amp: true };
import AmpStories from "@/templates/stories/AmpStories";

const Stories = ({
  _id,
  data,
  author,
  breadcrumbs,
  latest,
  related,
  tag,
  meta,
  slug,
  nextStories,
}) => {
  return (
    <AmpStories
      _id={_id}
      data={data}
      author={author}
      breadcrumbs={breadcrumbs}
      latest={latest}
      related={related}
      tag={tag}
      meta={meta}
      slug={slug}
      nextStories={nextStories}
    />
  );
};

export default Stories;

export async function getServerSideProps({ query, res }) {
  const { category_slug, subcategory_slug, stories_slug } = query;
  const url = `/${category_slug}/${subcategory_slug}/${stories_slug}`;
  const LIMIT = 10;
  const storiesUrl = `/${stories_slug}`;
  const latestStoriesPayload = {
    slug: url,
    limit: LIMIT,
  };

  try {
    const [storiesRes, authorRes, latestStoriesRes, youMayLikeRes] =
      await Promise.all([
        getStories(url),
        getStoriesAuthor(storiesUrl),
        getLatestStories(latestStoriesPayload),
        getYouMayAlsoLike(storiesUrl),
      ]);

    if (storiesRes?.data?.isURLCorrect === false) {
      res.writeHead(301, { Location: storiesRes?.data?.correctUrl });
      res.end();
    }
    if (
      !storiesRes ||
      storiesRes.statusCode === Const.NotFound404 ||
      Object.keys(storiesRes.data.articleData).length === 0
    ) {
      return {
        notFound: true,
      };
    }

    const { data: nextStories } = await getInfiniteStories(
      null,
      url,
      null,
      storiesRes?.data?.articleData?._id,
      10
    );

    return {
      props: {
        _id: storiesRes?.data?.articleData?._id ?? "",
        data: storiesRes?.data?.articleData?.data ?? {},
        breadcrumbs: storiesRes?.data?.articleData?.breadcrumbs ?? [],
        author:
          authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
        latest: latestStoriesRes?.data ?? [],
        related: youMayLikeRes?.data ?? [],
        tag: storiesRes?.data?.articleData?.tag ?? [],
        meta: storiesRes?.data?.articleData?.meta ?? {},
        slug: `/${category_slug}/${subcategory_slug}/${stories_slug}`,
        nextStories: nextStories.data,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      notFound: true,
    };
  }
}
