import React from "react";
import Link from "next/link";
import Image from "next/image";
import style from "@/components/stories/Stories.module.css";

const EditorJsRenderer = ({ data }) => {
  const renderBlock = (block) => {
    switch (block.type) {
      case "header":
        const Tag = `h${block.data.level}`;
        return <Tag key={block.id}>{block.data.text}</Tag>;

      case "paragraph":
        return (
          <p
            key={block.id}
            dangerouslySetInnerHTML={{ __html: block.data.text }}
          ></p>
        );

      case "list":
        return (
          <ul key={block.id}>
            {block.data.items.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        );

      case "image":
        return (
          <div
            className={`${style.image} ${
              block.data.withBorder ? "with-border" : ""
            } ${block.data.stretched ? "stretched" : ""} ${
              block.data.withBackground ? "with-background" : ""
            }`}
            key={block.id}
          >
            <Image
              src={block.data.file.url}
              fill
              alt={block.data.caption || "Image"}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return <>{data.blocks.map(renderBlock)}</>;
};

export default EditorJsRenderer;
