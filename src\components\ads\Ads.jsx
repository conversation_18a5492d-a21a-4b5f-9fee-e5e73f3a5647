import React, { useEffect, useState } from "react";
import { useAdsContext } from "@/context/AdsProvider";

export const Ads = ({ id = "", adUnits = [], targeting = {}, style }) => {
  const { transitioning, loadAds, googletag } = useAdsContext();
  const [selectedAd, setSelectedAd] = useState(null);

  useEffect(() => {
    const width = window.innerWidth;
    const matchedAd = adUnits.find(
      (ad) => width >= ad.minWidth && width <= ad.maxWidth
    );
    setSelectedAd(matchedAd ?? null);
  }, [adUnits]);

  useEffect(() => {
    if (transitioning || !selectedAd || !selectedAd.adUnit || !selectedAd.sizes) return;

    const { adUnit, sizes, sizeMapping } = selectedAd;

    setTimeout(() => {
      loadAds(id, adUnit, sizes, sizeMapping, targeting);
    }, 100);
  }, [transitioning, selectedAd, targeting, loadAds, googletag, id]);

  // Prevent rendering until ad is selected on client
  if (!selectedAd || !selectedAd.adUnit) return null;

  return (
    <div className="ad-flex-all">
      <div
        id={id}
        data-cy="Ad"
        className="ad-text"
        {...(style ? { style } : {})}
      />
    </div>
  );
};
