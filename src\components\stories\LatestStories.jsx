import React from "react";
import Image from "next/image";
import Link from "next/link";
import style from "./Stories.module.css";
import { dateFormateWithTimeShort, getAuthorText } from "@/utils/Util";

const LatestStories = ({ title = "Latest Stories", data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <>
          <section id="youmayalsolike" className={style.youMayAlsoLike}>
            <div className="container">
              <div className="row">
                <div className="col-md-12 d-flex align-items-start justify-content-between">
                  <h2>{title}</h2>
                  {/* <Link href={""} className="btn btn-outline out-btn">
                    <div className="hover-fixed">
                      <div className="middle-btn"></div>
                      SEE ALL
                    </div>
                  </Link> */}
                </div>
              </div>
              <div className="row scrollable-div">
                {data.map((item, i) => {
                  return (
                    <>
                      <div
                        className="col-md-2 col-lg-2 col-xl-3 rn-card"
                        key={`you-may-also-like-${i}`}
                      >
                        <Link href={item?.slug ?? "#"} className="card-wrapper">
                          <div className="feature-box">
                            <Image
                              src={
                                item?.croppedImg
                                  ? item?.croppedImg
                                  : item?.coverImg
                                  ? item?.coverImg
                                  : ""
                              }
                              fill
                              alt={item?.altName ?? "Article Alt"}
                            />
                          </div>
                          <div className="content-sec">
                            <div className="d-flex flex-columnn align-items-center gap-3">
                              <span className="category mb-1">
                                {item?.category ?? ""}
                              </span>
                              <span className="timeline">
                                {dateFormateWithTimeShort(
                                  item?.timestamp ?? ""
                                )}
                              </span>
                            </div>
                            <h3 className="card-title">{item?.title ?? ""}</h3>
                            <p className="card-subtitle">
                              {item?.excerpt ?? ""}
                            </p>
                            <span className="author">
                              {getAuthorText(
                                "BY",
                                item?.author,
                                item?.contributor
                              )}
                            </span>
                          </div>
                        </Link>
                      </div>
                    </>
                  );
                })}
              </div>
            </div>
          </section>
        </>
      )}
    </>
  );
};

export default LatestStories;
