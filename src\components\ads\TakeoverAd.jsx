import React, { useState, useEffect } from "react";
import Image from "next/image";
import { IoCloseOutline } from "react-icons/io5";

const TakeoverAd = ({ src = "/takeover-ad.png", isVideo = false }) => {
  const [ad, setAd] = useState(true);
  useEffect(() => {
    setAd(true);
    const adtime = setTimeout(() => {
      setAd(false);
    }, 4000);

    return () => {
      clearTimeout(adtime);
    };
  }, []);
  return (
    <>
      <div
        className="takeover-block"
        style={{
          background: ad ? "rgba(0,0,0,.8)" : "rgba(0,0,0,0)",
          pointerEvents: ad ? "all" : "none",
        }}
      >
        <div
          className="ad-anime"
          style={{ transform: ad ? "translateY(0%)" : "translateY(-117%)" }}
        >
          <div className="takeover-ad">
            <div className="pos-rel-full ad-img takeover-img">
              <div className="ad-name" style={{ zIndex: 10 }}>
                AD
              </div>
              {isVideo ? (
                <video src={src || ""} autoPlay loop></video>
              ) : (
                <Image src={src || "/takeover-ad.png"} fill objectFit="cover" />
              )}
            </div>
          </div>
          <div
            className="takeover-p flex-all"
            onClick={() => {
              setAd(false);
            }}
          >
            Click here to continue to the website
          </div>
          <div
            className="flex-all"
            onClick={() => {
              setAd(false);
            }}
          >
            <div className="takeover-close flex-all">
              <IoCloseOutline />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TakeoverAd;
