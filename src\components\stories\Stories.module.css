.storiesHeroSec {
  background-color: #f3f4f5;
  /* opacity: 0; */
  /* padding: 130px 0px 30px; */
  padding: 50px 0px 30px;
}

.storiesHeroBanner .heroSection {
  width: 100vw;
  position: relative;
  overflow: hidden;
  /* height: 44vw; */
  height: auto;
  background-color: rgba(0, 0, 0, 0.241);
  aspect-ratio: 16/9;
}

.storiesHeroBanner .heroSection img {
  /* object-fit: contain; */
  object-fit: cover;
  /* object-position: bottom center; */
}

.storiesHeroBanner p {
  font-size: 14px;
  letter-spacing: 0px;
  font-family: "kepler-std", serif !important;
}

.herocrAbs {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 50%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
  z-index: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}

.herocrAbs .herocrText {
  margin: 20px 60px;
}

.herocrAbs .herocrText h1 {
  font-family: var(--font-family-primary);
  font-size: 5rem;
  color: var(--background-color);
}

.heroImgCont {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.breadcumSec {
  margin-bottom: 10px;
}

.breadcumSec .breadcrumb {
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  /* gap: 10px; */
  list-style: none;
  color: var(--primary-color);
  text-transform: uppercase;
  font-size: 16px;
  font-weight: 600;
  font-family: var(--font-family-secondary);
}

.breadcumSec .breadcrumb .breadcrumb-item::after {
  content: ">";
  position: relative;
  font-weight: 300;
  font-size: 16px;
  margin: 0px 10px;
  color: var(--secondary-color);
}

.breadcumSec .breadcrumb .breadcrumb-item:last-child::after {
  content: "";
}

.breadcumSec .breadcrumb .breadcrumb-item a {
  color: currentColor !important;
  text-decoration: none !important;
}

.followus {
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.25);
  /* border-radius: 7px; */
  list-style: none !important;
  padding: 0px;
  margin-bottom: 0px;
  /* overflow: hidden; */
}

.followus a {
  color: initial;
}

.followus .items {
  position: relative;
  /* overflow: hidden; */
  padding: 10px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
}

.followus .items:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #000000;
}

.followus .items:last-child {
  border-bottom: none;
}

.followus .items .icons {
  width: 18px;
  height: 18px;
}
/*  */
.tooltipText {
  background-color: #fff;
  position: absolute;
  top: 0%;
  right: 90%;
  /* transform: translate(-0%, 0); */
  /* bottom: 100%; */
  padding: 10px 15px;
  border-radius: 5px;
  font-size: 15px;
  opacity: 0;
  transition: all 0.5s;
  /* transform-origin: initial; */
  text-align: right;
  align-items: flex-end;
  display: flex;
  width: max-content;
  justify-content: right;
}
.fb {
  color: #3b5998;
}
.tw {
  color: #1da1f2;
}
.wa {
  color: #25d366;
}

/*  Membuat dan style tip*/
.tooltipText::after {
  content: "";
  border-width: 5px;
  border-style: solid;
  border-color: #fff transparent transparent transparent;
  position: absolute;
  top: 100%;
  left: 40%;
  margin-left: 5%;
  z-index: 10;
}
/* Hover text tooltip */
.followus .items:hover .tooltipText {
  opacity: 1;
  transform: translateX(-10px);
}

/*  */
.youMayAlsoLike {
  padding: 10px 0px 0px;
}

.youMayAlsoLike .cardWrapper img {
  transition: all 0.4s ease;
}

.youMayAlsoLike .cardWrapper {
  position: relative;
  width: fit-content;
  overflow: hidden;
  margin-bottom: 35px;
  transition: all 0.3s ease;
}

.youMayAlsoLike .cardWrapper:hover {
  transition: all 0.3s ease;
}

.youMayAlsoLike .cardWrapper:hover .contentSec p {
  color: var(--primary-color);
}

.youMayAlsoLike .cardWrapper .featureBox {
  width: 100%;
  height: 300px;
  margin-bottom: 0px;
  overflow: hidden;
}

.youMayAlsoLike .cardWrapper:hover .featureBox img {
  transform: scale(1.02);
  transition: all 0.4s ease-in-out;
}

.copysvg {
  color: var(--primary-color);
  cursor: pointer;
}

.youMayAlsoLike .cardWrapper .featureBox img {
  width: 100%;
  height: inherit;
  object-fit: cover;
}

.youMayAlsoLike .cardWrapper .contentSec {
  padding: 5px 0px;
  text-align: left;
}

.youMayAlsoLike .cardWrapper .contentSec span.category {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary) !important;
}

.youMayAlsoLike .cardWrapper .contentSec p {
  font-size: 23px;
  color: var(--color);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 10px;
  letter-spacing: 0.00875rem;
  transition: all 0.3s ease;
}

.youMayAlsoLike .cardWrapper .contentSec p.timeline {
  color: var(--secondary-color);
  font-weight: 700;
  font-size: 12px;
  margin-bottom: 2px;
  font-family: var(--font-family-secondary) !important;
}

.imageSec {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 7px;
  overflow: hidden;
}

.contentSec {
  padding: 20px 0px;
  text-align: left;
}

.contentSec h1 {
  color: #000;
  font-size: 55px;
  font-weight: 500;
  line-height: 0.9;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

.contentSec:hover h1 {
  color: var(--primary-color);
}

.contentSec p {
  color: var(--secondary-color);
  font-size: 25px;
  line-height: 1.2;
  margin-bottom: 20px;
  font-family: "kepler-std", serif !important;
  font-weight: 300;
  font-style: normal;
  color: var(--secondary-color);
}

.authorSec {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 30px;
}

.authorSec span.author {
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary) !important;
}

.authorSec span.timeline {
  display: block;
  text-transform: uppercase;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: 16px;
  font-family: var(--font-family-secondary) !important;
}

.ln-cont .card {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  margin: 20px 0;
  transition: 0.5s ease;
}

.ln-cont .card:hover {
  transform: translateX(5px);
  cursor: pointer;
}

.card {
  background-color: #f3f4f5;
  /* border : 1px solid rgba(0, 0, 0, 0.175); */
  border-radius: 7px;
  padding: 0px;
  margin-bottom: 15px;
  overflow: hidden;
}

.card .image {
  position: relative;
  overflow: hidden;
}

.card .image img {
  width: inherit;
  height: inherit;
  object-fit: cover;
  object-position: left;
}

.card .content {
  padding: 10px 20px;
}

.card .content span.category {
  display: block;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--primary-color);
  font-family: var(--font-family-secondary);
}

.card .content span.timeline {
  display: block;
  font-size: 12px;
  color: var(--secondary-color);
  font-family: var(--font-family-secondary);
}

.card .content p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  text-align: left;
}

.heading {
  color: #6442ac;
  font-weight: 600;
  margin-bottom: 0px;
  /* font-size: 28px; */
}

.subheading {
  font-size: 20px;
  font-style: italic;
  margin-bottom: 20px;
}

.featureBox {
  position: relative;
  width: 100%;
  height: 320px;
  background: rgba(163, 163, 163, 0.25);
  border-radius: 7px;
  margin-bottom: 20px;
}

.mainContent {
  margin: 0px 35px 15px 0px;
}

.tip > p:last-child::after {
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20xmlns%3Axlink%3D%27http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%27%20width%3D%27259%27%20height%3D%27110%27%3E%3Cdefs%3E%3Cpattern%20id%3D%27a%27%20preserveAspectRatio%3D%27none%27%20width%3D%27100%25%27%20height%3D%27100%25%27%20viewBox%3D%270%200%2066%2028%27%3E%3Cimage%20width%3D%2766%27%20height%3D%2728%27%20xlink%3Ahref%3D%27data%3Aimage%2Fpng%3Bbase64,iVBORw0KGgoAAAANSUhEUgAAAEIAAAAcCAYAAADV0GlvAAAEh0lEQVRYhe2YW4xeUxTHf9OptjGqpKrELT2ymdPSQULd65a2ikhLQuLBJRVEIkEi0ajEiwjSBCGCRDRCH0gEwZR0SLRImyAimznpVuaFjkurLdXbyPat%2Bexv7fVd6tX3f9prnbXX2ee%2F1l5r79MzPKt%2FjC6Y0KWghi4Rgi4RgomZpobHga8zbeeYBDwL%2FAzc18GsFcBfwKPZkxwrgYOB27MnNg4H5gCLgGNMC8AqlvuAI1zwW6uinAY8KY4iaTe44OsEVUW5GHhY5rzjgl8h%2BouBtcBu4BvgV2CpC%2F63ZG4%2F8DwwHTgJ2AVsAgZd8A3kVUUZP%2FoWoA%2BYLe%2BLfn8HbnPBf5XYHgm8DkwFRoHlLvgNVVH2xvUDTwGHaiKsjPgskiDjF4BrZbwN%2BFbZLgUGZDxHIouwj2TGXGCPRDzFTcD5idwntsOKhB7gQeDoRN0r74s4TPm9WvktgBNd8JG8VVVRjkiQGmDViEFZwDUJCRFrXfB7le2CZDy5KsrpMl6o7Na74P9QOm0zjveVfKoiIcXOGDilW6TkoirKKeOCC34I%2BFw7sjJisCrKyPLTWp8KVVHGFD1O2cyoinJykiXN5s4ETsvebNi2IAwJzu7Eb8yUS5XNDhf8LqX7STvSRMQ9vBF4Dpj5HxY4Azg703b%2BccMu%2BO%2BVTke4ld9zgGlKtz4VqqKcaAVBb40PgPlSmBrmu%2BA3K531MUcBlyvdqJGKC7ChMyd2h%2FNMyxre68DvS0q%2BS9bZAJ0R2yQbWr5Q9tz8zApit7hM6da44OudqSrKCU0WjBHhi2LtyaxqCC74TUqnsyfWm9X8W3TvBB7LPBlELMssalij5AuBKZkV3JFp8rmxdT2TWdUwpGQr68ZhBWcH8CHwC%2FCRBPWQqigjQfcA8zIvAqtYauwxFthq32roKC%2B29iiwwegszTIHw68D3pT1bpbnC%2BWQNjubrdAJER%2B74HcewAJTfOmC1xX6Vkl5jYY2WBXl8UB%2FZlWDFZy7gZsT%2BREX%2FP1VUb4L3Nvu1GqdIzQaUrsqymOTw0w76OI3tUXxO5C2uc4Fvz3x22Nk6T9rdMHvd8HHuvBy5iVBJ0ToBXaaDdbcS4CDMiu7s7QiQvsdMA5dW5W8OvOSoB0RW4AvlK7T%2BhD3%2B7oO5w6qzmIdjBrslaxbdkRQ8vZk%2FKecSutoR4Rufb1Ge2yGIRe8vl80i7LuLPOMO8Q4rOBYRIwouS8ZvyKXxTraEqHkM%2BVa2wl0bYk3zFnGvDEjws0Iw8ieSNi5mVWO9Mb5InBjatGqa%2BwzTm5nZFY24kLfVk%2FOMi1howt%2Bi9I17ffAW0o%2BRW6jGqcrOR7d98p%2Fkrly9a%2FDyojlwJJo7IIfVc9WAVcA10sL01gpcwdc8HqPviFzHxL5AeBK4KrMS%2B0EuCRpea8C8TZ8AfCasv1EMug64IdEv6wqynqHcsF%2FKofAeJd6Qr%2FQ%2BjHzXROCUuxvkuYj8qwVJkmFj4eenhZ2yMLj5e9H43%2BGRszgE1R2jMn9yctfrUjkydnMJkT8L9H9eSvoEiHoEiHoEhEB%2FA3s2zj1mNH%2BUwAAAABJRU5ErkJggg%3D%3D%27%2F%3E%3C%2Fpattern%3E%3C%2Fdefs%3E%3Cpath%20fill%3D%27url%28%23a%29%27%20d%3D%27M0%200h259v110H0z%27%2F%3E%3C%2Fsvg%3E");
  background-size: 1.625rem;
  content: " ";
  display: inline-block;
  height: 0.6875rem;
  margin-left: 0.375rem;
  width: 1.625rem;
}

/* .mainContent::first-letter {
  display: inline-block;
  initial-letter: 3 !important;
  text-transform: uppercase !important;
  font-weight: 500 !important;
  margin-right: 10px !important;
} */

.mainContent p {
  font-size: 22px;
  font-weight: 300;
  line-height: 1.4;
  font-family: "kepler-std", serif !important;
}

.mainContent p a {
  position: relative;
  color: var(--primary-color) !important;
  text-decoration: none;
}
.mainContent p a span {
  color: var(--primary-color) !important;
}

.mainContent p a::after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
  transform-origin: 0% 100%;
  transform: scaleX(0);
  transition: all 0.3s ease;
}

.mainContent p a:hover::after {
  transform: scaleX(1);
  /* text-decoration: underline; */
}

.listStyle {
  font-size: 22px;
}

.mainContent .image {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
}

.mainContent .image img {
  object-fit: cover;
}
.mainContent .video {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
}
.mainContent .video video {
  object-fit: cover;
}
.readMore h3 {
  font-size: 14px;
  font-family: var(--font-family-secondary);
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: 10px;
}

.readMore .breadcumSec {
  margin-bottom: 10px;
}

.mainCaption {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 3px 0px;
}

.caption {
  display: block;
  font-size: 14px !important;
  letter-spacing: 0 !important;
  font-family: "kepler-std", serif !important;
  margin-bottom: 0px !important;
  margin-right: 10px;
  font-weight: 100;
}

.courtesy {
  display: block;
  font-size: 12px !important;
  letter-spacing: 0 !important;
  font-family: "Karla", sans-serif !important;
  margin-bottom: 0px !important;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.5);
}

.readMore .breadcumSec .breadcrumb {
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  /* gap: 10px; */
  list-style: none;
  color: var(--primary-color);
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
  font-family: var(--font-family-secondary);
}

.readMore .breadcumSec .breadcrumb .breadcrumb-item::after {
  content: "|";
  position: relative;
  font-weight: 300;
  font-size: 12px;
  margin: 0px 5px;
  color: var(--secondary-color);
}

.readMore .breadcumSec .breadcrumb .breadcrumb-item:last-child::after {
  content: "";
}

.relatedPostHeading {
  font-size: 34px !important;
  font-weight: 700 !important;
}

.nextStoryContainer {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2rem 0;
  gap: 1rem;
}

.nextStoryTitle {
  font-family: serif;
  font-size: 1.8rem;
  font-weight: bold;
  white-space: nowrap;
  margin: 0;
  color: #1a1a1a;
}

.nextStoryLine {
  flex-grow: 1;
  height: 1px;
  background-color: #1a1a1a;
}

/* Compters and Big Screens Small Changes*/
@media screen and (min-width: 1600px) and (max-width: 1900px) {
}

/* Compters and Big Screens */
@media screen and (min-width: 1600px) {
}

/* Laptop and Tablets Small Changes*/
@media screen and (min-width: 901px) and (max-width: 1200px) {
}

/* Tablets and Mobiles Small Changes */
@media screen and (min-width: 426px) and (max-width: 900px) {
}

@media screen and (max-width: 900px) {
  .tooltipText {
    background-color: #fff;
    position: absolute;
    top: 50%;
    left: 0%;
    /* transform: translate(-0%, 0); */
    /* bottom: 100%; */
    padding: 5px;
    border-radius: 5px;
    font-size: 14px;
    opacity: 0;
    transition: all 0.5s;
  }
  .followus .items:hover .tooltipText {
    opacity: 1;
    transform: translateY(25px);
  }
  .storiesHeroSec {
    padding: 30px 0px 30px;
  }
  .breadcumSec .breadcrumb {
    font-size: 14px;
    margin-bottom: 0px;
  }

  .breadcumSec .breadcrumb .breadcrumb-item::after {
    margin: 0px 5px;
  }

  .contentSec {
    padding: 10px 0px;
  }

  .contentSec h1 {
    font-size: 40px;
  }

  .mainCaption {
    flex-direction: column;
    align-items: baseline;
  }

  .contentSec p {
    font-size: 22px;
  }

  .dropdownauthor {
    width: 275px !important;
  }

  .authorSec span.author {
    font-size: 14px;
  }

  .authorSec span.timeline {
    font-size: 14px;
  }

  .followus {
    display: flex;
    margin-top: 20px;
    /* border; */
  }

  .followus .items {
    border-right: 1px solid rgba(0, 0, 0, 0.25);
    border-bottom: none;
  }

  .followus .items:last-child {
    border-right: none;
  }

  .storiesHeroBanner .heroSection {
    min-height: 44vw;
  }

  .storiesHeroBanner p {
    line-height: 1.3;
  }

  .mainContent {
    margin: 40px 10px 50px;
  }

  .mainContent p {
    font-size: 20px;
  }

  .readMore h3 {
    font-size: 16px;
  }

  .readMore .breadcumSec .breadcrumb {
    font-size: 14px;
  }
}
@media screen and (max-width: 767px) {
  .grid {
    display: grid;
    grid-template-columns: 1fr !important;
    gap: 0px !important;
    padding: 2px 0px !important;
    border-top: 1px solid rgba(0, 0, 0, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 1);
  }
  .row {
    border-top: 1px solid rgba(0, 0, 0, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 1);
  }

  .relatedStoriesCard {
    grid-column: span 1;
  }
  .grid .relatedPostCard,
  .row .relatedPostCard {
    border-bottom: 1px dotted rgba(0, 0, 0, 1);
  }

  .relatedPostCard {
    padding-block: 10px;
  }

  .grid .relatedPostCard:last-child {
    border-bottom: none !important;
  }

  .grid h3.card-title,
  .row h3.card-title {
    -webkit-line-clamp: 2 !important;
    font-size: 23px !important;
  }
  .row .relatedPostCard:first-child {
    padding-top: 10px !important;
  }
  .row .relatedPostCard:last-child {
    padding-bottom: 10px !important;
  }
  .relatedPostHeading {
    font-size: 27px !important;
    font-weight: 700 !important;
  }
  .nextStoryTitle {
    margin-left: 12px;
  }
}

@media screen and (max-width: 425px) {
  .contentSec p {
    font-size: 18px;
  }
  .grid {
    display: grid;
    grid-template-columns: 1fr !important;
    gap: 0px !important;
    padding: 2px 0px !important;
    border-top: 1px solid rgba(0, 0, 0, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 1);
  }

  .row {
    border-top: 1px solid rgba(0, 0, 0, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 1);
  }

  .relatedStoriesCard {
    grid-column: span 1;
  }
  .grid .relatedPostCard,
  .row .relatedPostCard {
    border-bottom: 1px dotted rgba(0, 0, 0, 1);
    border-right: none !important;
    min-height: auto !important;
  }

  .grid .relatedPostCard:last-child {
    border-bottom: none !important;
  }

  .row .images {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding-bottom: 0px !important;
    background-color: #3475de;
  }
}

.modal {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.73);
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9;
  top: 0;
  left: 0;
  animation-name: fadeIn;
  animation-duration: 0.2s;
  animation-iteration-count: 1;
  animation-timing-function: all ease;
}

.card {
  padding: 30px 30px;
  width: 650px;
  background-color: white;
  border-radius: 20px;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  animation-name: zoomin;
  animation-duration: 0.5s;
  animation-iteration-count: 1;
  animation-timing-function: ease;
  overflow-y: hidden;
  position: relative;
}

.cardBody {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}

.imageSec {
  position: relative;
  width: 130px;
  min-width: 130px;
  height: 130px;
  overflow: hidden;
}

.imageSec img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.contentSec {
  width: 100%;
  /* margin-left: 20px; */
}

.contentSec h4 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 10px;
}

.contentSec p {
  font-size: 22px;
  width: 100%;
  font-weight: 300;
  color: rgba(0, 0, 0, 0.65);
}
.author_image {
  border-radius: 50%;
  object-fit: cover;
  object-position: center;
}

.author_right {
  position: relative;
  width: 100%;
}

.dropdownauthor {
  width: 350px;
  /* min-height: 400px; */
  z-index: 2;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3019607843);
  position: absolute;
  background-color: #f3f4f6;
  top: 28px;
  padding: 20px;
}

.disshow {
  display: initial;
}

.authorpopsocial {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 15px;
}
.authorpopsocial a {
  color: inherit;
}

.followp {
  margin: 0 !important;
  font-size: 16px !important;
  text-transform: uppercase;
  font-weight: 700 !important;
  color: var(--primary-color) !important;
  font-family: var(--font-family-secondary) !important;
}

.sociallogo {
  font-size: 18px;
  cursor: pointer;
  color: var(--primary-color);
}

.sociallogo:hover {
  color: var(--primary-color);
}

.authordesignation {
  margin: 0 !important;
  font-size: 20px !important;
  font-family: var(--font-family-secondary) !important;
  color: #000 !important;
  text-align: left;
  margin-bottom: 10px !important;
}

.dotauthor {
  background-color: var(--primary-color);
  width: 12px;
  height: 10px;
  position: relative;
  background-color: var(--primary-color);
  top: 6px;
}

.panddot {
  display: flex;
  gap: 12px;
  color: inherit;
}

.morestories {
  margin: 0 !important;
  font-size: 16px !important;
  text-transform: uppercase;
  font-weight: 700 !important;
  color: #000 !important;
  font-family: var(--font-family-secondary) !important;
}

.hr {
  margin: 0 !important;
  margin-block: 10px !important;
}

.relatedstoryauthor {
  font-size: 18px !important;
  margin: 0 !important;
  color: black !important;
}

.author_wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.timeline {
  text-transform: uppercase;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: 16px;
  font-family: var(--font-family-secondary) !important;
}
.author_name {
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary) !important;
}

.box {
  margin-top: 10px;
  padding: 6px 12px;
  background-color: rgb(230, 244, 255);
  border: 1px solid var(--primary-color);
  border-radius: 8px;
}

.box span {
  display: block;
  font-size: 15px;
  font-weight: 600;
}
.maininput {
  width: 100%;
  border-radius: 5px;
  height: 32px;
  padding: 15px;
  margin-top: 5px;
  border: 1px solid rgb(220, 220, 220);
}

.inputdiv {
  margin-top: 20px;
}

.slug {
  margin-top: 5px;
  font-size: 15px !important;
  color: rgb(177 177 177) !important;
}
.infologo {
  color: var(--primary-color);
  cursor: pointer;
}
.imp {
  color: var(--primary-color);
}

.box p {
  font-size: 15px;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
  gap: 10px;
}

.btnDangerOutline {
  padding: 7px 17px;
  font-size: 14px;
  background-color: transparent;
  border-radius: 25px;
  border: 1px solid #e62214;
  cursor: pointer;
  color: #e62214;
  transition: all 0.3s ease;
}

.btnDangerOutlineblue {
  padding: 7px 17px;
  font-size: 14px;
  background-color: transparent;
  border-radius: 25px;
  border: 1px solid var(--primary-color);
  cursor: pointer;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.btnDanger {
  padding: 7px 17px;
  font-size: 14px;
  background-color: #e62214;
  border-radius: 25px;
  border: 1px solid #e62214;
  cursor: pointer;
  color: #fff;
  transition: all 0.3s ease;
}

.btnDangerblue {
  padding: 7px 17px;
  font-size: 14px;
  background-color: var(--primary-color);
  border-radius: 25px;
  border: 1px solid var(--primary-color);
  cursor: pointer;
  color: #fff;
  transition: all 0.3s ease;
}

.btnDangerOutline:hover {
  background-color: #e62214;
  border-radius: 25px;
  border: 1px solid #e62214;
  color: #fff;
}

.btnDanger:hover {
  border: 1px solid #d0180b;
  background-color: #d0180b;
}

.btnDangerOutlineblue:hover {
  background-color: var(--primary-color);
  border-radius: 25px;
  border: 1px solid var(--primary-color);
  color: #fff;
}

.btnDangerblue:hover {
  border: 1px solid #3475de;
  background-color: #3475de;
}

.row {
  background-color: #f7f7f7;
}

.row .relatedPostCard {
  display: flex !important;
  gap: 10px !important;
  border-bottom: 1px dotted rgba(0, 0, 0, 1);
  border-left: none !important;
  border-radius: 0px !important;
  overflow: hidden;
  color: inherit !important;
  padding-block: 10px;
}
.row .relatedPostCard:first-child {
  padding-top: 0px;
}
.row .relatedStoriesCard {
  grid-column: span 1;
}
.row .relatedPostCard:last-child {
  border-bottom: none !important;
  padding-bottom: 0px;
}

.category {
  color: red;
  font-weight: bold;
  margin-top: 10px;
  font-size: 12px;
  text-transform: uppercase;
  font-family: var(--font-family-secondary) !important;
}
.relatedPostWrapper {
  width: 100%;
  padding: 20px 20px;
  background-color: #f7f7f7;
}
.relatedPostImage {
  position: relative;
  width: 100%;
  max-width: 150px;
  height: auto;
  aspect-ratio: 16/9;
  overflow: hidden;
}
.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}
.grid .relatedStoriesCard {
  grid-column: 0 1;
}
.grid .relatedPostCard {
  display: flex !important;
  gap: 10px !important;
  /* border-right: 1px dotted rgba(0, 0, 0, 1); */
  border-left: none !important;
  border-radius: 0px !important;
  overflow: hidden;
  color: inherit !important;
}

.grid .relatedPostCard:nth-child(2n) {
  border-right: none !important;
}

.grid h3.card-title {
  display: -webkit-box;
  max-width: 300px;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 23px;
  font-weight: 600 !important;
  line-height: 1;
  text-align: left;
  font-weight: 300;
  font-style: normal;
  color: inherit !important;
}
.row h3.card-title {
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 23px;
  font-weight: 600 !important;
  line-height: 1;
  text-align: left;
  font-weight: 300;
  font-style: normal;
  color: inherit !important;
}
.row .image {
  max-width: 25% !important;
  padding-bottom: 20px !important;
}

.row .relatedPostCard:last-child .image {
  padding-bottom: 0 !important;
}
@media screen and (max-width: 425px) {
  .grid h3.card-title,
  .row h3.card-title {
    -webkit-line-clamp: 2 !important;
    font-size: 23px !important;
    font-weight: 500 !important;
  }
  .relatedPostImage {
    max-width: 120px;
  }
}
@keyframes zoomin {
  0% {
    transform: scale(0.5);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
