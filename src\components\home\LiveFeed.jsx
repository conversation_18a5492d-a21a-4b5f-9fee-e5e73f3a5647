import React, { useEffect } from "react";
import Image from "next/image";
import style from "./Home.module.css";

import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Link from "next/link";
import { formatDateTimeHv } from "@/utils/Util";

const LiveFeed = ({ data }) => {
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom2", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent2",
        start: "top 60%",
        // markers: true,
      },
    });
  }, []);
  return (
    <>
      {data && data.length > 0 && (
        <section id="livefeed" className={style.liveFeed}>
          <div className="container">
            <div className="row">
              <div className="col-md-12">
                <div className="lf-image">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 700 65">
                    <path d="M56.2 40.9c-.7 5-1.6 15.5-2.2 20.9-1.2-.1-4.8-.1-10.6-.3-5.8-.1-13.9-.2-24.4-.2-5 0-15.2.4-17.4.4l.2-1.2 4.8-.4c2.4-.2 2.8-.7 3.1-2.5.4-3.4.6-11.2.6-20.9V21.6C10.1 11.8 10 9.2 9.8 7 9.7 5.5 9 4.9 7.2 4.7l-4.8-.4.1-1.2c1.6 0 11.3.4 17.4.4 6.7 0 12-.4 16.2-.4l.4 1.2-4.3.4c-2.1.2-3.2.7-3.3 2.4-.3 3.9-.6 11.5-.6 20v10.2c0 10.7 0 18.2.3 20.6.2 1.3.8 1.8 2 1.8 8.1 0 12.3-.5 15.8-3.4 3.7-3.2 6.3-7.8 8.7-15.5h1.1zM94 25.6c0-11.1-.3-16.1-.5-18.3-.3-2.4-2.3-2.7-7.4-3l.3-1.2c3.4 0 9.6.4 17.8.4 6.3 0 12.8-.4 15.9-.4l.3 1.2c-5.4.4-7.3.8-7.6 3-.2 2.2-.5 7.3-.5 18.3v13.3c0 11.2.4 16.4.5 18.6.3 2.2 2.2 2.6 7.6 2.9l-.3 1.2c-3.5 0-10.5-.4-16.8-.4-7.3 0-13.5.4-17.4.4l.2-1.2c5.1-.4 7.2-.7 7.4-2.9.3-2.2.5-9.2.5-18.6V25.6zM145.9 3.1c2.4 0 10.2.4 17.3.4 6.4 0 11-.4 16.6-.4l.6 1.2-4.2.5c-1.6.2-2.2.5-1.9 2.2.4 1.7 9.6 26.7 15.1 40.1 4.3-11.2 12.3-31.3 13.9-39.5.4-1.9-.1-2.4-2.1-2.7-1.1-.4-4.9-.6-6.2-.7l-.2-1.2c2.8 0 7.7.4 11.2.4 3.5 0 5.9-.4 9.6-.4l.7 1.2c-5.8 0-7.3 1.4-9.7 6.4C202.2 19.8 193 42 185.7 62c-1.9.1-7.3.3-9.6.6-7.7-20.4-18.5-46.5-22-53.7-1.9-3.8-3.4-4.6-8.1-4.7l-.1-1.1zM240 3.1c3.2 0 13.5.4 18.9.4h18.2c7.3 0 12.9-.4 15.2-1.1-.6 3.6-.3 14.1-.3 18.8l-1.2.4c-.5-3.6-2.1-8.2-4.4-12C283.6 5.4 282 5 270.7 5h-1.2c-1.5 0-2.1.4-2.1 1.3-.1 2.9-.6 9.9-.6 16.2v7.8c1.3.1 3.5.1 5.1.1 7.9 0 10.8-2.9 12.6-10.4h1.1c-.9 6.7-1.2 18.7-.6 24l-1.2-.1c-1.2-7.3-3.2-11.9-12.5-11.9-1.5 0-3.3 0-4.5.2V39c0 7.3.1 14.6.4 18 .1 1.5.5 2.6 4.2 2.6 6.7 0 10.7-.2 15.1-2.8 4.4-2.6 8.4-9.9 10-15.5l1.2.4c-.1 1.9-2.6 17.5-3.2 20.1-6.2-.3-15.8-.4-23.8-.4H259c-6.8 0-15.1.4-19.4.4l.1-1.2 4.8-.4c2.2-.2 3.5-1 3.6-3.2.3-3.5.5-11.3.5-19.8V22.5c0-8.6-.2-13.1-.5-15.5-.2-1.5-1.3-2.1-3.5-2.3l-4.6-.4V3.1zM370.1 3.1c2.7 0 13.5.4 20.4.4h13.3c10.4 0 16.9-.5 19.7-.9-.8 7.4-.5 16.6-.4 19.2H422c-.5-2.9-1.2-7.3-4.1-11-2.7-3.5-6.2-5.8-16.6-5.8h-1.9c-1.2 0-1.9.2-1.9 1.2-.1 1-.5 6.9-.5 16.6v8.8c1.1.3 2.8.4 4.9.4 7.6 0 12.1-1.5 13.5-10.3l1.2.2c-.7 5.9-.6 20.4-.1 23.4h-1.2c-1.5-6.9-3.8-11.6-13.5-11.6-3 0-4 .1-4.9.4v9c0 9.5.2 12.5.4 14.3.2 1.7.9 2.5 2.9 2.7 1.4.1 3.6.6 5.8.7l-.2 1.2c-3.4 0-10.3-.4-17-.4-7.7 0-16.1.4-18.3.4l-.4-1.2 5-.5c1.7-.2 2.8-.9 3-2.5.2-1.9.5-9.8.5-18.2V22.5c0-9.9-.1-12.1-.2-14.8-.1-1.8-.9-2.7-2.2-2.8l-6-.5-.1-1.3zM454.6 3.1c3.2 0 13.5.4 18.9.4h18.2c7.3 0 12.9-.4 15.2-1.1-.7 3.6-.4 14.2-.4 18.9l-1.2.4c-.5-3.6-2.1-8.2-4.4-12-2.7-4.3-4.3-4.7-15.7-4.7H484c-1.5 0-2.1.4-2.1 1.3-.1 2.9-.6 9.9-.6 16.2v7.8c1.3.1 3.5.1 5.1.1 7.9 0 10.8-2.9 12.6-10.4h1.1c-.9 6.7-1.2 18.7-.6 24l-1.2-.1c-1.2-7.3-3.2-11.9-12.5-11.9-1.5 0-3.3 0-4.5.2V39c0 7.3.1 14.6.4 18 .1 1.5.5 2.6 4.2 2.6 6.7 0 10.7-.2 15.1-2.8 4.4-2.6 8.4-9.9 10-15.5l1.2.4c-.1 1.9-2.6 17.5-3.2 20.1-6.2-.3-15.8-.4-23.8-.4h-11.7c-6.8 0-15.1.4-19.4.4l.1-1.2 4.8-.6c2.2-.2 3.5-1 3.6-3.2.3-3.5.5-11.3.5-19.8V22.5c0-8.6-.2-13.1-.5-15.5-.2-1.5-1.3-2.1-3.5-2.3l-4.5-.4V3.1zM542.3 3.1c3.2 0 13.5.4 18.9.4h18.2c7.3 0 12.9-.4 15.2-1.1-.7 3.6-.4 14.2-.4 18.9l-1.2.4c-.5-3.6-2.1-8.2-4.4-12C585.9 5.4 584.3 5 573 5h-1.2c-1.5 0-2.1.4-2.1 1.3-.1 2.9-.6 9.9-.6 16.2v7.8c1.3.1 3.5.1 5.1.1 7.9 0 10.8-2.9 12.6-10.4h1.1c-.9 6.7-1.2 18.7-.6 24H586c-1.2-7.3-3.2-11.9-12.5-11.9-1.5 0-3.3 0-4.5.2v6.8c0 7.3.1 14.6.4 18 .1 1.5.5 2.6 4.2 2.6 6.7 0 10.7-.2 15.1-2.8 4.4-2.6 8.4-9.9 10-15.5l1.2.4c-.1 1.9-2.6 17.5-3.2 20.1-6.2-.3-15.8-.4-23.8-.4h-11.7c-6.8 0-15.1.4-19.4.4l.1-1.2 4.8-.4c2.2-.2 3.5-1 3.6-3.2.3-3.5.5-11.3.5-19.8V22.5c0-8.6-.2-13.1-.5-15.5-.2-1.5-1.3-2.1-3.5-2.3l-4.5-.4V3.1zM638.5 22.9c0-9.2-.3-13.1-.4-15.3-.2-2.2-1.5-2.7-3.4-2.8l-4.8-.4.2-1.2c2.2 0 11.2.4 16.5.4 5.9 0 13.6-.4 19.3-.4 12.1 0 16.9 1.3 20.7 3.4 8.3 4.4 12 13.2 12 24.2 0 11.4-4.2 23.3-17.6 28.3-4.2 1.5-10.4 2.7-17.9 2.7-5.8 0-11.8-.3-17.6-.3-7.8 0-13.2.4-15.8.4l.1-1.2 5-.4c2.6-.2 3-1.1 3.1-2.7.2-1.9.6-10 .6-19.9V22.9zm18.2 15.2c0 14.2.1 18 .5 20.2.2 1.1.8 1.8 3.5 1.8 7.9 0 11.3-2.4 13.7-5.8 2.5-3.5 5-11.2 5-23 0-12.1-1.9-18.7-5.2-22.5-2.7-3.1-6.8-4.2-11.9-4.2-4.5 0-5 .9-5.2 2.7-.2 1.3-.4 4.1-.4 16.8v14z"></path>
                  </svg>
                  <div className="lf-line"></div>
                </div>
                {/* <h2 className={style.heading} style={{ color: "teal" }}>
                  LIVE FEED
                </h2> */}
                <p className={style.subheading}>Inside the shownd biz of TV</p>
              </div>
            </div>
            <div className="row flexcolumn-reverse">
              <div className="col-md-5">
                <div className={style.hvItems}>
                  {data.map(
                    (item, i) =>
                      i !== 0 && (
                        <Link
                          className={style.hvContentSec}
                          href={item?.slug ?? "#"}
                          key={`lv-${i}`}
                        >
                          <span className={style.smallTime}>
                            {formatDateTimeHv(item?.timestamp ?? "")}
                          </span>
                          <h3 className={style.sideTitle}>
                            {item?.title ?? ""}
                          </h3>
                          <p>
                            {item?.excerpt?.replace(/(<([^>]+)>)/gi, "") ?? ""}
                          </p>
                        </Link>
                      )
                  )}
                </div>
              </div>
              <div className="col-md-7 zoom-cont">
                <Link href={data[0]?.slug ?? "#"}>
                  <div
                    className={`${style.hvImageSec} image-zoom-parent3 pos-rel`}
                  >
                    <Image
                      className="image-zoom2 imgcover "
                      src={data[0]?.coverImg ?? ""}
                      alt={data[0]?.altName ?? ""}
                      fill
                    />
                  </div>
                  <div className={style.hvContentSec}>
                    <span className={style.smallTime}>
                      {formatDateTimeHv(data[0]?.timestamp ?? "")}
                    </span>
                    <h3>{data[0]?.title ?? ""}</h3>
                    <p>
                      {data[0]?.excerpt?.replace(/(<([^>]+)>)/gi, "") ?? ""}
                    </p>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default LiveFeed;
