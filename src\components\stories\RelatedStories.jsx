import React from "react";
import Link from "next/link";
import Image from "next/image";
import style from "./Stories.module.css";
import { dateFormateWithTimeShort } from "@/utils/Util";
import SponsoredTag from "../common/SponsoredTag";

const RelatedStories = ({ data }) => {
  return (
    <div className="row mob-hidden">
      <div className="col-md-12">
        <h2>Latest News</h2>
      </div>
      <div className="col-md-12">
        {data?.map((item, i) => {
          return (
            <Link
              href={item?.slug ?? "#"}
              className="side-card h-par updated"
              key={`latest-stories-${i}`}
            >
              <div className="image h100">
                <Image
                  fill
                  src={
                    item?.croppedImg
                      ? item?.croppedImg
                      : item?.coverImg
                      ? item?.coverImg
                      : ""
                  }
                  alt={item?.altName ?? ""}
                  className="imgcover"
                />
              </div>
              <div className="content">
                {item.isPromotional && (
                  <SponsoredTag
                    customStyle={{
                      lineHeight: 1,
                      fontWeight: 700,
                    }}
                  />
                )}
                <div className="d-flex flex-columnn align-items-center gap-2">
                  <span className="category">{item?.category ?? ""}</span>
                  <span className="timeline">
                    {dateFormateWithTimeShort(item?.timestamp ?? "")}
                  </span>
                </div>
                <h3 className="card-title">{item?.title ?? ""}</h3>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default RelatedStories;
