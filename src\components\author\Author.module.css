.authorHero .heroSection {
  width: 100vw;
  position: relative;
  overflow: hidden;
  height: 30vw;
  background-color: rgba(0, 0, 0, 0.241);
}
.authorSection {
  /* padding: 100px 0px 50px; */
  padding: 20px 0px 50px;
}
.infoSection {
  display: flex;
  align-items: center;
  /* gap: 15px; */
  width: 100%;
  margin-bottom: 50px;
  flex-direction: column;
  /* justify-content: center; */
  background-color: rgb(0 0 0 / 3%);
  padding: 1rem 5rem;
  border-radius: 5px;
}
.imageBlock {
  display: flex;
  position: relative;
  width: 180px;
  min-width: 180px;
  height: 180px;
  border-radius: 50%;
  overflow: hidden;
}
.imageBlock img {
  object-fit: cover;
}
.contentWrapper {
  display: block;
  width: 100%;
}
.descriptionBlock {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  margin-top: 15px;
  border-bottom: 1px dotted rgba(0, 0, 0, 1);
  flex-direction: column;
}
.titleGroup {
  width: auto;
}
.followUs {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 10px;
}
.followUs a {
  position: relative;
  width: 25px;
  height: 25px;
  padding: 2px;
  color: inherit !important;
}
.followUs svg {
  width: 25px;
  height: 25px;
  color: var(--primary-color);
  transition: all 0.3s ease;
}
.followUs svg:hover {
  color: rgba(0, 0, 0, 1);
}
.title {
  font-size: 42px;
  margin-bottom: 5px;
  font-weight: 600;
}
.contentBlock {
  margin-top: 10px;
}
.contentBlock p {
  font-size: 24px;
}
.subheading {
  font-size: 22px;
  margin-bottom: 10px;
  font-family: var(--font-family-secondary);
  font-weight: 300;
}
.imageSec {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 7px;
  overflow: hidden;
}
.contentSec {
  padding: 20px 36px;
  text-align: center;
}
.contentSec h3 {
  color: #000;
  font-size: 50px;
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}
.hoverSec:hover.contentSec h3 {
  color: var(--primary-color);
}
.contentSec p {
  color: var(--secondary-color);
  font-size: 22px;
  /* width: 650px; */
  line-height: 1.2;
  margin: 5px auto;
  font-family: "kepler-std-display", serif !important;
  font-weight: 300;
  font-style: normal;
}
.ln-cont .card {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  margin: 20px 0;
  transition: 0.5s ease;
}
.ln-cont .card:hover {
  transform: translateX(5px);
  cursor: pointer;
}
.card {
  background-color: #f3f4f5;
  /* border : 1px solid rgba(0, 0, 0, 0.175); */
  border-radius: 7px;
  padding: 0px;
  margin-bottom: 15px;
  overflow: hidden;
}
.card .image {
  position: relative;
  overflow: hidden;
}
.card .image img {
  width: inherit;
  height: inherit;
  object-fit: cover;
  object-position: left;
}
.card .content {
  padding: 10px 20px;
}
.card .content span.author {
  display: block;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--primary-color);
  font-family: var(--font-family-secondary);
}
.card .content span.timeline {
  display: block;
  font-size: 12px;
  color: var(--secondary-color);
  font-family: var(--font-family-secondary);
}
.card .content p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  text-align: left;
}
/* Compters and Big Screens Small Changes*/
@media screen and (min-width: 1600px) and (max-width: 1900px) {
}
/* Compters and Big Screens */
@media screen and (min-width: 1600px) {
}
/* Laptop and Tablets Small Changes*/
@media screen and (min-width: 901px) and (max-width: 1200px) {
  .infoSection {
    padding: 1rem 3rem;
  }
}

@media screen and (max-width: 900px) {
  .infoSection {
    padding: 1rem 2rem;
  }
  .authorHero .heroSection {
    height: 50vw;
  }
  .imageBlock {
    display: flex;
    position: relative;
    width: 150px;
    min-width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
  }
  .imageBlock img {
    object-fit: cover;
  }
  .contentWrapper {
    display: block;
    width: 100%;
  }
  .descriptionBlock {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;

    margin-top: 15px;
    border-bottom: 1px dotted rgba(0, 0, 0, 1);
  }
  .titleGroup {
    width: auto;
  }
  .followUs {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 10px;
  }
  .authorSection {
    padding: 20px 0px 50px;
  }

  .followUs a {
    position: relative;
    width: 25px;
    height: 25px;
    padding: 2px;
    color: inherit !important;
  }
  .followUs svg {
    width: 25px;
    height: 25px;
    color: var(--primary-color);
    transition: all 0.3s ease;
  }
  .followUs svg:hover {
    color: rgba(0, 0, 0, 1);
  }
  .title {
    font-size: 24px;
    margin-bottom: 5px;
    font-weight: 600;
  }
  .contentBlock {
    margin-top: 10px;
  }
  .contentBlock p {
    font-size: 20px;
  }
  .subheading {
    font-size: 18px;
    margin-bottom: 10px;
    font-family: var(--font-family-secondary);
    font-weight: 300;
  }
}
/* Tablets and Mobiles Small Changes */
@media screen and (min-width: 426px) and (max-width: 900px) {
  .imageBlock {
    display: flex;
    position: relative;
    width: 100px;
    min-width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
  }
}

@media screen and (max-width: 425px) {
  .authorSection {
    padding: 20px 0px 25px;
  }
  .authorHero {
    top: 0px;
  }
  .infoSection {
    display: flex;
    align-items: center;
    /* gap: 20px; */
    width: 100%;
  }
  .imageBlock {
    display: flex;
    position: relative;
    width: 100px;
    min-width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
  }
  .imageBlock img {
    object-fit: cover;
  }
  .contentWrapper {
    display: block;
    width: 100%;
  }
  .descriptionBlock {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    margin-top: 15px;
    border-bottom: 1px dotted rgba(0, 0, 0, 1);
  }
  .titleGroup {
    width: auto;
  }
}
