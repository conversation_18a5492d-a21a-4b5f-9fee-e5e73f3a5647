import React from "react";
import Image from "next/image";
import { dateFormateWithTimeShort } from "@/utils/Util";
import Link from "next/link";

const LatestSection = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <>
          <div className="row">
            <div className="col-md-12">
              <h2>Latest News</h2>
            </div>
            <div className="col-md-12">
              {data.map((item, i) => {
                return (
                  <Link
                    className="side-card h-par"
                    key={`latest-${i}`}
                    href={item?.slug ?? ""}
                  >
                    <div className="image h100">
                      <Image
                        className="imgcover"
                        src={
                          item?.croppedImg
                            ? item?.croppedImg
                            : item?.image
                            ? item?.image
                            : ""
                        }
                        alt={item?.altName ?? ""}
                        fill
                      />
                    </div>
                    <div className="content">
                      <div className="d-flex flex-columnn align-items-center gap-2 mb-1">
                        {item.isPromotional && (
                          <SponsoredTag
                            customStyle={{
                              marginBottom: "5px",
                              lineHeight: 1,
                              fontWeight: 700,
                            }}
                          />
                        )}
                        <span className="category">
                          {item?.subcategory ?? ""}
                        </span>
                        <span className="timeline">
                          {dateFormateWithTimeShort(item?.timestamp ?? "")}
                        </span>
                      </div>
                      <h3 className="card-title">{item?.title ?? ""}</h3>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default LatestSection;
