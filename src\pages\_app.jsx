import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import AdsProvider from "@/context/AdsProvider";
import Loader from "@/components/common/Loader";
import { Toaster } from "react-hot-toast";
import "bootstrap/dist/css/bootstrap.min.css";
import "@/styles/globals.css";
import "@/styles/microsite.css";
import "@/styles/hamburger.css";
import "@/styles/popup.css";
import "@/styles/header.css";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

export default function App({ Component, pageProps }) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const handleStart = (url, { shallow }) => {
      if (!shallow) {
        setIsLoading(true);
      }
    };

    const handleStop = () => setIsLoading(false);

    router.events.on("routeChangeStart", handleStart);
    router.events.on("routeChangeComplete", handleStop);
    router.events.on("routeChangeError", handleStop);

    return () => {
      router.events.off("routeChangeStart", handleStart);
      router.events.off("routeChangeComplete", handleStop);
      router.events.off("routeChangeError", handleStop);
    };
  }, []);

  return (
    <>
      <AdsProvider>
        {isLoading ? <Loader /> : <Component {...pageProps} />}
      </AdsProvider>
      <Toaster />
    </>
  );
}
