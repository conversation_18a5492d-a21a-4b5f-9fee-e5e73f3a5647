import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Avtar from '@/assets/images/avtar1.jpg'
import style from './Home.module.css'

const FeaturedVoices = () => {
  return (
    <section id="featuredvoices" className={style.featuredVoices}>
        <div className="container">
            <div className="row">
                <div className="col-md-12 d-flex align-items-center justify-content-between">
                    <h2>Featured Voices</h2>
                    <Link href={""} className="btn btn-outline out-btn">
                        <div className="middle-btn"></div>
                        SEE ALL
                    </Link>
                </div>
            </div>
            <div className="row">
                <div className="col-md-3">
                    <div className={style.reviewCard}>
                        <div className={style.imageAvtar}>
                            <Image src={Avtar} width={50} height={50} alt="" />
                        </div>
                        <div className={style.contentSec}>
                            <span className={style.title}>ALEX WEPRIN</span>
                            <p className={style.desc}>SAG Awards Analysis: What the Results Tell Us About the Oscar Race</p>
                        </div>
                    </div>
                </div>
                <div className="col-md-3">
                    <div className={style.reviewCard}>
                        <div className={style.imageAvtar}>
                            <Image src={Avtar} width={50} height={50} alt="" />
                        </div>
                        <div className={style.contentSec}>
                            <span className={style.title}>ALEX WEPRIN</span>
                            <p className={style.desc}>SAG Awards Analysis: What the Results Tell Us About the Oscar Race</p>
                        </div>
                    </div>
                </div>
                <div className="col-md-3">
                    <div className={style.reviewCard}>
                        <div className={style.imageAvtar}>
                            <Image src={Avtar} width={50} height={50} alt="" />
                        </div>
                        <div className={style.contentSec}>
                            <span className={style.title}>ALEX WEPRIN</span>
                            <p className={style.desc}>SAG Awards Analysis: What the Results Tell Us About the Oscar Race</p>
                        </div>
                    </div>
                </div>
                <div className="col-md-3">
                    <div className={style.reviewCard}>
                        <div className={style.imageAvtar}>
                            <Image src={Avtar} width={50} height={50} alt="" />
                        </div>
                        <div className={style.contentSec}>
                            <span className={style.title}>ALEX WEPRIN</span>
                            <p className={style.desc}>SAG Awards Analysis: What the Results Tell Us About the Oscar Race</p>
                        </div>
                    </div>
                </div>
            </div>
            <hr />
        </div>
    </section>
  )
}

export default FeaturedVoices