import { Const } from "@/utils/Constants";
import { getNewsSitemap } from "@/pages/api/ArticleApi";
import { escapeXml, convertToISTISOString } from "@/utils/Util";

export default async function handler(req, res) {
  try {
    res.setHeader("Content-Type", "application/xml");
    const response = await getNewsSitemap();
    const data = response && response.data.length > 0 ? response.data : [];
    if (data.length > 0) {
      const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
          xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
          xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
          ${data
            .map(
              (item) => `
              <url>
                <loc>${Const.ClientLink}${item?.loc || ""}</loc>
                <news:news>
                  <news:publication>
                    <news:name>${Const.Brand}</news:name>
                    <news:language>${"en"}</news:language>
                  </news:publication>
                  <news:publication_date>${convertToISTISOString(item?.publication_date || "")}</news:publication_date>
                  <news:title>${escapeXml(item?.title) || ""}</news:title>
                  <news:keywords>${
                    item?.keywords?.join(",") || ""
                  }</news:keywords>
                </news:news>
                <lastmod>${convertToISTISOString(item?.lastmod || "")}</lastmod>
                ${
                  item?.image
                    ? `<image:image>
                        <image:loc>${item?.image || ""}</image:loc>
                      </image:image>`
                    : ""
                }
              </url>`
            )
            .join("")}
      </urlset>`;
      return res.status(200).send(sitemap);
    } else {
      return res.status(404).send();
    }
  } catch (error) {
    console.error("Error generating sitemap:", error.message);
    return res.status(500).send("Internal Server Error");
  }
}
