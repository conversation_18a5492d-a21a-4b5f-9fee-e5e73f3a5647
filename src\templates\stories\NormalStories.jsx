import React from "react";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";
import { useContinuousScroll } from "@/hooks/useContinuesScroll";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import NewsArticleSchema from "@/components/seo/NewsArticleSchema";
import MainContent from "@/components/stories/MainContent";
import { Ads } from "@/components/ads/Ads";
import RelatedStories from "@/components/stories/RelatedStories";
import NextStorySeparator from "@/components/stories/NextStorySeparator";
import { Const } from "@/utils/Constants";
import style from "@/components/stories/Stories.module.css";
import Hero from "@/components/stories/Hero";
import ReviewWidget from "@/components/stories/ReviewWidget";
import ReviewSchema from "@/components/seo/ReviewSchema";
import Head from "next/head";

const NormalStories = ({
  _id,
  data,
  author,
  breadcrumbs,
  latest,
  related,
  tag,
  meta,
  slug,
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const { stories, visibleSlug, registerStoryRef } = useContinuousScroll({
    _id,
    data,
    author,
    breadcrumbs,
    latest,
    related,
    tag,
    meta,
    slug,
  });
  return (
    <>
      <Head>
        <link rel="amphtml" href={`${Const.ClientLink}${slug}/amp`} />
      </Head>
      <Layout>
        <div>
          <div>
            {stories.map(
              ({
                _id,
                data,
                author,
                breadcrumbs,
                latest,
                related,
                tag,
                meta,
                slug,
              }) => {
                // Parse content for each story individually
                const storyContent =
                  data && data.content ? JSON.parse(data.content) : "";
                const reviews = data?.reviews ? data.reviews : null;
                // Detect if this is a promotional article
                const promotional = data?.isPromotional || false;

                return (
                  <div
                    key={_id}
                    className={`story-container `}
                    ref={(el) => registerStoryRef(el, _id, slug)}
                    data-current={slug === visibleSlug ? "true" : "false"}
                    data-promotional={promotional ? "true" : "false"}
                    islast={
                      _id.toString() ===
                      stories[stories.length - 1]?._id.toString()
                        ? "true"
                        : "false"
                    }
                  >
                    {slug === visibleSlug && (
                      <>
                        <SeoHeader meta={meta} />
                        <BreadcrumbSchema itemList={breadcrumbs ?? []} />
                        <NewsArticleSchema
                          headline={meta?.title || ""}
                          datePublished={data?.timestamp || ""}
                          dateModified={data?.updatedAt || ""}
                          articleSection={breadcrumbs?.[0]?.name || ""}
                          keywords={meta?.keywords || []}
                          description={meta?.description || ""}
                          url={pathname}
                          content={storyContent || {}}
                          author={author?.[0] || {}}
                          image={data?.coverImg || ""}
                        />
                        {(reviews?.title ||
                          reviews?.cast ||
                          reviews?.director ||
                          reviews?.runtime) && (
                          <ReviewSchema
                            name={reviews?.title || ""}
                            headline={data?.title || ""}
                            description={meta?.description || ""}
                            image={data?.coverImg || ""}
                            duration={reviews?.runtime || 0}
                            director={reviews?.director || ""}
                            author={author?.[0] || {}}
                            actor={reviews?.cast || ""}
                            url={pathname}
                            datePublished={data?.timestamp || ""}
                            dateModified={data?.updatedAt || ""}
                          />
                        )}
                      </>
                    )}

                    <Hero
                      _id={_id}
                      breadcrumbs={breadcrumbs}
                      title={data?.title ?? ""}
                      description={data?.excerpt ?? ""}
                      author={author ?? []}
                      timeline={data?.updatedAt ?? ""}
                      coverImage={data?.coverImg ?? ""}
                      caption={data?.caption ? data?.caption : ""}
                      courtesy={data?.courtesy ? data?.courtesy : ""}
                      promotional={promotional}
                      contributor={
                        data?.contributor && data?.contributor.length > 0
                          ? data?.contributor
                          : []
                      }
                      altName={data?.altName ? data?.altName : ""}
                      readTime={data?.readTime ? data?.readTime : 5}
                      articleUrl={`${Const.ClientLink}${pathname}`}
                      isPromotional={promotional}
                      reviews={data?.reviews ? data.reviews : null}
                    />
                    <section
                      id="storiessection"
                      className={style.storiesSection}
                    >
                      <div className="container">
                        <div className="row">
                          <div className="col-md-8">
                            <ReviewWidget data={reviews} />
                            <MainContent
                              _id={_id}
                              content={storyContent}
                              note={data?.duplicationNote ?? ""}
                              tag={tag}
                              isPromotional={promotional}
                            />
                          </div>
                          <div className="col-md-4 ln-cont">
                            <Ads
                              id={`div-gpt-ad-stories-rhs-300x250-${_id}`}
                              style={{ marginBottom: "20px" }}
                              adUnits={[
                                {
                                  adUnit: "/23290324739/THRI-Desktop-RHS-300",
                                  sizes: [[300, 250]],
                                  sizeMapping: [
                                    {
                                      viewport: [0, 0],
                                      sizes: [[300, 250]],
                                    },
                                  ],
                                  minWidth: 1024,
                                  maxWidth: Infinity,
                                },
                                {
                                  adUnit: "/23290324739/THRI-Desktop-RHS-300",
                                  sizes: [[300, 250]],
                                  sizeMapping: [
                                    {
                                      viewport: [1023, 0],
                                      sizes: [[300, 250]],
                                    },
                                  ],
                                  minWidth: 768,
                                  maxWidth: 1023,
                                },
                                // {
                                //   adUnit: "/23290324739/THRI-Desktop-RHS-300",
                                //   sizes: [[300, 250]],
                                //   sizeMapping: [
                                //     {
                                //       viewport: [767, 0],
                                //       sizes: [[300, 250]],
                                //     },
                                //   ],
                                //   minWidth: 0,
                                //   maxWidth: 767,
                                // },
                              ]}
                              targeting={{
                                section: [router?.query?.category || null],
                                "sub-section": [
                                  router?.query?.subcategory || null,
                                ],
                              }}
                            />
                            <RelatedStories
                              data={
                                latest ??
                                stories[0].latest.filter(
                                  (el) => el?._id.toString() !== _id
                                )
                              }
                            />
                            <Ads
                              id={`div-gpt-ad-stories-rhs-300x600-${_id}`}
                              style={{ marginBottom: "20px" }}
                              adUnits={[
                                {
                                  adUnit: "/23290324739/THRI-Desktop-RHS-300",
                                  sizes: [[300, 600]],
                                  sizeMapping: [
                                    {
                                      viewport: [0, 0],
                                      sizes: [[300, 600]],
                                    },
                                  ],
                                  minWidth: 1024,
                                  maxWidth: Infinity,
                                },
                                {
                                  adUnit: "/23290324739/THRI-Desktop-RHS-300",
                                  sizes: [[300, 600]],
                                  sizeMapping: [
                                    {
                                      viewport: [1023, 0],
                                      sizes: [[300, 600]],
                                    },
                                  ],
                                  minWidth: 768,
                                  maxWidth: 1023,
                                },
                                // {
                                //   adUnit: "/23290324739/THRI-Desktop-RHS-300",
                                //   sizes: [[300, 600]],
                                //   sizeMapping: [
                                //     {
                                //       viewport: [767, 0],
                                //       sizes: [[300, 600]],
                                //     },
                                //   ],
                                //   minWidth: 0,
                                //   maxWidth: 767,
                                // },
                              ]}
                              targeting={{
                                section: [router?.query?.category || null],
                                "sub-section": [
                                  router?.query?.subcategory || null,
                                ],
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </section>
                    <Ads
                      id={`div-gpt-ad-stories-bottom-${_id}`}
                      adUnits={[
                        {
                          adUnit: "/23290324739/THRI-Desktop-Bottom-300",
                          sizes: [[300, 350]],
                          sizeMapping: [
                            {
                              viewport: [0, 0],
                              sizes: [[300, 350]],
                            },
                          ],
                          minWidth: 1024,
                          maxWidth: Infinity,
                        },
                        {
                          adUnit: "/23290324739/THRI-Mobile-Bottom-300",
                          sizes: [[300, 250]],
                          sizeMapping: [
                            {
                              viewport: [1023, 0],
                              sizes: [[300, 250]],
                            },
                          ],
                          minWidth: 768,
                          maxWidth: 1023,
                        },
                        {
                          adUnit: "/23290324739/THRI-Mobile-Bottom-300",
                          sizes: [[300, 250]],
                          sizeMapping: [
                            {
                              viewport: [767, 0],
                              sizes: [[300, 250]],
                            },
                          ],
                          minWidth: 0,
                          maxWidth: 767,
                        },
                      ]}
                      targeting={{
                        section: [router?.query?.category || null],
                        "sub-section": [router?.query?.subcategory || null],
                      }}
                    />
                    {_id.toString() !==
                      stories[stories.length - 1]?._id.toString() && (
                      <NextStorySeparator
                        containerStyle={{ marginBottom: "80px 0px" }}
                      />
                    )}
                  </div>
                );
              }
            )}
          </div>
        </div>
      </Layout>
    </>
  );
};

export default NormalStories;
