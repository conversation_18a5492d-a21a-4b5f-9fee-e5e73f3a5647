.categoryHero {
  /* background: url("https://www.magzine.it/wp-content/uploads/2023/04/THR.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  height: 30vw;
  margin-top: 20px; */
  position: relative;
  /* top: 70px; */
  /* padding-top: 70px; */
}
/* .categoryHero{
  margin-top: 116px;
} */
.categoryHero .heroSection {
  width: 100vw;
  position: relative;
  overflow: hidden;
  height: 35vw;
  background-color: rgba(0, 0, 0, 0.241);
  /* margin-top: 20px; */
}
.categoryHero .heroSection img {
  object-fit: cover;
  object-position: top center;
}
.herocrAbs {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 50%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
  z-index: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}
.herocrAbs .herocrText {
  margin: 20px 60px;
}
.herocrAbs .herocrText h1 {
  font-family: var(--font-family-primary);
  font-size: 5rem;
  color: var(--background-color);
}
.heroImgCont {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.categorySection {
  padding: 50px 0px 50px;
}
.categoryHero .navs {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0px;
  padding: 0px 55px;
  border-bottom: 1px solid var(--primary-color);
}
.categoryHero .navs:not(:focus-within):not(:hover) .navItems {
  color: black;
}
.categoryHero .navs .navItems {
  position: relative;
  cursor: pointer;
  font-size: 16px;
  font-family: var(--font-family-secondary);
  padding: 20px 20px;
  display: flex;
  justify-content: center;
}
.categoryHero .navs .navItems.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  display: flex;
  align-items: center;
  height: 4px;
  border-radius: 7px;
  width: 100%;
  background: var(--primary-color);
  transition: all 0.3s ease;
}
.imageSec {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 7px;
  overflow: hidden;
}
.contentSec {
  padding: 20px 36px;
  text-align: center;
}
.contentSec h3 {
  color: #000;
  font-size: 50px;
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}
.hoverSec:hover.contentSec h3 {
  color: var(--primary-color);
}
.contentSec p {
  color: var(--secondary-color);
  font-size: 22px;
  /* width: 650px; */
  line-height: 1.2;
  margin: 5px auto;
  font-family: "kepler-std-display", serif !important;
  font-weight: 300;
  font-style: normal;
}
.ln-cont .card {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  margin: 20px 0;
  transition: 0.5s ease;
}
.ln-cont .card:hover {
  transform: translateX(5px);
  cursor: pointer;
}
.card {
  background-color: #f3f4f5;
  /* border : 1px solid rgba(0, 0, 0, 0.175); */
  border-radius: 7px;
  padding: 0px;
  margin-bottom: 15px;
  overflow: hidden;
}
.card .image {
  position: relative;
  overflow: hidden;
}
.card .image img {
  width: inherit;
  height: inherit;
  object-fit: cover;
  object-position: left;
}
.card .content {
  padding: 10px 20px;
}
.card .content span.category {
  display: block;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--primary-color);
  font-family: var(--font-family-secondary);
}
.card .content span.timeline {
  display: block;
  font-size: 12px;
  color: var(--secondary-color);
  font-family: var(--font-family-secondary);
}
.card .content p {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  text-align: left;
}
/* Compters and Big Screens Small Changes*/
@media screen and (min-width: 1600px) and (max-width: 1900px) {
}
/* Compters and Big Screens */
@media screen and (min-width: 1600px) {
}
/* Laptop and Tablets Small Changes*/
@media screen and (min-width: 901px) and (max-width: 1200px) {
}

@media screen and (max-width: 900px) {
  .categoryHero .heroSection {
    height: 50vw;
  }
  /* .categoryHero{
  margin-top: 43px;
} */
  .herocrAbs .herocrText {
    margin: 10px 20px;
  }
  .herocrAbs .herocrText h1 {
    font-size: 45px;
  }
}
/* Tablets and Mobiles Small Changes */
@media screen and (min-width: 426px) and (max-width: 900px) {
  .categoryHero {
    /* top: 0px; */
    padding: 0;
  }
}

@media screen and (max-width: 425px) {
  .categorySection {
    padding: 20px 0px 25px;
  }
  .categoryHero {
    /* top: 0px; */
    padding: 0;
  }
}
