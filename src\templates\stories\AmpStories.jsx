import AMPHeader from "@/components/amp/AMPHeader";
import AM<PERSON><PERSON>ero from "@/components/amp/AMPHero";
import AMPNewsArticleSchema from "@/components/amp/AMPNewsArticleSchema";
import AMPReviewSchema from "@/components/amp/AMPReviewSchema";
import AMPReviewWidget from "@/components/amp/AMPReviewWidget";
import AMPMainContent from "@/components/amp/stories/AMPMainContent";
import SiteNavigationSchema from "@/components/seo/SiteNavigationSchema";
import { ampCSS } from "@/utils/Constants";
import { Const } from "@/utils/Constants";
import Head from "next/head";

export const config = { amp: true };

function AmpStories({
  _id,
  data,
  author,
  breadcrumbs,
  latest,
  related,
  tag,
  meta,
  slug,
  nextStories,
}) {
  const storyContent = data && data.content ? JSON.parse(data.content) : "";
  const promotional = data?.isPromotional || false;
  const canonical = `${Const.ClientLink}${slug}`;
  const reviews = data?.reviews ? data.reviews : null;

  const webPageSchemaData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: meta?.title,
    description: meta?.description,
    speakable: {
      "@type": "SpeakableSpecification",
      xpath: ["//title", "//meta[@name='description']/@content"],
    },
    url: meta?.canonical || canonical,
  };

  const newsMediaOrganizationSchemaData = {
    "@context": "https://schema.org",
    "@type": "NewsMediaOrganization",
    name: Const.Brand,
    url: `${Const.ClientLink}/`,
    logo: {
      "@type": "ImageObject",
      url: `${Const.ClientLink}/thr-logo.png`,
    },
    address: {
      "@type": "PostalAddress",
      streetAddress:
        "RPSG Lifestyle Media, Thapar House, 3rd floor, Janpath Lane",
      addressLocality: "New Delhi",
      addressRegion: "India",
      postalCode: "110 001",
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+91–11–23486700",
      contactType: "Customer Service",
      areaServed: "IN",
      availableLanguage: "English",
      hoursAvailable: {
        opens: "09:00",
        closes: "19:00",
      },
    },
    sameAs: [
      "https://www.facebook.com/hollywoodreporterindia",
      "https://www.instagram.com/hollywoodreporterindia/",
      "https://twitter.com/thrindia_",
      "https://www.youtube.com/@HollywoodReporterIndia",
    ],
  };

  const breadcrumb = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        item: {
          "@id": Const.ClientLink,
          name: "Home",
        },
      },
    ],
  };

  if (breadcrumbs && breadcrumbs.length > 0) {
    breadcrumbs.forEach((item, index) => {
      breadcrumb.itemListElement.push({
        "@type": "ListItem",
        position: index + 2,
        item: {
          "@id": Const.ClientLink + item.slug,
          name: item.name,
        },
      });
    });
  }

  // Prepare next page data for amp-next-page - use content-only URLs
  const nextPageData =
    nextStories?.map((story) => ({
      title: story.data?.title || "Untitled",
      url: `${Const.ClientLink}${story.slug}/amp`,
      // url: `${Const.ClientLink}${story.slug}?amp=1`,
      image: story.data?.coverImg || "/default-image.jpg",
    })) || [];

  return (
    <>
      <style jsx>{ampCSS}</style>
      <Head>
        <meta name="description" content={meta?.description || ""} />
        <meta name="keywords" content={meta?.keywords || ""} />
        {meta?.author && <meta name="author" content={meta?.author || ""} />}
        <meta name="publisher" content={Const.Brand} />
        <meta
          name="robots"
          content={
            `${meta?.robots}, max-image-preview:large` ||
            "noindex,nofollow, max-image-preview:large"
          }
        />
        <meta name="google-adsense-account" content="ca-pub-****************" />
        <link rel="canonical" href={meta?.canonical || canonical} />
        {/* OG Tags */}
        <meta property="fb:app_id" content="***************" />
        <meta property="og:locale" content="en_IN" />
        <meta property="og:type" content="article" />
        <meta property="og:title" content={meta?.og?.title || ""} />
        <meta property="og:description" content={meta?.og?.description || ""} />
        <meta property="og:url" content={meta?.canonical || canonical} />
        <meta property="og:site_name" content={Const.Brand} />
        <meta property="og:image" content={meta?.og?.image || ""} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        {/* Twitter Tag */}
        <meta
          name="twitter:card"
          content={meta?.twitter?.card || "summary_large_image"}
        />
        <meta
          name="twitter:title"
          content={meta?.twitter?.title || meta?.title}
        />
        <meta
          name="twitter:description"
          content={meta?.twitter?.description || meta?.description}
        />
        <meta name="twitter:site" content={"@THRIndia_"} />
        <meta name="twitter:image" content={meta?.twitter?.image || ""} />
        <meta name="twitter:creator" content={"@THRIndia_"} />
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon-thr-india-16.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon-thr-india-32.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="192x192"
          href="/favicon-thr-india-192.png"
        />
        <link rel="apple-touch-icon" href="/favicon-thr-india-192.png" />
        <link rel="alternate" hreflang="en-in" href={meta?.canonical} />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(webPageSchemaData),
          }}
        ></script>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(newsMediaOrganizationSchemaData),
          }}
        ></script>
        {/* TODO: Site navigation */}
        <SiteNavigationSchema />
        {/* Breadcrumb Schema */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumb) }}
        ></script>
        <meta charSet="utf-8" />
        <script async src="https://cdn.ampproject.org/v0.js"></script>
        <script
          async
          custom-element="amp-analytics"
          src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"
        ></script>
        <script
          async
          custom-element="amp-next-page"
          src="https://cdn.ampproject.org/v0/amp-next-page-1.0.js"
        ></script>
        <script
          async
          custom-element="amp-ad"
          src="https://cdn.ampproject.org/v0/amp-ad-0.1.js"
        ></script>
        <title>{data?.title}</title>
        {/* <link rel="canonical" href={`${Const.ClientLink}${slug}`} /> */}
        <meta
          name="viewport"
          content="width=device-width,minimum-scale=1,initial-scale=1"
        />
        <style amp-boilerplate="">{`body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`}</style>
        <noscript>
          <style amp-boilerplate="">{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}</style>
        </noscript>
        {/* <style amp-custom="" dangerouslySetInnerHTML={{ __html: ampCSS }} /> */}
      </Head>
      <AMPNewsArticleSchema
        headline={meta?.title || ""}
        datePublished={data?.timestamp || ""}
        dateModified={data?.updatedAt || ""}
        articleSection={breadcrumbs?.[0]?.name || ""}
        keywords={meta?.keywords || []}
        description={meta?.description || ""}
        url={slug}
        content={storyContent || {}}
        author={author?.[0] || {}}
        image={data?.coverImg || ""}
      />
      {(reviews?.title ||
        reviews?.cast ||
        reviews?.director ||
        reviews?.runtime) && (
        <AMPReviewSchema
          name={reviews?.title || ""}
          headline={data?.title || ""}
          description={meta?.description || ""}
          image={data?.coverImg || ""}
          duration={reviews?.runtime || 0}
          director={reviews?.director || ""}
          author={author?.[0] || {}}
          actor={reviews?.cast || ""}
          url={slug}
          datePublished={data?.timestamp || ""}
          dateModified={data?.updatedAt || ""}
        />
      )}
      <AMPHeader />
      <div style={{ height: "60px", width: "100%" }} next-page-hide=""></div>
      <article className="story-container">
        <AMPHero
          _id={_id}
          breadcrumbs={breadcrumbs}
          title={data?.title ?? ""}
          description={data?.excerpt ?? ""}
          author={author ?? []}
          timeline={data?.updatedAt ?? ""}
          coverImage={data?.coverImg ?? ""}
          caption={data?.caption ? data?.caption : ""}
          courtesy={data?.courtesy ? data?.courtesy : ""}
          promotional={promotional}
          contributor={
            data?.contributor && data?.contributor.length > 0
              ? data?.contributor
              : []
          }
          altName={data?.altName ? data?.altName : ""}
          readTime={data?.readTime ? data?.readTime : 5}
          articleUrl={`${Const.ClientLink}${slug}`}
          isPromotional={promotional}
          reviews={data?.reviews ? data.reviews : null}
        />
        <section id="storiessection" className="storiesSection">
          <div className="container">
            <div className="row">
              <div className="col-md-8">
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <AMPReviewWidget data={reviews} />
                </div>
                <AMPMainContent
                  _id={_id}
                  content={storyContent}
                  note={data?.duplicationNote ?? ""}
                  tag={tag}
                  isPromotional={promotional}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Next Story Separator */}
        <div className="nextStoryContainer">
          <h2 className="nextStoryTitle">Next Story</h2>
          <div className="nextStoryLine"></div>
        </div>
      </article>
      <amp-analytics type="gtag" data-credentials="include">
        <script
          type="application/json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              vars: {
                gtag_id: "G-5TH270W358",
                config: {
                  "G-5TH270W358": {
                    groups: "default",
                    page_location: `${Const.ClientLink}${slug}/amp`,
                  },
                },
              },
              triggers: {
                trackPageview: {
                  on: "visible",
                  request: "pageview",
                },
              },
            }),
          }}
        />
      </amp-analytics>
      {/* AMP Next Page Configuration */}
      {nextPageData.length > 0 && (
        <amp-next-page>
          <script
            type="application/json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(nextPageData),
            }}
          />
        </amp-next-page>
      )}
    </>
  );
}

export default AmpStories;
