import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { BsArrowDown } from "react-icons/bs";
import { BsArrowUp } from "react-icons/bs";

const TopStreaming = ({ title, description, data }) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleSummary = (index) => {
    setOpenIndex((prevIndex) => (prevIndex === index ? null : index));
  };
  return (
    <>
      <div className="container">
        <div className="container-padding">
          <div className="Chart--heading-cntr">
            <h2 className="container-split">{title ?? ""}</h2>
            <p className="chart--small--title">{description ?? ""}</p>
          </div>
          {/* <div className="column-grid tablet--smaller is--flims">
            <div className="flims-list__title">rank</div>
            <div className="flims-list__title">Name</div>
            <div className="flims-list__title"></div>
            <div className="flims-list__title">Platform</div>
            <div className="flims-list__title">Views</div>
          </div> */}
          <div className="w-dyn-list">
            <div role="list" className="flims-list w-dyn-items">
              {data.map((item, index) =>
                item?.slug ? (
                  <Link
                    target="_blank"
                    href={item?.slug ?? "#"}
                    role="listitem"
                    className="flims-item list--view w-dyn-item"
                    key={`top-streaming-${index}`}
                  >
                    <div className="line-w">
                      <div className="line-inner" />
                    </div>
                    <div className="flims-item__inner w-inline-block" title="">
                      <div className="column-grid tablet--smaller is--flims streaming">
                        <span className="rank--text">{index + 1}</span>
                        <div className="flims__left">
                          <div className="flims__col--img__w">
                            <Image
                              loading="lazy"
                              src={item?.coverImg ?? ""}
                              alt={item?.altName ?? ""}
                              width={500}
                              height={300}
                            />
                          </div>
                          <div className="flim--text-cntr">
                            <h3 className="text--large is--caps">
                              {item?.articleTitle ?? ""}
                            </h3>
                            {/* <h5 className="text--small is--caps">
                              {item?.articleSubheading ?? ""}
                            </h5> */}
                          </div>
                        </div>
                        <div className="text--font--size text-weekend">
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.format ?? ""}
                          </p>
                          {item?.format && (
                            <p className="subhead-charts">Format</p>
                          )}
                        </div>
                        <div className="text--font--size text-weekend">
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.platform ?? ""}
                          </p>
                          {item?.platform && (
                            <p className="subhead-charts">Platform</p>
                          )}
                        </div>
                        <div
                          className="text--font--size text-cumulative text-responsive"
                          style={{
                            // display: "flex",
                            flexDirection: "column",
                            alignItems: "flex-end",
                            color: "#db242a",
                          }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.views ?? ""}
                          </p>
                          {item?.views && (
                            <p className="subhead-charts">Views</p>
                          )}
                        </div>
                        <div
                          className="down-arrow"
                          onClick={(event) => {
                            event.preventDefault();
                            toggleSummary(index);
                          }}
                        >
                          {openIndex === index ? (
                            <BsArrowUp />
                          ) : (
                            <BsArrowDown />
                          )}
                        </div>
                      </div>
                      {openIndex === index && (
                        <div className="summary">
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title">platform</div>
                            <div className="text-week font--size">
                              {item?.platform ?? ""}
                            </div>
                          </div>

                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title">Views</div>
                            <div className="text-week font--size">
                              {item?.views ?? ""}
                            </div>
                          </div>
                          <img
                            loading="lazy"
                            alt={item?.altName ?? ""}
                            src={item?.coverImg ?? ""}
                          />
                        </div>
                      )}
                    </div>
                  </Link>
                ) : (
                  <div
                    role="listitem"
                    className="flims-item list--view w-dyn-item chart-div"
                    key={`top-streaming-${index}`}
                  >
                    <div className="line-w">
                      <div className="line-inner" />
                    </div>
                    <div className="flims-item__inner w-inline-block" title="">
                      <div className="column-grid tablet--smaller is--flims streaming">
                        <span className="rank--text">{index + 1}</span>
                        <div className="flims__left">
                          <div className="flims__col--img__w">
                            <Image
                              loading="lazy"
                              src={item?.coverImg ?? ""}
                              alt={item?.altName ?? ""}
                              width={500}
                              height={300}
                            />
                          </div>
                          <div className="flim--text-cntr">
                            <h3 className="text--large is--caps">
                              {item?.articleTitle ?? ""}
                            </h3>
                            {/* <h5 className="text--small is--caps">
                              {item?.articleSubheading ?? ""}
                            </h5> */}
                          </div>
                        </div>
                        <div className="text--font--size text-weekend">
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.format ?? ""}
                          </p>
                          {item?.format && (
                            <p className="subhead-charts">Format</p>
                          )}
                        </div>
                        <div className="text--font--size text-weekend">
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.platform ?? ""}
                          </p>
                          {item?.platform && (
                            <p className="subhead-charts">Platform</p>
                          )}
                        </div>
                        <div
                          className="text--font--size text-cumulative"
                          style={{
                            // display: "flex",
                            flexDirection: "column",
                            alignItems: "flex-end",
                            color: "#db242a",
                          }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.views ?? ""}
                          </p>
                          {item?.views && (
                            <p className="subhead-charts">Views</p>
                          )}
                        </div>
                        <div
                          className="down-arrow"
                          onClick={(event) => {
                            event.preventDefault();
                            toggleSummary(index);
                          }}
                        >
                          {openIndex === index ? (
                            <BsArrowUp />
                          ) : (
                            <BsArrowDown />
                          )}
                        </div>
                      </div>
                      {openIndex === index && (
                        <div className="summary">
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title">platform</div>
                            <div className="text-week font--size">
                              {item?.platform ?? ""}
                            </div>
                          </div>

                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title">Views</div>
                            <div className="text-week font--size">
                              {item?.views ?? ""}
                            </div>
                          </div>
                          <img
                            loading="lazy"
                            alt={item?.altName ?? ""}
                            src={item?.coverImg ?? ""}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TopStreaming;
