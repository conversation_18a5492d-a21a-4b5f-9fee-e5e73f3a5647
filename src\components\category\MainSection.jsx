import React, { useEffect } from "react";
import Image from "next/image";
import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Link from "next/link";
import { formatDateAndTime, getAuthorText } from "@/utils/Util";
import SponsoredTag from "../common/SponsoredTag";

const MainSection = ({ data, hasMore, handleShowMore }) => {
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent",
        start: "top 60%",
        // markers: true,
      },
    });
  }, []);

  return (
    <>
      {data && data.length > 0 ? (
        <>
          <div className="row">
            <div className="col-md-12 zoom-cont">
              <Link href={data[0]?.slug ?? "/"}>
                <div className="image-sec image-zoom-parent pos-rel">
                  <Image
                    className="image-zoom imgcover"
                    src={
                      data[0]?.croppedImg
                        ? data[0]?.croppedImg
                        : data[0]?.coverImg
                        ? data[0]?.coverImg
                        : ""
                    }
                    alt={data[0]?.altName ?? ""}
                    fill
                  />
                </div>
                <div className="content-sec">
                  <span className="category-span">
                    {data && data[0]?.category ? data[0]?.category : ""}
                  </span>
                  <h3 className="h3_formob">{data[0]?.title ?? ""}</h3>
                  <p>{data[0]?.excerpt ?? ""}</p>
                  <span className="author-span">
                    {getAuthorText(
                      "BY",
                      data?.[0]?.author,
                      data?.[0]?.contributor
                    )}
                  </span>
                </div>
              </Link>
            </div>
          </div>
          <div className="row secmt ln-count">
            <div className="col-md-12">
              {data?.map((item, index) => {
                if (index !== 0) {
                  return (
                    <Link
                      key={`category-item-${index}`}
                      className="cat-card"
                      href={item && item.slug ? item.slug : "/"}
                    >
                      <div className="image">
                        <Image
                          className="imgcover"
                          src={
                            item?.croppedImg
                              ? item?.croppedImg
                              : item?.coverImg
                              ? item?.coverImg
                              : ""
                          }
                          alt={item && item.altName ? item.altName : ""}
                          fill
                        />
                      </div>
                      <div className="content">
                        <div className="d-flex flex-columnn align-items-center gap-3 mb-1">
                          <span className="category">
                            {item && item.category ? item.category : ""}
                          </span>
                          <span className="timeline">
                            {item && item.timeline
                              ? formatDateAndTime(item?.timestamp)
                              : ""}
                          </span>
                        </div>
                        <h2>{item && item.title ? item.title : ""}</h2>
                        <p>{item && item.excerpt ? item.excerpt : ""}</p>
                        <span className="author">
                          {item?.isPromotional && (
                            <SponsoredTag
                              customStyle={{
                                display: "inline",
                                marginRight: "5px",
                              }}
                            />
                          )}
                          {getAuthorText("BY", item?.author, item?.contributor)}
                        </span>
                      </div>
                    </Link>
                  );
                }
              })}
            </div>
            {hasMore && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <button
                  className="btn btn-outline out-btn4"
                  style={{ width: "118px" }}
                  onClick={handleShowMore}
                >
                  <div className="middle-btn"></div>
                  Show More
                </button>
              </div>
            )}
          </div>
        </>
      ) : (
        <>
          <div className="row">
            <div className="col-md-12">
              <h4>No stories to show</h4>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default MainSection;
