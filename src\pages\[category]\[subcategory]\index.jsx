import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Hero from "@/components/category/Hero";
import MainSection from "@/components/category/MainSection";
import LatestSection from "@/components/category/LatestSection";
import style from "@/components/category/Category.module.css";
import {
  getCategory,
  getCategoryLatest,
  getSubmenus,
  getCategoryVideos,
} from "@/pages/api/CategoryApi";
import { Const } from "@/utils/Constants";
// import LeaderboardAd from "@/components/ads/LeaderboardAd";
// import WrappedBanner2024 from "@/components/wrapped/WrappedBanner2024";
import { Ads } from "@/components/ads/Ads";
// import TopVideos from "@/components/category/TopVideos";
// import Shorts from "@/components/category/Shorts";

const Subcategory = ({
  initialData,
  initialCount,
  initialLatestArticle,
  initialSubmenus,
  initialTopVideos,
  initialShortsVideos,
  breadcrumbs,
  coverImg,
  meta,
  title,
}) => {
  const router = useRouter();
  const [data, setData] = useState(initialData);
  const [count, setCount] = useState(initialCount);
  const [latestArticleList, setLatestArticleList] = useState(
    initialLatestArticle?.data ?? []
  );
  const [offset, setOffset] = useState(Const.Offset);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    setData(initialData);
    setCount(initialCount);
    setLatestArticleList(initialLatestArticle?.data ?? []);
    setOffset(Const.Offset);
    setHasMore(initialCount > initialData.length);
  }, [router.query.subcategory, initialData, initialLatestArticle]);

  const handleShowMore = async () => {
    const newOffset = offset + Const.Limit;
    setOffset(newOffset);

    const payload = {
      slug: `/${router.query.category}/${router.query.subcategory}`,
      limit: Const.Limit,
      offset: newOffset,
    };

    try {
      const response = await getCategory(payload);
      if (response?.data?.data.length === 0) {
        setHasMore(false);
        return;
      }
      setData((prev) => [...prev, ...response?.data?.data]);
      setCount(response?.data?.count);
      setHasMore(response?.data?.count > newOffset + Const.Limit);
    } catch (e) {
      console.error("Error fetching more categories:", e);
    }
  };

  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema itemList={breadcrumbs?.slice(0, 1) ?? []} />
      <Hero title={title} coverImg={coverImg} submenus={initialSubmenus} />
      <Ads
        id="div-gpt-ad-subcategory-top"
        style={{margin: "20px auto"}}
        adUnits={[
          {
            adUnit: "/23290324739/THRI-Desktop-Top-970",
            sizes: [[970, 90]],
            sizeMapping: [
              {
                viewport: [0, 0],
                sizes: [[970, 90]],
              },
            ],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Top-320",
            sizes: [[320, 50]],
            sizeMapping: [
              {
                viewport: [1023, 0],
                sizes: [[320, 50]],
              },
            ],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Top-320",
            sizes: [[320, 50]],
            sizeMapping: [
              {
                viewport: [767, 0],
                sizes: [[320, 50]],
              },
            ],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
        targeting={{
          section: [router?.query?.category || null],
          "sub-section": [router?.query?.subcategory || null],
        }}
      />
      {/* <WrappedBanner2024 /> */}
      <section id="all-tab" className={style.categorySection}>
        <div className="container pb-4">
          <div className="row">
            <div className="col-md-12 col-lg-7 col-xl-8">
              <MainSection
                data={data}
                handleShowMore={handleShowMore}
                hasMore={hasMore}
              />
            </div>
            <div className="col-md-12 col-lg-5 col-xl-4 ln-cont">
              {/* <LeaderboardAd
                adUnits={[
                  {
                    adImagePath: "/medium-rectangle-ad.png",
                    adSize: [300, 250],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adImagePath: "/medium-rectangle-ad.png",
                    adSize: [300, 250],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adImagePath: "/medium-rectangle-ad.png",
                    adSize: [300, 250],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
              /> */}
              <Ads
                id={`div-gpt-ad-subcategory-rhs-300x250`}
                style={{ marginBottom: "20px" }}
                adUnits={[
                  {
                    adUnit: "/23290324739/THRI-Desktop-RHS-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.query?.category || null],
                  "sub-section": [router?.query?.subcategory || null],
                }}
              />
              <LatestSection data={latestArticleList} />
              {/* <LeaderboardAd
                adUnits={[
                  {
                    adImagePath: "/leaderboard-ad.png",
                    adSize: [300, 600],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adImagePath: "/leaderboard-ad.png",
                    adSize: [300, 600],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adImagePath: "/leaderboard-ad.png",
                    adSize: [300, 600],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
              /> */}
              <Ads
                id={`div-gpt-ad-subcategory-rhs-300x600`}
                adUnits={[
                  {
                    adUnit: "/23290324739/THRI-Desktop-RHS-300",
                    sizes: [[300, 600]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[300, 600]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Bottom-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/THRI-Mobile-Bottom-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.query?.category || null],
                  "sub-section": [router?.query?.subcategory || null],
                }}
              />
            </div>
          </div>
        </div>
        {/* <LeaderboardAd
          adUnits={[
            {
              adImagePath: "/ad1 (1).jpg",
              adSize: [970, 250],
              minWidth: 1024,
              maxWidth: Infinity,
            },
            {
              adImagePath: "/ad1 (1).jpg",
              adSize: [970, 250],
              minWidth: 768,
              maxWidth: 1023,
            },
            {
              adImagePath: "/leaderboard-ad.png",
              adSize: [300, 250],
              minWidth: 0,
              maxWidth: 767,
            },
          ]}
        /> */}
      </section>
      {/* <TopVideos data={initialTopVideos.data} />
      <Shorts data={initialShortsVideos.data} /> */}
    </Layout>
  );
};

export default Subcategory;

export async function getServerSideProps(context) {
  const url = `/${context.params.category}/${context.params.subcategory}`;
  const payload = {
    slug: url,
    limit: Const.Limit,
    offset: Const.Offset,
  };

  try {
    const [
      categoryRes,
      submenusRes,
      latestArticleRes,
      topVideosRes,
      shortsVideosRes,
    ] = await Promise.all([
      getCategory(payload),
      getSubmenus(url),
      getCategoryLatest(url),
      getCategoryVideos({
        slug: url,
        limit: Const.Limit,
        offset: Const.Offset,
        shorts: false,
      }),
      getCategoryVideos({
        slug: url,
        limit: Const.Limit,
        offset: Const.Offset,
        shorts: true,
      }),
    ]);

    if (!categoryRes?.data?.isExists) {
      return { notFound: true };
    }

    return {
      props: {
        initialData: categoryRes?.data?.data ?? [],
        initialCount: categoryRes?.data?.count ?? 0,
        initialLatestArticle: latestArticleRes?.data ?? { count: 0, data: [] },
        initialSubmenus: submenusRes?.data?.submenus ?? [],
        initialTopVideos: topVideosRes?.data ?? { count: 0, data: [] },
        initialShortsVideos: shortsVideosRes?.data ?? { count: 0, data: [] },
        breadcrumbs: categoryRes?.data?.breadcrumbs || [],
        coverImg: categoryRes?.data?.image ?? "",
        meta: categoryRes?.data?.meta ?? {},
        title: submenusRes?.data?.title ?? "",
      },
    };
  } catch (error) {
    console.error("Error fetching category data:", error);
    return { notFound: true };
  }
}
