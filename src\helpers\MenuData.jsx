export const menus = [
  {
    name: "REVIEWS",
    link: "/reviews",
    submenus: [
      {
        name: "ALL",
        link: "/reviews",
      },
      {
        name: "STREAMING",
        link: "/reviews/streaming",
      },
      {
        name: "THEATRICAL",
        link: "/reviews/theatrical",
      }
    ],
  },
  {
    name: "FEATURES",
    link: "/features",
    submenus: [
      {
        name: "ALL",
        link: "/features",
      },
      {
        name: "INTERVIEWS",
        link: "/features/interviews",
      },
      {
        name: "RAMBLING REPORTER",
        link: "/features/rambling-reporter",
      },
      {
        name: "COLUMNS",
        link: "/features/columns",
      },
      {
        name: "INSIGHT",
        link: "/features/insight",
      },
    ],
  },
  {
    name: "LIFESTYLE",
    link: "/lifestyle",
    submenus: [
      {
        name: "ALL",
        link: "/lifestyle",
      },
      {
        name: "FASHION",
        link: "/lifestyle/fashion",
      },
      // {
      //   name: "BEAUTY",
      //   link: "/lifestyle/beauty",
      // },
      {
        name: "FOOD",
        link: "/lifestyle/food",
      },
      {
        name: "TRAVEL",
        link: "/lifestyle/travel",
      },
      {
        name: "INTERIORS",
        link: "/lifestyle/interiors",
      },
    ],
  },
  {
    name: "GLOBAL",
    link: "/global",
    submenus: [
      {
        name: "ALL",
        link: "/global",
      },
      {
        name: "NEWS",
        link: "/global/news",
      },
      {
        name: "INTERVIEWS",
        link: "/global/interviews",
      },
      {
        name: "REVIEWS",
        link: "/global/reviews",
      },
    ],
  },
  {
    name: "LISTS",
    link: "/lists",
    submenus: [
      // {
      //   name: "ALL",
      //   link: "/lists",
      // },
      // {
      //   name: "TOP 10",
      //   link: "/lists/top-10",
      // },
      // {
      //   name: "FAVOURITES",
      //   link: "/lists/favourites",
      // },
    ],
  },
  {
    name: "CHARTS",
    link: "/charts",
    submenus: [],
  },

  {
    name: "THR VIDEO",
    link: "/thr-video",
    submenus: [],
  },
];
