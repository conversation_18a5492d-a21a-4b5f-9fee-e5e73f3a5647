import React from "react";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import TopSection from "@/components/thrtv/TopSection";
import Youtube from "@/components/thrtv/Youtube";
import { getFlaggedPage } from "@/pages/api/HomeApi";
import {
  getVideoRecent,
} from "@/pages/api/VideoApi";

const THRVideo = ({ meta, top, mostRecent, sections }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <TopSection main={top} mostRecent={mostRecent} />
      {sections &&
        sections.length > 0 &&
        sections?.map((section, index) => {
          if(section.stories.length > 0)
          return (
            <>
              <Youtube
                key={`thr-videos-sec-${index}`}
                heading={section?.title ?? ""}
                data={section?.stories ?? []}
                seeAll={section?.seeAll ?? "/thr-video"}
              />
            </>
          );
        })}
    </Layout>
  );
};

export default THRVideo;

export async function getServerSideProps() {
  try {
    const [response, sectionRes] = await Promise.all([
      getVideoRecent(),
      getFlaggedPage("/thr-video"),
    ]);
    if (response.status !== "success") {
      return {
        props: {
          notFound: true,
        },
      };
    }

    const { data } = response;
    const top = data && data.length > 0 ? data[0] : {};
    const mostRecent = data && data.length > 0 ? data.slice(1) : [];
    const { sections, meta } = sectionRes.data;
    return {
      props: {
        top: top,
        mostRecent: mostRecent,
        sections: sections && sections.length > 0 ? sections : [],
        meta: meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        top: {},
        mostRecent: [],
        sections: [],
        meta: {
          title: "Original THR Videos | The Hollywood Reporter India",
          description:
            "Watch the latest THR review videos, THR Talks videos, Roundtable videos, In Focus with THR videos and more from The Hollywood Reporter India.",
          keywords: [],
          robots: "index,follow",
        },
      },
    };
  }
}
