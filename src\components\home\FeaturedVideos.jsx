import React,{useEffect, useRef} from "react";
import Link from "next/link";
import style from "./Home.module.css";
import { HiOutlineDotsVertical } from "react-icons/hi";
import Image from "next/image";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "../common/SlideNavButton";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import "swiper/css";
import "swiper/css/navigation";

const FeaturedVideos = ({ data }) => {
  const swiperReffv = useRef(null);
  const swiperClass = 'featured-videos-swiper';

  useEffect(() => {
    if (swiperReffv.current && swiperReffv.current.swiper) {
      const swiper = swiperReffv.current.swiper;
  
      const handleSlideChange = () => {
        const bullets = document.querySelectorAll(`.${swiperClass} .swiper-pagination-bullet`);
        
        bullets.forEach((bullet, index) => {
          bullet.classList.remove('expanded');
          
          if (index === swiper.realIndex) {
            setTimeout(() => {
              bullet.classList.add('expanded');
            }, 50);
          }
        });
      };
  
      swiper.on('slideChange', handleSlideChange);
  
      handleSlideChange();
  
      return () => {
        swiper.off('slideChange', handleSlideChange);
      };
    }
  }, []);
  return (
    <>
      {data && data.length > 0 && (
        <section id="featuredvideo" className={style.featuredVideo}>
          <div className="container">
            <div className="row-featurevideo">
              <div>
                <h2 style={{ marginBottom: "0px !important" }}>
                  Featured Videos
                </h2>
              </div>
              <div>
                <SlideNavButton
                  prev={"featuredVideoPrev"}
                  next={"featuredVideoNext"}
                />
              </div>
            </div>
            <div className={style.featureVideoRow}>
              <Swiper
              ref={swiperReffv}
              className={swiperClass}
                autoplay={{
                  delay: 2500,
                  disableOnInteraction: false,
                }}
                pagination={{
                  clickable: true,
                }}
                modules={[Navigation, Pagination, Keyboard]}
                spaceBetween={20}
                slidesPerView={5}
                navigation={{
                  prevEl: ".featuredVideoPrev",
                  nextEl: ".featuredVideoNext",
                }}
                loop={true}
                breakpoints={{
                  320: {
                    slidesPerView: 1,
                    spaceBetween: 10,
                  },
                  480: {
                    slidesPerView: 2,
                    spaceBetween: 10,
                  },
                  768: {
                    slidesPerView: 3,
                    spaceBetween: 15,
                  },
                  1024: {
                    slidesPerView: 5,
                    spaceBetween: 20,
                  },
                }}
              >
                {data.map((item, i) => (
                  <SwiperSlide key={`featured-videos-${i}`}>
                    <div className="papa-div-fv">
                    <div className="feature-video-master">
                      <Link href={item?.slug ?? "#"}>
                        <div
                          className="feature-video-wrapper"
                          style={{ borderRadius: "10px", position:'relative' }}
                        >
                          <Image
                            className={style.featuredVideoSingle}
                            src={item?.coverImg ?? ""}
                            fill
                          />
                          {/* <video
                            ref={(el) => (videoRefs.current[i] = el)}
                            src={el.videosrc}
                            className={style.featuredVideoSingle}
                            loop
                            muted
                            playsInline
                          /> */}
                        </div>
                        <div className="fv-title-wrapper">
                          <div className="hcr-text">
                            <div className="fv-title">
                              Janhvi Kapoor, continues to enthrall her fans with
                              her impeccable fashion choices.
                            </div>
                            
                          </div>
                        </div>
                      </Link>
                    </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
            <hr />
          </div>
        </section>
      )}
    </>
  );
};

export default FeaturedVideos;
