{"name": "thr-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@gsap/react": "^2.1.2", "@tiptap/react": "^2.6.6", "bootstrap": "^5.3.3", "critters": "^0.0.25", "crypto-js": "^4.2.0", "gsap": "npm:@gsap/business@^3.12.7", "html-react-parser": "^5.1.10", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "next": "14.1.1", "react": "^18", "react-bootstrap": "^2.10.1", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-outside-click-handler": "^1.3.0", "react-player": "^2.16.0", "react-social-media-embed": "^2.5.18", "swiper": "^11.0.7"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.1.1"}}