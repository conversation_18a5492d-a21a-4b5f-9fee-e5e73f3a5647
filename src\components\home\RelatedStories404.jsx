import React from "react";
import Link from "next/link";
import Image from "next/image";
import style from "./Home.module.css";
import { formatDateAndTime, getAuthorText } from "@/utils/Util";

const ReadNow = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <section
          id="readnow"
          className={style.readNow}
          style={{ marginBottom: "60px" }}
        >
          <div className="container">
            {/* <div className="row">
                        <div className="col-md-12 d-flex align-items-start justify-content-between">
                            <h2>Read Now</h2>
                            <Link href={""} className="btn btn-outline out-btn">
                                <div className="hover-fixed">
                                    <div className="middle-btn"></div>
                                    SEE ALL
                                </div>
                            </Link>
                        </div>
                    </div> */}

            <div className="row scrollable-div">
              {data.map((item, i) => {
                return (
                  <>
                    <div className="col-md-3 rn-card" key={`read-${i}`}>
                      <Link href={item?.slug ?? "#"}>
                        <div className="card-wrapper">
                          <span className="category">
                            {item?.category ?? ""}
                          </span>
                          <div className="feature-box image-div topstory-scale">
                            <Image
                              src={item?.coverImg ?? ""}
                              fill
                              alt={item?.altName ?? "top story"}
                            />
                          </div>
                          <div className="content-sec">
                            <h3 className="card-title">{item?.title ?? ""}</h3>
                            <p className="card-subtitle">
                              {item?.excerpt ?? ""}
                            </p>
                            <span className="author">
                              {getAuthorText(
                                "BY",
                                item?.author,
                                item?.contributor
                              )}
                            </span>
                            <span className="author">
                              {formatDateAndTime(item?.timestamp ?? "")}
                            </span>
                          </div>
                        </div>
                      </Link>
                    </div>
                  </>
                );
              })}
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default ReadNow;
