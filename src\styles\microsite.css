:root {
  --d: 1000ms;
  --e: cubic-bezier(0.19, 1, 0.22, 1);
  /* --font-sans: 'Rubik', sans-serif;
    --font-serif: '<PERSON><PERSON>', serif; */
}
/* newcodewebsite */
:root {
  --black: black;
  --swatch--red: #ff1e00;
  --swatch--padding--main: 30px;
  --swatch--white: white;
  --white: rgb(255 242 221);
  --dark: #171717;
  --swatch--black: #000;
  --swatch--padding--nav-color: var(--white);
}
.text-fly-wrapper div {
  margin: 0 5px;
}
.text_content .paragraphcntr {
  font-family: kepler-std, serif !important;
}
.page-wrapper {
  overflow-x: clip;
  position: relative;
  /* z-index: 999; */
}
.bottom_sound {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9;
  /* margin-bottom: 20px; */
  /* background-color: blue; */
}
.Top_sound {
  position: fixed;
  top: 3%;
  right: 1%;
  /* transform: translate(-50%, -50%); */
  z-index: 999;
}
.bottom_sound div,
.Top_sound div {
  text-align: center;
}
.bottom_sound .volume,
.Top_sound .volume {
  align-items: center;
  background: #000;
  border-radius: 50px;
  display: flex;
  height: 36px;
  justify-content: flex-end;
  margin: 0 auto 15px;
  overflow: hidden;
  padding-right: 7px;
  position: relative;
  transform: translateZ(0);
  width: 88px;
  cursor: pointer;
}
.Top_sound .volume {
  height: 30px;
  width: 80px;
}
.bottom_sound .volume .lines,
.Top_sound .volume .lines {
  align-items: center;
  display: flex;
  height: 100%;
  position: absolute;
  width: 100%;
}
.bottom_sound .volume .lines .off,
.Top_sound .volume .lines .off {
  height: 2px;
  transform: translate(10px);
  transition: opacity 0.3sease-in-out;
  width: 57px;
}
.bottom_sound .volume .lines .on,
.Top_sound .volume .lines .on {
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-name: infinityLineMove;
  animation-timing-function: linear;
  height: 16px;
  left: 0;
  opacity: 0;
  position: absolute;
  transform: translate(10px);
  transition: opacity 0.3sease-in-out;
  width: 57px;
}
.bottom_sound .volume .lines .off.active,
.Top_sound .volume .lines .off.active {
  opacity: 0;
}
.bottom_sound .volume .lines .on.active,
.Top_sound .volume .lines .on.active {
  opacity: 1;
}
@keyframes infinityLineMove {
  0% {
    transform: translate(0);
  }
  100% {
    transform: translate(14px);
  }
}
.bottom_sound .volume .status,
.Top_sound .volume .status {
  position: relative;
  z-index: 2;
}
.bottom_sound .volume .status p,
.Top_sound .volume .status p {
  color: #b6b6b6;
  font-size: 12px;
  margin: auto;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.36px;
  line-height: 1;
  text-align: center;
  text-transform: uppercase;
  display: block;
}
.bottom_sound .volume .back-left,
.Top_sound .volume .back-left {
  background: linear-gradient(90deg, #000 14.85%, transparent 95.83%);
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 36px;
}
.bottom_sound .volume .back-right,
.Top_sound .volume .back-right {
  background: linear-gradient(90deg, #000 63.71%, transparent 95.83%);
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  transform: rotate(180deg);
  width: 49px;
}
.bottom_sound p {
  color: #000;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.54px;
  line-height: 110%;
  text-align: center;
  text-transform: uppercase;
}
.Top_sound p {
  display: none;
}
.hero-section {
  z-index: 2;
  padding-right: var(--swatch--padding--main);
  padding-left: var(--swatch--padding--main);
  pointer-events: auto;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100svh;
  display: flex;
  position: relative;
  overflow: clip;
  z-index: 9;
  background-color: var(--white);
}
.hero-heading-content {
  z-index: 0;
  background-color: var(--swatch--red);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  inset: 0%;
  overflow: hidden;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}
.Rpsgmediatoplogo {
  position: absolute;
  bottom: 10px;
  width: 80px;
  min-width: 80px;
  height: 5vw;
}
.Rpsgmediatoplogo img {
  /* height: 90px;
    width: auto; */
  scale: 1;
  object-fit: contain;
  width: 100%;
  height: 100%;
}
.hero-heading-content.mobile {
  display: none;
}
.main-text-heading {
  perspective: 1000px;
  text-align: center;
  letter-spacing: -0.05em;
  text-transform: uppercase;
  width: 100%;
  max-width: 860px;
  /* max-width: 1040px; */
  /* max-width: 100%; */
  height: auto;
  /* font-size: clamp(1rem, 5vw, 3.2rem); */
  font-size: clamp(2rem, 6vw, 7.2rem);
  line-height: 1.2;
  position: relative;
  transform: perspective(1000px);
}
.splitTextanim {
  font-size: clamp(1rem, 3vw, 2.2rem);
}
.line-parent {
  overflow: clip !important;
}
.dala-span {
  perspective: 1000px;
  /* word-spacing: 0.1em; */
  /* font-size: clamp(2rem, 5vw, 3.5rem); */
  font-weight: 700;
  line-height: 1.2;
  display: inline-block;
  transform: perspective(1000px);
}
/* .w-inline-block {
    max-width: 100%;
    display: inline-block;
  }
  .snw-chevron {
    border-bottom: 2px solid #000;
    border-right: 2px solid #000;
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 60px;
    transform: rotate(45deg);
  } */
.text-section {
  background-color: var(--white);
  text-align: center;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 500svh;
  display: flex;
  position: relative;
  overflow-x: clip;
  z-index: 2;
}
.image-fly-in-wrapper {
  perspective: 1000px;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 200vh;
  margin-bottom: 0;
  display: flex;
  position: absolute;
  bottom: 0;
  transform: perspective(1000px);
}
.image-fly-in-wrapper.old {
  display: none;
}
.fly-in-image.one {
  width: 70px;
  top: 16%;
  left: 33%;
}
.fly-in-image.two {
  width: 78px;
  top: 22%;
  left: 61%;
}
.fly-in-image.three {
  width: 97px;
  top: 39%;
  right: 14%;
}
.fly-in-image.four {
  cursor: pointer;
  width: 136px;
  bottom: 18%;
  left: 50%;
}
.fly-in-image.five {
  width: 75px;
  bottom: 22%;
  left: 25%;
}
.fly-in-image.six {
  width: 111px;
  top: 42%;
  left: 14%;
}
.w-background-video {
  color: #fff;
  height: 500px;
  position: relative;
  overflow: hidden;
}
.hero-video {
  z-index: 1;
  pointer-events: none;
  width: 100%;
  height: 100%;
  position: absolute;
  will-change: scale;
}
audio,
canvas,
progress,
video {
  vertical-align: baseline;
  display: inline-block;
}
.w-background-video > video {
  object-fit: contain;
  z-index: -100;
  background-position: 50%;
  background-size: cover;
  width: 100%;
  height: 100%;
  margin: auto;
  position: absolute;
  inset: -100%;
}
.image-fly-in-wrapper.back {
  z-index: 0;
}
.fly-in-list-item {
  display: flex;
  position: absolute;
}
[data-slug="magna-labore-ipsum"] {
  width: 24vw;
  height: 15vw;
  top: 46%;
  left: 15%;
}
.w-embed:before,
.w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}
.w-embed:before,
.w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}
.w-embed:after {
  clear: both;
}
.fly-in-image {
  width: 100%;
  height: 100%;
  position: absolute;
}
.fly-in-tag {
  aspect-ratio: auto;
  transform-origin: 0 100%;
  object-fit: cover;
  width: 100%;
  height: 100%;
  margin-bottom: 0.5rem;
  transition: all 0.25scubic-bezier (0.215, 0.61, 0.355, 1);
  display: block;
  position: relative;
  transform: translate(-50%);
}
[data-slug="adipiscing-amet-magna-ipsum"] {
  width: 9vw;
  height: 11vw;
  top: 29%;
  left: 35%;
}
[data-slug="drones"] {
  width: 13vw;
  height: 20vh;
  top: 94%;
  left: 49%;
}
[data-slug="sed-lorem-ipsum"] {
  width: 15vw;
  height: 20vw;
  top: 78%;
  left: 78%;
}
.video-pin-wrapper {
  z-index: 1;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100svh;
  transition: background-color 0.25scubic-bezier (0.215, 0.61, 0.355, 1);
  display: flex;
  position: absolute;
  top: -100vh;
}
.text-fly-wrapper {
  pointer-events: auto;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
}
.main-text-heading.text-fly-in {
  position: absolute;
  transform: none;
}
.image-fly-in-wrapper.front {
  z-index: 2;
}
[data-slug="consectetur-lorem-sit"] {
  width: 21vw;
  height: 16vw;
  top: 20%;
  left: 83%;
}
[data-slug="dolore-incididunt"] {
  width: 14vw;
  height: 19vw;
  top: 15%;
  left: 10%;
}
[data-slug="elit"] {
  width: 14vw;
  height: 14vw;
  top: 57%;
  left: 90%;
}
[data-slug="eiusmod-consectetur-ut-elit"] {
  width: 7vw;
  height: 13vw;
  top: 39%;
  left: 74%;
}
[data-slug="sed-incididunt"] {
  width: 13vw;
  height: 7vw;
  top: 62%;
  left: 40%;
}
[data-slug="aliqua-ipsum-sit"] {
  width: 11vw;
  height: 13vw;
  top: 84%;
  left: 13%;
}
.video-section-mobile {
  display: none;
}
.mobile-hero-video {
  object-fit: cover;
}
.text-section-mobile {
  display: none;
}
.w-background-video > video {
  object-fit: contain;
  z-index: -100;
  background-position: 50%;
  background-size: cover;
  width: 100%;
  height: 100%;
  margin: auto;
  position: absolute;
  inset: -100%;
}
/* horizontalslider */
.capabilities-section {
  background-color: var(--white);
  pointer-events: auto;
  flex-flow: column;
  /* height: 00vh; */
  padding: 0;
  display: block;
  position: relative;
  /* overflow-x: clip; */
  overflow: hidden;
  /* background-color: green; */
  z-index: 2;
}
.cap-pin {
  flex-flow: column;
  height: 100vh;
  padding: 30px;
  display: flex;
  position: relative;
  z-index: 2;
}
.cap-header {
  flex-flow: column;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding-top: 40px;
  padding-bottom: 40px;
  display: flex;
}
.cap-eyebrow {
  text-align: center;
  text-transform: uppercase;
  font-size: 0.9rem;
  font-weight: 700;
  line-height: 1;
  display: none;
}
.cap-heading {
  text-align: center;
  letter-spacing: -0.05em;
  text-transform: uppercase;
  margin-top: 20px;
  font-size: 2.5rem;
  line-height: 1;
}
.except-for-the-audience {
  opacity: 0.64;
  margin-bottom: 20px;
  font-weight: 500;
}
.cap-frame {
  flex: 0 auto;
  justify-content: flex-start;
  align-items: stretch;
  display: flex;
}
.cap-track {
  flex: none;
  padding-right: 30px;
  display: flex;
}
.cap-content,
.cap-list,
.cap-list-wrapper {
  display: flex;
}
.cap-list-item {
  width: 42vw;
  padding: 0 30px;
  display: flex;
  position: relative;
}
.cap-list-item-stack {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  display: flex;
}
.w-layout-vflex {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}
.cap-list-item-text {
  flex: none;
  width: 100%;
}
.cap-list-num {
  margin-bottom: 1em;
  font-size: 1rem;
  font-weight: 700;
}
.cap-list-heading {
  max-width: 300px;
  margin-bottom: 1em;
  font-size: 1.9rem;
  font-weight: 500;
}
.cap-list-description {
  opacity: 0;
  max-width: 600px;
  margin-bottom: 2em;
  font-size: 1.4rem;
  line-height: 1.4;
}
.cap-list-image {
  aspect-ratio: 4 / 2;
  opacity: 0;
  object-fit: cover;
  flex: 0 auto;
  display: block;
}
.cap-list-item-bar {
  background-color: #00000042;
  width: 1px;
  height: 15%;
  position: absolute;
  inset: 0% auto 0% 0%;
}
/* for mobile code horizontalslider */
.capabilities-mobile {
  background-color: var(--white);
  pointer-events: auto;
  display: none;
  position: relative;
}
.promise {
  font-size: 3.9rem;
}

svg:not(:root) {
  overflow: hidden;
}
html.w-mod-touch * {
  background-attachment: scroll !important;
}
.main-text-heading.heroHeading {
  max-width: 100%;
}
.main-text-heading.heroHeading span {
  line-height: 1;
}
@media screen and (max-width: 767px) {
  .bottom_sound {
    width: 80%;
  }
  .hero-section {
    opacity: 1;
    min-height: 500px;
    padding: 24vw 12vw 12vw;
    display: flex;
  }
  .hero-heading-content {
    z-index: 3;
    padding-left: 13vw;
    padding-right: 13vw;
  }
  .main-text-heading.heroHeading {
    padding-left: 0;
    padding-right: 0;
  }
  .hero-heading-content.mobile {
    display: none;
  }
  .text-fly-wrapper div,
  .split-line div {
    margin: 0 2px !important;
  }
  .main-text-heading {
    font-size: 36px;
  }
  /* .main-text-heading.heroHeading{
      padding-left:0;
      padding-right: 0;
    } */
  .main-text-heading.heroHeading span {
    font-size: 2.2rem;
  }
  .dala-span {
    font-size: 30px;
  }
  /* .hero-video {
      display: none;
    } */
  .text-section {
    height: 500vh;
  }
  .video-pin-wrapper {
    height: 100vh;
  }
  .video-section-mobile {
    aspect-ratio: 2 / 3;
    width: 100%;
    min-height: 500px;
    /* display: flex; */
    position: relative;
    overflow: hidden;
  }
  .mobile-hero-video {
    pointer-events: none;
    object-fit: cover;
    width: 100%;
    height: 100%;
    display: flex;
    position: absolute;
  }
  .text-section-mobile {
    z-index: 1;
    margin-bottom: 0;
    padding: 24vw 5vw 0;
    /* display: flex; */
    position: relative;
  }
  .mobile-text {
    text-align: center;
    letter-spacing: -0.02em;
    text-transform: uppercase;
    font-size: clamp(1rem, 5vw, 6rem);
    font-weight: 700;
    line-height: 1.2;
  }
  .mobile-dala-floda-span {
    font-family: Dala Floda, sans-serif;
    font-size: clamp(1rem, 5.4vw, 6rem);
    line-height: 1;
  }
  /* .text-section {
      display: none;
    } */
  .capabilities-section {
    display: none;
  }

  .capabilities-mobile {
    padding: 60px 20px 100px;
    display: block;
    z-index: 99;
  }

  .cap-eyebrow {
    display: block;
  }
  .cap-header {
    padding-top: 5px;
    padding-bottom: 5px;
  }
  .cap-heading {
    margin-bottom: 60px;
    font-size: 2rem;
    line-height: 1.2;
  }
  .promise {
    font-size: 2.2rem;
    line-height: 0.4;
  }
  .capability-accordions {
    font-size: clamp(0rem, 4.4vw, 1.5rem);
    position: relative;
  }
  .accordion-wrapper {
    grid-column-gap: 0em;
    grid-row-gap: 0em;
    flex-flow: column;
    display: flex;
    position: relative;
  }
  .accordion {
    font-size: inherit;
    border: 0 solid #0003;
    position: relative;
  }
  .accordion-header {
    cursor: pointer;
    font-size: inherit;
    background-color: #efefef00;
    border-bottom: 1px solid #0000003d;
    width: 100%;
    padding: 0.9em 0;
    display: flex;
  }
  .accordion-header-column {
    grid-column-gap: clamp(0px, 5vw, 30px);
    grid-row-gap: clamp(0px, 5vw, 30px);
    flex: 1;
    justify-content: flex-start;
    align-items: center;
    font-family: Gotham, sans-serif;
    display: flex;
  }
  .accordion-number {
    font-size: clamp(0px, 5vw, 12px);
    font-weight: 700;
  }
  .accordion-heading {
    font-weight: 500;
    font-size: inherit;
    text-align: start;
  }
  .accordion-body {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
  }
  .accordion-body[style*="display: block"] {
    max-height: 1000px;
  }
  /* .accordion-body.close {
      height: 0;
    }
    .accordion-body.open {
      height: auto;
    } */
  /* .accordion-header.open + .accordion-body {
      max-height: 1000px; 
      padding-top: 15px;
      padding-bottom: 15px;
    } */

  .accordion-content {
    grid-column-gap: 1em;
    grid-row-gap: 1em;
    flex-flow: column;
    padding: 1em 0;
    display: flex;
  }
  .accordion-description {
    font-size: 1.3rem;
    line-height: 1.3;
  }
  .image-2 {
    aspect-ratio: 3 / 2;
    object-fit: cover;
    width: 100%;
  }
  .accordion-header-icon-image {
    width: 22px;
    height: 22px;
  }
  .accordion-header-icon-image svg {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
  }
  .accordion-header-icon-image.open .accordion-header-icon-image svg {
    transform: rotate(45deg);
  }
  [data-slug="adipiscing-amet-magna-ipsum"] {
    width: 15vw;
    height: 17vw;
    top: 29%;
    left: 35%;
  }
  [data-slug="sed-incididunt"] {
    width: 20vw;
    height: 15vw;
    top: 62%;
    left: 40%;
  }
  [data-slug="aliqua-ipsum-sit"] {
    width: 15vw;
    height: 20vw;
    top: 84%;
    left: 13%;
  }
  [data-slug="eiusmod-consectetur-ut-elit"] {
    width: 15vw;
    height: 16vw;
    top: 39%;
    left: 74%;
  }
}
@media screen and (max-width: 479px) {
  .heroheadingcnt {
    padding-left: 3vw;
    padding-right: 3vw;
  }
  .bottom_sound {
    width: 100%;
  }
  .bottom_sound p {
    font-size: 12px;
    font-weight: 600;
  }
  .main-text-heading {
    max-width: 350px;
    /* font-size: 24px; */
    font-size: clamp(1rem, 5vw, 6rem);
    letter-spacing: -0.02em;
    line-height: 1.2;
  }
  .dala-span {
    font-size: 22px;
  }
  .text-section-mobile {
    margin-bottom: 0;
  }
  .capabilities-mobile {
    padding-bottom: 100px;
    padding-left: 15px;
    padding-right: 15px;
    z-index: 99;
  }
  .cap-heading {
    margin-bottom: 15px;
    font-size: 1.5rem;
  }
  [data-slug="drones"] {
    width: 18vw !important;
    height: 10vh !important;
    top: 94%;
    left: 49%;
  }

  [data-slug="adipiscing-amet-magna-ipsum"] {
    width: 15vw;
    height: 17vw;
    top: 29%;
    left: 35%;
  }
  [data-slug="sed-incididunt"] {
    width: 20vw;
    height: 15vw;
    top: 62%;
    left: 40%;
  }
  [data-slug="aliqua-ipsum-sit"] {
    width: 15vw;
    height: 20vw;
    top: 84%;
    left: 13%;
  }
  [data-slug="eiusmod-consectetur-ut-elit"] {
    width: 15vw;
    height: 16vw;
    top: 39%;
    left: 74%;
  }
}
/* for mobile code horizontalslider */

/* horizontalslider */

/* newcodewebsite */
/* img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.2s;
    user-select: none;
  } */
/* p {
    font-size: 14px;
    font-weight: 600;
    --webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  } */

.Slidercontainer {
  position: relative;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  overflow: hidden;
  background-color: var(--swatch--red);
  /* background: linear-gradient(to bottom, black, #be1211); */
  display: flex;
  /* top: 0;
    left: 0; */
  z-index: 1;
  /* pointer-events: none; */
}
.main-contentcntr {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  /* justify-content: center; */
}
nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 0em;
  display: flex;
  /* grid-template-columns: 1.5fr 2fr; */
  color: #fff;
  /* align-items: center; */
  justify-content: center;
  z-index: 999;
}
.nav_innercntr {
  display: flex;
  /* align-items: center; */
  justify-content: space-between;
}
.nav_innercntr p svg {
  position: absolute;
  right: 1.5em;
  font-size: 50px;
  height: 100%;
  top: 0;
  color: #ff1313;
}
nav img {
  width: 180px;
  filter: brightness(0.1);
}

button {
  /* margin: 2px; */
  /* padding: 5px 1rem; */
  color: rgb(0, 0, 0);
  border: none;
}

.left {
  width: 55%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.img-preview {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* width: 50%; */
  height: 30vw;
  overflow: hidden;
  border-radius: 16px;
  /* aspect-ratio: 3 / 4; */
  aspect-ratio: 1;
}
.img-preview img {
  /* position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); */
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* transition: opacity 0.2s; */
  /* user-select: none; */
}
/* .scroll_indictor {
    position: absolute;
    bottom: 2em;
    color: #fff;
    font-size: 2rem;
    overflow: hidden;
    width: 10ch;
    text-wrap: nowrap;
    animation: typing 1.5s steps(20) infinite alternate-reverse;
  }
  @keyframes typing {
    from {
      width: 0px;
    }
  } */
.minimap {
  position: absolute;
  top: 50%;
  left: 4.6em;
  width: 80px;
}

.indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100px;
  border: 1px solid #ffffff;
  z-index: 2;
  border-radius: 12px;
}
.items {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0px;
  will-change: transform;
  align-items: center;
}
.item {
  width: 100%;
  height: 100px;
  padding: 5px;
  cursor: pointer;
  overflow: hidden;
}
.item img {
  border-radius: 10px;
  opacity: 0.2;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  /* user-select: none; */
}
.item:nth-child(1) img {
  opacity: 1;
}
.item img:hover {
  opacity: 0.6 !important;
}
.right {
  width: 40%;
  height: 100%;
  position: relative;
  display: initial;
  display: flex;
  /* flex-direction: column; */
  justify-content: center;
  align-items: center;
  /* background-color: blue; */
}
.text_content {
  /* position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); */
  width: 100%;
  text-align: center;
  color: #fff;
  /* height: 400px; */
  /* max-height: calc(100% - 400px); */
  /* height: 100%; */
  /* -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none; */
}
.titleheading {
  position: relative;
  margin: 1em 0 1em 0;
  line-height: 1.5;
}
.text_content h2,
.text_content h5 {
  font-size: 2rem;
  /* border-bottom: 1px solid #fff; */
  color: #000;
  /* line-height: 1; */
}
.text_content h5 {
  font-size: 1.3rem;
  font-weight: 600;
  line-height: 1;
}
/* .text_content button {
    margin: 1em 0 2em 0;
    padding: 5px 1rem;
    border: none;
    background-color: transparent;
  } */
/* .text_content button a {
    text-decoration: none;
    color: #000;
    font-size: 1rem;
    color: rgb(255, 255, 255);
  } */
.text_content .paragraphcntr {
  font-size: 1.3rem;
  height: 400px;
  max-height: calc(100% - 400px);
  overflow: auto;
  color: #000;
  font-weight: 100;
  padding: 0 10px;
}
.text_content .paragraphcntr::-webkit-scrollbar {
  width: 1px;
}
.text_content .paragraphcntr::-webkit-scrollbar-thumb {
  background-color: #fff;
}
.split-line {
  overflow: hidden;
}
.split-line div {
  transform: translateY(100%);
  margin: 0 5px;
}
/* .Mobile_sLider_wrapper {
    display: none;
  } */
.triviaCntr {
  /* display: flex; */
  position: relative;
}
.triviaCntr span {
  font-weight: 600;
}
.triviaCntr span img {
  height: auto;
}
.triviapara {
  margin-top: 5px;
  line-height: 1.1;
}
/* .triviapara::first-line {
    line-height: 35px;
  } */
.dala-span span {
  color: #ff1e00 !important;
}
@media (max-width: 1199px) {
  .main-contentcntr {
    padding: 5rem 0;
    /* padding: 0; */
    flex-direction: column;
    gap: 1rem;
    width: 70%;
    margin: 0 auto;
  }
  .left {
    width: 100%;
    height: 50%;
  }
  .img-preview {
    position: relative;
    height: 100%;
    inset: 0;
    transform: none;
    aspect-ratio: 1;
  }
  .right {
    width: 100%;
    height: 50%;
  }
  .text_content {
    position: relative;
    inset: 0;
    transform: none;
  }
  .text_content h2 {
    /* margin: 15px 0; */
    font-size: 1.5rem;
    line-height: 1.2;
  }
  .text_content h5 {
    font-size: 1.2rem;
    line-height: 1.2;
  }
  .text_content .paragraphcntr {
    height: 325px;
    font-size: 1.15rem;
  }
}
@media (max-width: 991px) {
  body {
    overflow-x: clip;
    /* touch-action: none; */
  }
  nav {
    justify-content: center;
    padding: 0.5em;
  }
  nav img {
    width: 130px;
  }
  .nav_innercntr p svg {
    right: 0.5em;
    font-size: 30px;
  }
  .Slidercontainer {
    /* touch-action: none; */
    /* display: none; */
    pointer-events: all;
  }
  .main-contentcntr {
    flex-direction: column;
    padding: 4.5rem 0;
    /* padding: 0; */
    gap: 1rem;
    width: 100%;
  }
  .Mobile_sLider_wrapper {
    z-index: 9;
    background: linear-gradient(to bottom, black, #be1211);
    width: 100vw;
    height: 100vh;
    padding-top: 20vw;
    display: flex;
    flex-direction: column;
    /* justify-content: center; */
    gap: 1rem;
    position: relative;
  }
  .slider-container {
    width: 100%;
    /* height: 500px; */
    height: calc((100vw - 44px - 16px));
    /* height: calc((100vh - 400px - 50px) * 1.42); */
    display: flex;
    justify-content: center;
    position: relative;
  }
  .slide {
    width: 100%;
    /* height: 100%; */
    height: calc((100vw - 44px - 16px));

    border: none;
    transition: all ease 1s;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
  }
  .slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    /* transform: scale(1) !important; */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .slide:nth-child(2n) {
    transform: scale(1) !important;

    background-color: rgb(66, 217, 248);
  }
  .out-of-view {
    width: 0;
    /* border-radius: 0px; */
    /* transition: border-radius ease 0.1s; */
  }
  .small-slide {
    width: 22px;
  }
  .large-slide {
    /* width: calc(100% - 44px - 16px); */
    width: 100%;

    margin: 0 8px;
    transition: all ease 0.5s;
  }
  .mblslide {
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    /* gap: 1rem; */
    padding: 0 1rem;
  }
  .mblslidename {
    position: relative;
  }
  .mblslidename h1 span {
    position: relative;
  }
  .mblslidename h1 span::before {
    content: "";
    border: 1px solid #fff;
    position: absolute;
    left: 0%;
    width: 100%;
    bottom: -1px;
  }
  .mblslidename h5 {
    margin: 10px 0;
    font-size: 1rem;
  }
  .mblslide p {
    font-size: 1rem;
  }
  .mblminimap {
    top: auto;
    right: auto;
    /* bottom: 1em; */
    left: 50%;
    width: 100%;
    height: 50px;
    /* touch-action: none; */
    position: relative;
    transform: translate(-50%, 0);
  }
  /* .mblminimap {
      position: absolute;
      top: 50%;
      left: 4.6em;
      width: 80px;
    } */

  .mblindicator {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 100%;
    border: 1px solid #ffffff;
    z-index: 2;
    border-radius: 12px;
    transform: translate(-50%, -50%);
  }
  .mbl_items {
    width: max-content;
    height: 100%;
    /* touch-action: none; */
    display: flex;
    flex-direction: row;
    gap: 0px;
    will-change: transform;
    align-items: center;
    position: absolute;
    left: calc(50% - 30px);
  }
  .mbl_item {
    width: 60px;
    height: 100%;
    padding: 5px;
    cursor: pointer;
    overflow: hidden;
    position: relative;
  }
  .mbl_item img {
    border-radius: 10px;
    opacity: 1;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    /* user-select: none; */
  }

  /* mbl up */
  .left {
    width: 100%;
    height: 50%;
  }
  .img-preview {
    position: relative;
    inset: 0;
    top: 0;
    transform: none;
    aspect-ratio: 1 / 1;
    height: 100%;
  }
  .right {
    /* display: none; */
    width: 100%;
    height: 45%;
    padding: 0 2rem;
  }
  .text_content h2 {
    font-size: 1.5rem;
    /* margin: 12px 0; */
    margin-bottom: 0;
  }
  .text_content {
    position: relative;
    transform: none;
    left: 0;
    top: 0;
  }
  .text_content .paragraphcntr {
    font-size: 1.1rem;
    height: 200px;
    font-weight: 500;
  }
  .trivaiCntr {
    padding: 5px 0;
  }

  .minimap {
    top: auto;
    right: auto;
    bottom: 1em;
    left: 50%;
    width: auto;
    height: 40px;
    /* touch-action: none; */
  }
  .indicator {
    top: 0;
    left: 0;
    width: 40px;
    height: 100%;
  }
  .items {
    flex-direction: row;
    width: max-content;
    height: 100%;
    /* touch-action: none; */
  }
  .item {
    width: 40px;
    height: 100%;
    padding: 5px;
  }
  /* .img-preview {
      top: 45%;
      width: 75%;
      height: 50%;
    } */
  .scroll_indictor {
    display: none;
  }

  /* .triviaCntr span img {
      height: 28px;
      left: -20px;
      top: -5px;
    }
    .triviapara::first-line {
      line-height: 20px;
    } */
}
@media (max-width: 479px) {
  .right {
    padding: 0 1rem;
  }
  /* .triviaCntr span img {
      height: 25px;
      left: -20px;
      top: 1px;
    }
    .triviapara::first-line {
      line-height: 25px;
    } */
}
@media (orientation: landscape) and (max-width: 767px) {
  .main-contentcntr {
    flex-direction: row;
  }
  .minimap {
    position: absolute;
    /* top: 50%; */
    left: 4.6em;
    width: 80px;
  }
  .items {
    /* flex-direction: column; */
  }
  /* .items {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 0px;
      will-change: transform;
      align-items: center;
    } */
  .left {
    height: 100%;
    width: 45%;
  }
  .right {
    height: 100%;
    width: 55%;
    padding: 0 1em;
    display: flex;
    align-items: center;
  }
  .text_content {
    overflow: hidden;
  }
  .text_content .paragraphcntr {
    height: 150px;
  }
}

/* footercntr */
.FooterCntr {
  width: 100%;
  height: 100dvh;
  clip-path: polygon(0% 0px, 100% 0%, 100% 100%, 0px 100%);
  background-color: rgb(255 242 221);
}
.FooterCntrInner {
  position: relative;
  height: calc(100dvh + 100dvh);
  top: -100dvh;
}
.FooterCntrWrap {
  position: sticky;
  top: calc(100dvh - 100dvh);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100dvh;
}
.FooterCntrImageWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.FooterCntrImages {
  position: absolute;
  top: -100px;
  left: 0;
  right: 0;
  width: 100%;
}
.footerTextCntr {
  position: relative;
  z-index: 5;
  padding-left: 1rem;
  padding-right: 1rem;
}
.footerTextCntr h1 {
  font-weight: 600;
  letter-spacing: -0.02em;
  font-size: 3rem;
  text-align: center;
  /* font-size: 80px; */
  line-height: 1;
}
.footerTextCntr p {
  z-index: 5;
  margin-top: 30px;
  text-align: center;
  font-size: 20px;
  line-height: 24px;
}

@media (min-width: 640px) {
  .footerTextCntr h1 {
    font-size: 5rem;
  }
}
@media (min-width: 768px) {
  .footerTextCntr h1 {
    font-size: 5rem;
    line-height: 1;
  }
  .footerTextCntr p {
    font-size: 26px;
    line-height: 24px;
  }
}
@media (min-width: 1024px) {
  .footerTextCntr h1 {
    font-size: 8rem;
    line-height: 120px;
  }
  .footerTextCntr p {
    max-width: 1200px;
    font-size: 24px;
    line-height: 28px;
  }
}
@media (min-width: 1280px) {
  .footerTextCntr h1 {
    font-size: 8rem;
  }
  .footerTextCntr p {
    font-size: 30px;
    line-height: 38px;
  }
}

/* footercntr */
/* subfooter */
/* subfooter */

.sub-footer-micro {
  position: relative;
}
.sub-footer_innercntrft-pd {
  padding: 10px 50px;
}
.sub-footer_innercntrmob-view {
  display: none !important;
}
.sub-footer_innercntrdesk-view {
  display: block !important;
}
.sub-footer_innercntrrow {
  display: flex;
}
.sub-footer_innercntrrow {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: 1.5rem * 0.5;
  padding-left: 1.5rem * 0.5;
  margin-top: 0;
}
.sub-footer_innercntrft-end-sec {
  display: flex;
  align-items: center;
  padding-left: 1.5rem;
}
.desktop-viewft-end-img {
  position: relative;
  width: 180px;
  min-width: 180px;
  height: 5vw;
  overflow: hidden;
}
.desktop-viewft-end-img img {
  object-fit: contain;
  width: 100%;
  height: 100%;
}
.sub-footer-micro .linevr {
  background: #000000;
  opacity: 1;
  width: 1px;
  height: 2.75em;
  opacity: 0.7;
  margin: auto 15px;
}
.sub-footer-micro .desktop-viewft-end-content p {
  color: #000000;
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 0;
  /* opacity: 0.7; */
  font-family: var(--font-family-secondary) !important;
}
.sub-footer-micro .sub-footerft-end-logo {
  position: relative;
  width: 80px;
  min-width: 80px;
  height: 5vw;
  overflow: hidden;
}
.sub-footer-micro .sub-footerft-end-logo img {
  scale: 1;
  object-fit: contain;
  width: 100%;
  height: 100%;
}
.sub-footerhr-mob {
  background: #000000;
  width: 1px;
  height: 50px;
  opacity: 0.7;
  margin: auto 15px;
}
.sub-footer_innercntrft-end-img {
  position: relative;
  width: 180px;
  min-width: 180px;
  height: 5vw;
  overflow: hidden;
}
@media screen and (max-width: 425px) {
  .sub-footer_innercntrdesk-view {
    display: none !important;
  }
  .desktop-viewft-end-content {
    margin-top: 15px;
  }
  .sub-footer-micro .desktop-viewft-end-content p {
    font-size: 12px;
  }
  .sub-footer-micro .linevr {
    display: none;
  }
  .sub-footer_innercntrmob-view {
    display: block !important;
  }
  .sub-footer_innercntrmob-viewcntr {
    display: flex;
    align-items: center;
  }
}
@media (min-width: 768px) {
  .sub-footer_innercntrcol-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
    margin: 0 auto;
  }
}
@media screen and (max-width: 900px) {
  .sub-footer-micro .sub-footer_innercntrft-pd {
    padding: 15px;
  }
  .sub-footer_innercntrft-end-sec {
    flex-direction: column;
    padding-left: 0;
    align-items: flex-start;
  }
  .sub-footer_innercntrft-end-img {
    width: 50px;
    height: 50px;
  }
  .desktop-viewft-end-img {
    width: 50px;
    height: 50px;
  }
  .sub-footer_innercntrft-end-img img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .sub-footer-micro .linevr {
    height: 1px;
    min-height: unset !important;
    width: 100%;
    margin: 15px auto !important;
  }
  .sub-footer-micro .sub-footerft-end-logo,
  .Rpsgmediatoplogo {
    width: 50px;
    min-width: 50px;
    height: 50px;
  }
}
