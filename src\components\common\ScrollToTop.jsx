import React, { useState, useEffect } from "react";
import { FiArrowUp } from "react-icons/fi";

const ScrollToTop = () => {
  const [scroll, setScroll] = useState(false);

  useEffect(() => {
    document.addEventListener("scroll", () => {
      if (window.scrollY > 450) {
        setScroll(true);
      } else {
        setScroll(false);
      }
    });
  }, []);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };
  return (
    <>
      <button
        className={`btn-scrollable ${scroll ? "t3-scroll" : ""}`}
        onClick={() => handleScrollToTop()}
      >
        <FiArrowUp />
      </button>
    </>
  );
};

export default ScrollToTop;
