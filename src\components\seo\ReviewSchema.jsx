import { Const } from "@/utils/Constants";
import { convertToISTISOString, formatMinutesToDuration } from "@/utils/Util";

const ReviewSchema = ({
  name = "",
  headline = "",
  description = "",
  image = "",
  duration = 0,
  url = "",
  author = "",
  actor = "",
  director = "",
  datePublished = "",
  dateModified = "",
}) => {
  const link = `${Const.ClientLink}${url}`;
  const actorList = actor?.split(",") || [];
  const actorData = actorList?.map((actor) => ({
    "@type": "Person",
    name: actor?.trim() || "",
  }));

  const schemaData = {
    "@context": "https://schema.org",
    "@type": "Review",
    datePublished: convertToISTISOString(datePublished),
    dateModified: convertToISTISOString(dateModified),
    name: name,
    url: link,
    headline: headline,
    description: description,
    inLanguage: "en",
    author: {
      "@type": "Person",
      name: author?.name || "",
      url: `${Const.ClientLink}${author?.slug || ""}`,
      worksFor: {
        "@type": "Organization",
        name: Const.Brand,
        url: Const.ClientLink,
      },
    },
    itemReviewed: {
      "@type": "Movie",
      name: name,
      image: image,
      datePublished: convertToISTISOString(datePublished),
      duration: [
        {
          "@type": "QuantitativeValue",
          value: formatMinutesToDuration(duration),
        },
      ],
      actor: actorData,
      director: {
        "@type": "Person",
        name: director,
      },
    },
    publisher: {
      "@type": "Organization",
      name: Const.Brand,
      sameAs: link,
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
    ></script>
  );
};

export default ReviewSchema;
