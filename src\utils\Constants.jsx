export const Const = {
  Token: "token",
  Session: "Session",
  LoggedInRolePermission: "Role",
  User: "User",
  LoggedIn: "LoggedIn",
  LoggedInUser: "LoggedInUser",
  STrue: true,
  SFalse: false,
  Success200: 200,
  Created201: 201,
  Invalid400: 400,
  UnAuth401: 401,
  Forbidden403: 403,
  NotFound404: 404,
  ServerError500: 500,
  BadGateway502: 502,
  ServiceUnavailable503: 503,
  GatewayTimeout504: 504,
  Redirect302: 302,
  Inactive: 0,
  Active: 1,
  Trash: 2,
  Draft: 3,
  Scheduled: 4,
  Limit: 20,
  Offset: 0,
  // Link: "http://localhost:3001/",
  Link: process.env.NEXT_PUBLIC_BACKEND_URL,
  ClientLink: process.env.NEXT_PUBLIC_CLIENT_LINK,
  // ClientLink: "https://www.hollywoodreporterindia.com",
  Brand: "The Hollywood Reporter India",
};

export const ProcessAPI = async (res) => {
  if (res.status === Const.Success200 || res.status === Const.Created201) {
    const data = await res.json();
    return data;
  } else if (res.status === Const.Redirect302) {
  } else if (res.status === Const.Invalid400) {
  } else if (res.status === Const.UnAuth401) {
    localStorage.clear();
    window.location.href = "/signin";
  } else if (res.status === Const.NotFound404) {
    const data = await res.json();
    return data;
    // return {
    //   notFound: true,
    // };
  } else {
    throw new Error("Some error occurred");
  }
};

export const ampCSS = `
@font-face {
  font-family: "Kepler Std Bold";
  src: url("../assets/fonts/KeplerStd-BoldScnDisp.woff") format("woff");
}

* {
  margin: 0%;
  padding: 0%;
  box-sizing: border-box;
}
body {
  width: 100%;
  height: 100%;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
}
.nav-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
  max-width: 1200px;
  margin: 0 auto;
}
  .hidden{
  display:none;
  }
.logo {
  flex: 1;
  text-align: center;
  max-width: 200px;
}
.search_icon {
  font-size: 20px;
  color: #db242a;
  cursor: pointer;
}
.tn-nav-rel {
  position: fixed;
  top: 0;
  left:0;
  right:0;  
  height: 60px;
  background-color: #fff;
  z-index: 999;
}
  /* Side Navbar */
amp-sidebar.mob-menu {
    width: 100vw ;
    max-width: 100vw ;
    background: #fff;
    z-index: 9999;
    top:8%
  }
.amp-sidebar-mask {
  background-color:transparent;
  width:100%;
  display: none
}
  .mob-item {
		height: 55px;
		width: 100%;
		background-color: white;
		border-bottom: 0.5px solid #9c9797;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0px 20px;
		font-size: 22px;
		transition: all 0.3s ease;
		font-weight: 500;
		cursor: pointer;
    font-family: "kepler-std-semicondensed-dis",serif;
	}
.mob-open-0 svg {
		transform: rotate(180deg);
	}

	.mob-open-0 {
		color: #db242a;
	}
	.mob-item svg {
		font-size: 14px;
	}
  .mbf {
		/* height: 65px; */
		width: 100%;
		background-color: white;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 0px 20px 20px;
		margin-top: 0px;
		margin-bottom: 30px;
		transition: all 0.3s ease;
	}
  .mob-follows {
		font-size: 20px;
		font-weight: 500;
		margin-bottom: 10px;
	}
  .mf-icons {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10px;
	}

	.mf-icons svg {
		width: 22px;
		height: 22px;
	}
    .caption {
  display: block;
  font-size: 14px;
  letter-spacing: 0;
  font-family: "kepler-std", serif;
  margin-bottom: 0px;
  margin-right: 10px;
  font-weight: 100;
}
  .courtesy {
  display: block;
  font-size: 12px;
  letter-spacing: 0;
  font-family: "Karla", sans-serif;
  margin-bottom: 0px;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.5);
}
  .listStyle {
  font-size: 22px;
}
  dl, ol, ul {
    margin-top: 0;
    margin-bottom: 1rem;
}
  .submenu-active{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  }
  .mbf-artboard-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.bo-right-click-text {
  font-size: 5.5vw;
  padding-top: 2.5vw;
  width: 100%;
  text-align: center;
  color:#212529
}

  .mob-mbody .mbody-head{
        display: grid;
        height: 55px;
        font-size: 22px;
        font-weight: 500;
        align-items: center;
        background-image: linear-gradient(180deg, #000 50%, #fff 0);
        background-size: 100% 200%;
        transition: all .4s ease-out;
        border-bottom: 1px solid #dedcdc;
        grid-template-columns: 2rem auto 2rem;
        padding: 0 20px;
        background-position-y: 0;
        color: #fff;
        position: sticky;
        top: 0;
        z-index: 2;
        cursor: pointer;
    }
  .mob-item + .mbf,
	.mob-submenu .mbf {
		margin-top: 20px;
		margin-bottom: 5px;
	}
.tn-ham {
  order: 3;
  display: flex;
  align-items: center;
}
.hamburger .line {
  width: 23px;
  height: 2px;
  background-color: #db242a;
  display: block;
  margin: 4px auto;
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.hamburger:hover {
  cursor: pointer;
}

#hamburger-1.is-active .line:nth-child(2) {
  opacity: 0;
}

#hamburger-1.is-active .line:nth-child(1) {
  -webkit-transform: translateY(6px) rotate(45deg);
  -ms-transform: translateY(6px) rotate(45deg);
  -o-transform: translateY(6px) rotate(45deg);
  transform: translateY(6px) rotate(45deg);
}

#hamburger-1.is-active .line:nth-child(3) {
  -webkit-transform: translateY(-6px) rotate(-45deg);
  -ms-transform: translateY(-6px) rotate(-45deg);
  -o-transform: translateY(-6px) rotate(-45deg);
  transform: translateY(-6px) rotate(-45deg);
}

#hamburger-6.is-active {
  -webkit-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  -webkit-transition-delay: 0.6s;
  -o-transition-delay: 0.6s;
  transition-delay: 0.6s;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

#hamburger-6.is-active .line:nth-child(2) {
  width: 0px;
}

#hamburger-6.is-active .line:nth-child(1),
#hamburger-6.is-active .line:nth-child(3) {
  -webkit-transition-delay: 0.3s;
  -o-transition-delay: 0.3s;
  transition-delay: 0.3s;
}

#hamburger-6.is-active .line:nth-child(1) {
  -webkit-transform: translateY(6px);
  -ms-transform: translateY(6px);
  -o-transform: translateY(6px);
  transform: translateY(6px);
}

#hamburger-6.is-active .line:nth-child(3) {
  -webkit-transform: translateY(-6px) rotate(90deg);
  -ms-transform: translateY(-6px) rotate(90deg);
  -o-transform: translateY(-6px) rotate(90deg);
  transform: translateY(-6px) rotate(90deg);
}
.nav-parent {
  position: fixed;
  top: 60px;
  left: 0;
  width: 200vw;
  display: flex;
  height: calc(100vh + 60px);
  transition: all 0.3s ease;
  /* background-color: red; */
  z-index: 3;
}
.nextStoryContainer {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0px;
  gap: 1rem;
}
.nextStoryTitle {
  font-family: serif;
  font-size: 1.8rem;
  font-weight: bold;
  white-space: nowrap;
  margin-left: 12px;
  color: #1a1a1a;
}
.nextStoryLine {
  flex-grow: 1;
  height: 1px;
  background-color: #1a1a1a;
}

.story-container{
    padding-top: 20px;
    background-color: #f3f4f5;
    border:none
}
/* HERO */
.storiesHeroSec {
  background-color: #f3f4f5;
  padding: 10px 12px 30px 12px;
}
.d-flex {
  display: flex;
}
.align-items-end {
  align-items: flex-end;
}
.justify-content-end {
  justify-content: flex-end;
}
#storiesherosec .col-md-4 {
  justify-content: flex-start;
}
.breadcumSec {
  margin-bottom: 10px;
}
.breadcumSec .breadcrumb {
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  list-style: none;
  color: #db242a;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 600;
  font-family: "Karla, sans-serif";
}
.breadcumSec .breadcrumb .breadcrumb-item::after {
  content: ">";
  position: relative;
  font-weight: 300;
  font-size: 14px;
  margin: 0px 10px;
  color: #636363;
}
.breadcumSec .breadcrumb .breadcrumb-item::before {
  content: none;
}

.breadcumSec .breadcrumb .breadcrumb-item:last-child::after {
  content: "";
}
.contentSec {
  padding: 20px 0px;
  text-align: left;
}

.contentSec h1 {
  color: #000;
  font-size: 40px;
  font-weight: 500;
  line-height: 0.9;
  margin-bottom: 25px;
  font-family: kepler-std-semicondensed-dis, serif;
  transition: all 0.3s ease;
}

.contentSec:hover h1 {
  color: #db242a;
}

.contentSec p {
  color: #636363;
  font-size: 22px;
  line-height: 1.2;
  margin-bottom: 20px;
  font-family: "kepler-std", serif;
  font-weight: 300;
  font-style: normal;
}
.author_wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.author_right {
  position: relative;
  width: 100%;
}
.author_image {
  overflow: hidden;
  border-radius: 50%;
  object-fit: cover;
  object-position: center;
}
.author_name {
  display: block;
  font-size: 16px;
  text-transform: uppercase;
  font-weight: 700;
  color: #db242a;
  font-family: "Karla", sans-serif;
}
.authorSec span.timeline {
  display: block;
  text-transform: uppercase;
  color: #636363;
  font-weight: 500;
  font-size: 16px;
  font-family: "Karla", sans-serif;
}
.authorSec span.timeline {
  font-size: 14px;
}
.timeline {
  text-transform: uppercase;
  color: #636363;
  font-weight: 500;
  font-size: 16px;
  font-family: "Karla", sans-serif;
}
.follow-us-section {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}
.followus {
  display: flex;
  margin-top: 20px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.25);
  list-style: none;
  padding: 0px;
  margin-bottom: 0px;
}

.followus .items {
  position: relative;
  padding: 10px 20px;
  border-right: 1px solid rgba(0, 0, 0, 0.25);
  border-bottom: none;
  transition: all 0.3s ease;
}
.sponsored-tag {
	color: #f3f3f3;
	width: fit-content;
	padding: 2px 8px;
	background-color:  #db242a;
	font-family:  "Karla", sans-serif;
	text-transform: uppercase;
	font-size: 14px;
}
.followus .items:hover {
  background: #db242a;
  border-color: #db242a;
  color: #000000;
}

.followus .items:last-child {
  border-right: none;
}

.followus .items .icons {
  width: 18px;
  height: 18px;
}
.followus .items:hover .tooltipText {
  opacity: 1;
  transform: translateX(-10px);
}
.followus .items:hover .tooltipText {
  opacity: 1;
  transform: translateY(25px);
}
.storiesSection{
  padding:0px 12px;
}
.tooltipText {
  background-color: #fff;
  position: absolute;
  top: 50%;
  left: 0%;
  right: 90%;
  padding: 5px;
  border-radius: 5px;
  font-size: 14px;
  opacity: 0;
  transition: all 0.5s;
  text-align: right;
  align-items: flex-end;
  display: flex;
  width: max-content;
  justify-content: right;
}
.tooltipText::after {
  content: "";
  border-width: 5px;
  border-style: solid;
  border-color: #fff transparent transparent transparent;
  position: absolute;
  top: 100%;
  left: 40%;
  margin-left: 5%;
  z-index: 10;
}
/* Hover text tooltip */
.followus .items:hover .tooltipText {
  opacity: 1;
  transform: translateX(-10px);
}
.followus .items:hover .tooltipText {
  opacity: 1;
  transform: translateY(25px);
}
.container-fluid,
.container {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}
.row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.storiesHeroBanner .heroSection {
  width: 100vw;
  position: relative;
  overflow: hidden;
  height: auto;
  background-color: rgba(0, 0, 0, 0.241);
  aspect-ratio: 16/9;
}

.storiesHeroBanner .heroSection img {
  object-fit: cover;
}
.storiesHeroBanner .heroSection {
  min-height: 44vw;
}
.heroImgCont {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.pos-rel-full {
  position: relative;
  width: 100%;
  height: 100%;
}
.mainCaption {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  justify-content: flex-start;
  padding: 3px 0;
}
.storiesHeroBanner .container {
  padding: 0px 12px;
  margin-top: 0px;
}

/* Main Content */

.reviewInnerContainer {
		margin-bottom: 0rem;
    display: flex;
    flex-direction: column;
	  width: calc(100% - 35px);
	  margin-top: 1rem;
	  background-color: #fff6f0;
	  border-radius: 6px;
	  overflow: hidden;
	  border: 1px solid #f0eae0; 
}
.reviewInnerContainer .container1 {
	width: 100%;
    padding: 20px 25px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.reviewInnerContainer .container1::after {
	content: "";
	position: absolute;
  top: unset;
  bottom: 0;
  right: 0;
  left: 20px;
  width: 90%;
  text-align: center;
	border: .1px dashed #3c3c3ca6;
}
.reviewInnerContainer .container1 h4 {
	font-family: "Karla",sans-serif;
	margin-bottom: 10px;
	font-weight: 700;
	font-size: 1.5rem;
}
.reviewInnerContainer .container2 {
	width: 100%;
	padding: 15px 20px;
	display: flex;
	flex-direction: column;
	gap: 5px;
}
  .descriptionText h5{
    font-size: 18px;
    font-weight: 700;
    font-family: "Karla",sans-serif;
    color: #db242a;
    margin-bottom: 4px;
  }
.reviewText .h5 {
    font-size: 16px;
    font-weight: 700;
    font-family:  "Karla",sans-serif;
    margin: 0;
    white-space: nowrap;
  }
.reviewText {
    display: flex;
    align-items: flex-start;
}
.reviewText p, .reviewText span:nth-child(2) {
    padding-left: 5px;
}

.readMore .breadcumSec {
  margin-bottom: 10px;
}
  .video {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
}
.readMore h3 {
  font-size: 14px;
  font-family: "Karla", sans-serif;
  font-weight: 500;
  color: #636363;
  margin-bottom: 10px;
}
.breadcrumb {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  list-style: none;
  color: #db242a;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  /* font-family: karla, sans-serif; */
  margin-bottom: 0;
}
.readMore .breadcumSec .breadcrumb {
  font-size: 14px;
}
.readMore .breadcumSec .breadcrumb .breadcrumb-item:after {
  content: "|";
  position: relative;
  font-weight: 300;
  font-size: 12px;
  margin: 0 5px;
  color: #636363;
}
.breadcrumb-item::before {
  content: none;
}
.mainContent {
  margin: 40px 22px 20px;
}

/* Tip tap renderer */
.tip > p:last-child::after {
  background-image: url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20xmlns%3Axlink%3D%27http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%27%20width%3D%27259%27%20height%3D%27110%27%3E%3Cdefs%3E%3Cpattern%20id%3D%27a%27%20preserveAspectRatio%3D%27none%27%20width%3D%27100%25%27%20height%3D%27100%25%27%20viewBox%3D%270%200%2066%2028%27%3E%3Cimage%20width%3D%2766%27%20height%3D%2728%27%20xlink%3Ahref%3D%27data%3Aimage%2Fpng%3Bbase64,iVBORw0KGgoAAAANSUhEUgAAAEIAAAAcCAYAAADV0GlvAAAEh0lEQVRYhe2YW4xeUxTHf9OptjGqpKrELT2ymdPSQULd65a2ikhLQuLBJRVEIkEi0ajEiwjSBCGCRDRCH0gEwZR0SLRImyAimznpVuaFjkurLdXbyPat%2Bexv7fVd6tX3f9prnbXX2ee%2F1l5r79MzPKt%2FjC6Y0KWghi4Rgi4RgomZpobHga8zbeeYBDwL%2FAzc18GsFcBfwKPZkxwrgYOB27MnNg4H5gCLgGNMC8AqlvuAI1zwW6uinAY8KY4iaTe44OsEVUW5GHhY5rzjgl8h%2BouBtcBu4BvgV2CpC%2F63ZG4%2F8DwwHTgJ2AVsAgZd8A3kVUUZP%2FoWoA%2BYLe%2BLfn8HbnPBf5XYHgm8DkwFRoHlLvgNVVH2xvUDTwGHaiKsjPgskiDjF4BrZbwN%2BFbZLgUGZDxHIouwj2TGXGCPRDzFTcD5idwntsOKhB7gQeDoRN0r74s4TPm9WvktgBNd8JG8VVVRjkiQGmDViEFZwDUJCRFrXfB7le2CZDy5KsrpMl6o7Na74P9QOm0zjveVfKoiIcXOGDilW6TkoirKKeOCC34I%2BFw7sjJisCrKyPLTWp8KVVHGFD1O2cyoinJykiXN5s4ETsvebNi2IAwJzu7Eb8yUS5XNDhf8LqX7STvSRMQ9vBF4Dpj5HxY4Azg703b%2BccMu%2BO%2BVTke4ld9zgGlKtz4VqqKcaAVBb40PgPlSmBrmu%2BA3K531MUcBlyvdqJGKC7ChMyd2h%2FNMyxre68DvS0q%2BS9bZAJ0R2yQbWr5Q9tz8zApit7hM6da44OudqSrKCU0WjBHhi2LtyaxqCC74TUqnsyfWm9X8W3TvBB7LPBlELMssalij5AuBKZkV3JFp8rmxdT2TWdUwpGQr68ZhBWcH8CHwC%2FCRBPWQqigjQfcA8zIvAqtYauwxFthq32roKC%2B29iiwwegszTIHw68D3pT1bpbnC%2BWQNjubrdAJER%2B74HcewAJTfOmC1xX6Vkl5jYY2WBXl8UB%2FZlWDFZy7gZsT%2BREX%2FP1VUb4L3Nvu1GqdIzQaUrsqymOTw0w76OI3tUXxO5C2uc4Fvz3x22Nk6T9rdMHvd8HHuvBy5iVBJ0ToBXaaDdbcS4CDMiu7s7QiQvsdMA5dW5W8OvOSoB0RW4AvlK7T%2BhD3%2B7oO5w6qzmIdjBrslaxbdkRQ8vZk%2FKecSutoR4Rufb1Ge2yGIRe8vl80i7LuLPOMO8Q4rOBYRIwouS8ZvyKXxTraEqHkM%2BVa2wl0bYk3zFnGvDEjws0Iw8ieSNi5mVWO9Mb5InBjatGqa%2BwzTm5nZFY24kLfVk%2FOMi1howt%2Bi9I17ffAW0o%2BRW6jGqcrOR7d98p%2Fkrly9a%2FDyojlwJJo7IIfVc9WAVcA10sL01gpcwdc8HqPviFzHxL5AeBK4KrMS%2B0EuCRpea8C8TZ8AfCasv1EMug64IdEv6wqynqHcsF%2FKofAeJd6Qr%2FQ%2BjHzXROCUuxvkuYj8qwVJkmFj4eenhZ2yMLj5e9H43%2BGRszgE1R2jMn9yctfrUjkydnMJkT8L9H9eSvoEiHoEiHoEhEB%2FA3s2zj1mNH%2BUwAAAABJRU5ErkJggg%3D%3D%27%2F%3E%3C%2Fpattern%3E%3C%2Fdefs%3E%3Cpath%20fill%3D%27url%28%23a%29%27%20d%3D%27M0%200h259v110H0z%27%2F%3E%3C%2Fsvg%3E);
  background-size: 1.625rem;
  content: " ";
  display: inline-block;
  height: 0.6875rem;
  margin-left: 0.375rem;
  width: 1.625rem;
}

/* Next Story Styles */

.amp-next-page-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  /* padding: 20px; */
  /* background-color: #f5f5f5; */
  /* margin: 20px 0; */
  font-family: "Karla", sans-serif;
  /* font-weight: 600; */
  /* color: #333; */
}
/* .amp-next-page-separator::before {
  content: "";
  flex: 1;
  height: 1px;
  background: #ddd;
  margin-right: 20px;
}
.amp-next-page-separator::after {
  content: "";
  flex: 1;
  height: 1px;
  background: #ddd;
  margin-left: 20px;
} */

`;
