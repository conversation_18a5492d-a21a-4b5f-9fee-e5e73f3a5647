import React from "react";
import Head from "next/head";
import { Const } from "@/utils/Constants";
import { convertToISTISOString, extractTextFromDoc } from "@/utils/Util";

const NewsArticleSchema = ({
  headline,
  datePublished,
  dateModified,
  articleSection,
  keywords,
  description,
  url,
  content,
  author,
  image,
}) => {
  const link = `${Const.ClientLink}${url}`;
  const articleBody = extractTextFromDoc(content);
  const schemaData = {
    "@context": "http://schema.org",
    "@type": "NewsArticle",
    headline: headline,
    datePublished: convertToISTISOString(datePublished),
    dateModified: convertToISTISOString(dateModified),
    articleSection: articleSection,
    inLanguage: "en",
    keywords: keywords?.join(", "),
    description: description,
    url: link,
    articleBody: articleBody,
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": link,
    },
    publisher: {
      "@type": "Organization",
      name: "Hollywood Reporter India",
      url: Const.ClientLink,
      logo: {
        "@type": "ImageObject",
        url: `${Const.ClientLink}/thr-logo.png`,
        width: 160,
        height: 80,
      },
    },
    author: {
      "@type": "Person",
      name: author?.name || "",
      url: `${Const.ClientLink}${author?.slug || ""}`,
    },
    image: {
      "@type": "ImageObject",
      url: image,
      width: 1920,
      height: 1080,
    },
  };
  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
      ></script>
    </Head>
  );
};

export default NewsArticleSchema;
