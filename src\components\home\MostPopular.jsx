import React, { useEffect, useRef } from "react";
import style from "./Home.module.css";
import Link from "next/link";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "@/components/common/SlideNavButton";

const MostPopular = ({ data }) => {
  const swiperRef = useRef(null);

  const swiperClass = "most-popular-swiper";

  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper) {
      const swiper = swiperRef.current.swiper;

      const handleSlideChange = () => {
        const bullets = document.querySelectorAll(
          `.${swiperClass} .swiper-pagination-bullet`
        );

        bullets.forEach((bullet, index) => {
          bullet.classList.remove("expanded");

          if (index === swiper.realIndex) {
            setTimeout(() => {
              bullet.classList.add("expanded");
            }, 80);
          }
        });
      };

      swiper.on("slideChange", handleSlideChange);

      handleSlideChange();

      return () => {
        swiper.off("slideChange", handleSlideChange);
      };
    }
  }, []);
  return (
    <>
      {data && data.length > 0 && (
        <>
          <section id="mostpopular" className={style.mostPopular}>
            <div className="container">
              <div className="row desk-view">
                <div className="col-md-12 d-flex align-items-start justify-content-between mp-margin">
                  <h2>Lists</h2>
                  <SlideNavButton prev={"mpPrev"} next={"mpNext"} />
                </div>
              </div>
              <div className="row mob-view">
                <div className="col-md-12 d-flex align-items-start justify-content-between mb-2">
                  <h2>Lists</h2>
                </div>
              </div>
              <div className="row">
                <div className="col-md-12 desk-view">
                  <Swiper
                    ref={swiperRef}
                    className={swiperClass}
                    autoplay={{
                      delay: 2500,
                      disableOnInteraction: false,
                    }}
                    pagination={{
                      clickable: true,
                    }}
                    modules={[Navigation, Pagination, Keyboard]}
                    navigation={{
                      prevEl: ".mpPrev",
                      nextEl: ".mpNext",
                    }}
                    // navigation={true}
                    loop={true}
                    spaceBetween={15}
                    slidesPerView={1}
                    breakpoints={{
                      425: { slidesPerView: 1.5 },
                      600: { slidesPerView: 1 },
                      1200: { slidesPerView: 3 },
                    }}
                  >
                    {data.map((item, i) => {
                      const paddedIndex = String(i + 1).padStart(2, '0');
                      return (
                        <SwiperSlide key={`most-popular-${i}`}>
                          <div className={style.sliderItem}>
                            <Link
                              className={style.items}
                              href={item?.slug ?? ""}
                            >
                              <h3 className={style.sequence}>{paddedIndex}.</h3>
                              <p className="ff1 mpp">{item?.title ?? ""}</p>
                            </Link>
                          </div>
                        </SwiperSlide>
                      );
                    })}
                  </Swiper>
                </div>
                <div className="col-md-12 mob-view">
                  <div className={style.sliderItem}>
                    {data.map((item, i) => {
                      const paddedIndex = String(i + 1).padStart(2, '0');
                      return (
                        <Link
                          className={style.items}
                          href={item?.slug ?? ""}
                          key={`most-popular-mob-${i}`}
                        >
                          <div
                            className={style.items}
                            style={{ display: "flex" }}
                            key={`most-mob-${i}`}
                          >
                            <h3 className={style.sequence}>{paddedIndex}.</h3>
                            <p className="ff1 mpp">{item?.title ?? ""}</p>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          </section>
        </>
      )}
    </>
  );
};

export default MostPopular;
