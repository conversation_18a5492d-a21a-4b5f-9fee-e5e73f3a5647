import React, { useEffect, useState } from "react";
import Outside<PERSON><PERSON><PERSON>and<PERSON> from "react-outside-click-handler";
import { useRouter } from "next/router";
import { IoMdSearch } from "react-icons/io";
import { convertSlugOrTitle } from "@/utils/Util";

const Search = ({ open, setOpen, isSmall }) => {
  const [text, setText] = useState("");
  const router = useRouter();
  const { query } = router.query;

  useEffect(() => {
    setText(query);
  }, [query]);

  const tags = [
    {
      name: "Reviews",
    },
    {
      name: "News",
    },
    {
      name: "Hollywood",
    },
    {
      name: "Song",
    },
    {
      name: "Entertainment",
    },
    {
      name: "Business",
    },
    {
      name: "Fashion",
    },
    {
      name: "Beauty",
    },
    {
      name: "Celebrity",
    },
    {
      name: "Bollywood",
    },
    {
      name: "New Styles",
    },
  ];

  const handleSearch = (string) => {
    const queryText = convertSlugOrTitle(string, false);
    setOpen(false);
    router.push(`/result/${queryText}/1`);
    setText("");
  };

  return (
    <>
      <div
        className="search t3"
        style={{
          background: open ? "rgba(0, 0, 0, .5)" : "rgba(0, 0, 0, 0)",
          pointerEvents: open ? "all" : "none",
          opacity: open ? 1 : 0,
        }}
      >
        <OutsideClickHandler
          onOutsideClick={() => {
            setOpen(false);
          }}
        >
          <div
            className="search-div t3"
            style={{
              transform: `translateY(${open ? "0%" : "-100%"})`,
              padding: isSmall ? "70px 380px 45px" : "104px 380px 45px",
            }}
          >
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSearch(text);
              }}
            >
              <div className="search-main-div">
                <input
                  type="text"
                  placeholder="Search here..."
                  className="search-input-div"
                  value={text || ""}
                  onChange={(e) => {
                    setText(e.target.value);
                  }}
                ></input>
                <IoMdSearch
                  style={{
                    cursor: "pointer",
                    color: "#d40f16",
                    fontSize: "18px",
                  }}
                  onClick={() => handleSearch(text)}
                />
              </div>
            </form>

            <div style={{ marginTop: "15px" }}>
              <p className="sugg-head-p">Suggested Topics :</p>
              <div className="tags-sugg-div">
                {tags.map((el, i) => (
                  <span
                    className="tags-suggest"
                    onClick={() => handleSearch(el.name)}
                  >
                    {el.name}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </OutsideClickHandler>
      </div>
    </>
  );
};

export default Search;
