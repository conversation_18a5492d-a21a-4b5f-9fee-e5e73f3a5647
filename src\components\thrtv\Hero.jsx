import React from "react";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";
import style from "@/components/category/Category.module.css";
import Image from "next/image";
import HeroImage from "@/assets/images/category-hero.png";

const Hero = ({ title }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <section id="categoryhero" className={style.categoryHero}>
      <div className="container-fluid">
        <div className="row">
          <div className="col-md-12 p-0" style={{ zIndex: "-1" }}>
            <div className={style.heroSection}>
              <div className={style.herocrAbs}>
                <div className={style.herocrText}>
                  <h1>{title}</h1>
                </div>
              </div>
              <div className={style.heroImgCont}>
                <div className="pos-rel-full">
                  <Image src={HeroImage} alt="cover" fill />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
