import React from "react";
import Link from "next/link";
import Image from "next/image";
import { formatDateAndTime } from "@/utils/Util";

const TopVideos = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <section id="top-videos">
          <div className="container">
            <div className="row">
              <div className="col-md-12">
                <h2>Top Videos</h2>
              </div>
            </div>
            <div className="row scrollable-div">
              {data.map((item, i) => (
                <div className="col-md-3 rn-card" key={`top-videos-${i}`}>
                  <Link className="card-wrapper" href={item?.slug ?? "#"}>
                    <div className="feature-box">
                      <div className="video-timestamp">32:20</div>
                      <Image src={item.image} fill />
                    </div>
                    <div className="content-sec">
                      <h3 className="card-title">{item.title}</h3>
                      <span className="view-timeline">{`111k views . ${formatDateAndTime(
                        item.timestamp
                      )}`}</span>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default TopVideos;
