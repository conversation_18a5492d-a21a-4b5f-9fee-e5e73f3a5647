import React, {useEffect, useRef} from "react";
import style from "./Home.module.css";
import Image from "next/image";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "../common/SlideNavButton";
import Link from "next/link";

const HighlightMagazine = ({ data }) => {
  const swiperRef = useRef(null);

  const swiperClass = 'highlight-maga-swiper';

  useEffect(() => {
    if (swiperRef.current && swiperRef.current.swiper) {
      const swiper = swiperRef.current.swiper;
  
      const handleSlideChange = () => {
        const bullets = document.querySelectorAll(`.${swiperClass} .swiper-pagination-bullet`);
        
        bullets.forEach((bullet, index) => {
          bullet.classList.remove('expanded');
          
          if (index === swiper.realIndex) {
            setTimeout(() => {
              bullet.classList.add('expanded');
            }, 50);
          }
        });
      };
  
      swiper.on('slideChange', handleSlideChange);
  
      handleSlideChange();
  
      return () => {
        swiper.off('slideChange', handleSlideChange);
      };
    }
  }, []);
  const hlgs = [
    {
      title: "COVER STORY",
      img: "https://static.mirchi.in/photo/97690172/97690172.jpg",
      p: "Reneé Rapp Sings Openly About Assault, Anxiety and Sexuality. She’s Even More Candid",
    },
    {
      title: "BUSINESS",
      img: "https://im.rediff.com/getahead/2023/nov/13glams2.jpg?w=670&h=900",
      p: "Shane Smith and the Final Collapse of Vice News",
    },
    {
      title: "BUSINESS",
      img: "https://akm-img-a-in.tosshub.com/sites/visualstory/wp/2023/05/Slide-1-5.jpg",
      p: " Streaming TV Prizefight: Walmart Tries Muscling in on Amazon",
    },
    {
      title: "TV",
      img: "https://st1.bollywoodlife.com/wp-content/uploads/2017/06/Pooja-Hegde-Allu-Arjun.jpg",
      p: " Streaming TV Prizefight: Walmart Tries Muscling in on Amazon",
    },
  ];
  return (
    <section id="highlightmagazine" className={style.highlightMagazine}>
      <div className="container">
        {/* <div className="row hms">
                <div className="col-md-12">
                    <div className={`${style.banner} hmsb`}>
                        {
                            hlgs.map((itm, i) => {
                                return (
                                    <div className="hl-div">
                                        <div className="hl-abs"></div>
                                        <div className="hl-content">
                                            <div className="hl-title">{itm.title}</div>
                                            <div className="hl-p">{itm.p}</div>
                                        </div>
                                        <Image src={itm.img} fill />
                                    </div>
                                )
                            })
                        }
                    </div>
                </div>
            </div> */}
        {data && data.length > 0 && (
          <>
            <div className="container" style={{ marginTop: "45px" }}>
              <div className="row awards-row desk-view">
                <div className="col-md-12 d-flex align-items-start flex-spacebtn mb-4 awards">
                  <div className="">
                    <h2>Highlights from the magazine</h2>
                  </div>
                  <SlideNavButton prev={"awardNext"} next={"awardPrev"} />
                </div>
              </div>
              <div className="row d-flex align-items-start">
                <div className="col-md-12 mob-view">
                  <div className="col-md-4">
                    <div className="ft-img">
                      <Image
                        src={
                          "https://www.hollywoodreporter.com/wp-content/uploads/2024/03/10cover.hires-2024.jpg"
                        }
                        fill
                        alt="footer sample"
                      />
                      <button className={style.magazinesub}>SUBSCRIBE</button>
                    </div>
                  </div>
                </div>
                <div
                  className="col-md-2 desk-view"
                  style={{ position: "relative", height: "385px", width:'22%' }}
                >
                  <div>
                    <div className="ft-img">
                      <Image
                      style={{objectFit:'cover', objectPosition:'top'}}
                        src={
                          "https://www.hollywoodreporter.com/wp-content/uploads/2024/03/10cover.hires-2024.jpg"
                        }
                        fill
                        alt="footer sample"
                      />
                      <button className="magazinesub">SUBSCRIBE</button>
                    </div>
                  </div>
                </div>
                <div className="col-md-10 desk-view" style={{width:'78%'}}>
                  <Swiper
                    ref={swiperRef}
                    className={swiperClass}
                    autoplay={{
                      delay: 2500,
                      disableOnInteraction: false,
                    }}
                    pagination={{
                      clickable: true,
                    }}
                    modules={[Navigation, Pagination, Keyboard]}
                    navigation={{
                      prevEl: ".awardPrev",
                      nextEl: ".awardNext",
                    }}
                    // navigation={true}
                    loop={true}
                    spaceBetween={15}
                    slidesPerView={3}
                    breakpoints={{
                      425: { slidesPerView: 1 },
                      600: { slidesPerView: 1 },
                      1200: { slidesPerView: 3 },
                    }}
                  >
                    {data.map((item, i) => {
                      return (
                        <>
                          <SwiperSlide key={`awards-${i}`}>
                            <div className={style.cardItem}>
                              <Link
                                className={style.cardWrapper}
                                href={item?.slug ?? "#"}
                              >
                                <div className={style.featureBox}>
                                  <Image src={item?.coverImg ?? ""} fill />
                                </div>
                                <div className={style.magawrap}>
                                  <span className={style.magacat}>
                                    Cover Story
                                  </span>
                                  <span className={style.magatime}>
                                    6 Days Ago
                                  </span>
                                </div>
                                <h3 className={style.magazinetitle}>
                                  {item?.title ?? ""}
                                </h3>
                                <p class={style.cardsubtitle}>
                                  From Jon Stewart's live 'The Daily Show'
                                  taping to John Legend's concert for Illinois
                                  governor JB Pritzker, The Hollywood Reporter
                                  has rounded up details on Democratic shindigs
                                  in the Windy City.
                                </p>
                                <span className={style.authorname}>BY Anubhav Gupta</span>
                              </Link>
                            </div>
                          </SwiperSlide>
                        </>
                      );
                    })}
                  </Swiper>
                </div>
              </div>
              <div className="col-md-12 mob-view">
                <div className="row scrollable-div">
                  {data.map((item, i) => {
                    return (
                      <>
                        <div
                          className="col-md-3 rn-card"
                          key={`awards-mob-${i}`}
                        >
                          <div className={style.cardItem}>
                            <Link
                              className={style.cardWrapper}
                              href={item?.slug ?? "#"}
                            >
                              <div className={style.featureBox}>
                                <Image src={item?.coverImg ?? ""} fill />
                              </div>
                              <h3>{item?.title ?? ""}</h3>
                            </Link>
                          </div>
                        </div>
                      </>
                    );
                  })}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default HighlightMagazine;
