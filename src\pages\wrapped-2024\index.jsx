import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Footer from "@/components/wrapped/Footer";
import Section1 from "@/components/wrapped/Section1";
import Section2 from "@/components/wrapped/Section2";
import gsap from "gsap";
import ScrollToPlugin from "gsap/dist/ScrollToPlugin";
import React, { useRef } from "react";
import { useEffect } from "react";
gsap.registerPlugin(ScrollToPlugin);

const Wrapped2024 = ({ meta }) => {
  const home = useRef();

  useEffect(() => {
    document.title = "The Hollywood Reporter | Year in Review";
    gsap.to(window, {
      scrollTo: 0,
    });
    var tl = gsap.timeline();
    tl.to("#wrap-loader-logo", {
      opacity: 0,
      delay: 1,
      duration: 0.5,
    })
      .to("#wrap-loader-text", {
        opacity: 1,
        duration: 0.5,
      })
      .to("#thr-wrap-loader", {
        top: "-150%",
        delay: 0.8,
        duration: 1,
      })
      .to(
        home.current.querySelectorAll("h4"),
        {
          transform: "translateY(0%)",
          stagger: 0.2,
          duration: 0.5,
          delay: -0.7,
        },
        "a"
      )
      .to(
        home.current.querySelector("h5"),
        {
          opacity: 1,
          duration: 0.5,
          delay: 0.1,
        },
        "a"
      )
      .to(
        home.current.querySelector("h2"),
        {
          transform: "translateY(0%)",
          delay: -0.7,
          duration: 0.5,
        },
        "a"
      );
  }, []);

  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div id="thr-main">
        <div id="thr-bg-vid">
          <video autoPlay muted loop playsInline src="/wrapped/vid.mp4"></video>
        </div>
        <div id="thr-wrap-loader">
          <img id="wrap-loader-logo" src="/wrapped/thr-logo.png" alt="The Hollywood Reporter India Logo" />
          <h1 id="wrap-loader-text">THR India Wrapped 2024</h1>
        </div>
        <div id="thr-wrap-container">
          <Section1 home={home} />
          <Section2 />
          <Footer />
        </div>
      </div>
    </>
  );
};

export default Wrapped2024;

export async function getStaticProps() {
  const meta = {
    title:
      "THR India Wrapped 2024: Top Movies, Shows & Performances of the Year",
    description:
      "Explore THR India Wrapped 2024! Dive into the year's best performances, best movies, shows, music and pop culture that defined Indian entertainment and culture.",
    keywords: [],
    author: "THR INDIA",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
