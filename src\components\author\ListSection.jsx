import React from "react";
import Image from "next/image";
import Link from "next/link";
import { formatDateAndTime, getAuthorText } from "@/utils/Util";

const MainSection = ({ name, data, hasMore, handleShowMore }) => {
  return (
    <>
      {data && data.length > 0 && (
        <>
          <div className="row">
            <div className="col-md-12">
              <h2>MORE FROM {name?.toUpperCase()}</h2>
            </div>
          </div>
          <div className="row secmt ln-count">
            <div className="col-md-12">
              {data?.map((item, index) => {
                return (
                  <Link
                    className="cat-card"
                    href={item && item.slug ? item.slug : "/"}
                  >
                    <div className="image">
                      <Image
                        fill
                        src={
                          item?.croppedImg
                            ? item?.croppedImg
                            : item?.image
                            ? item?.image
                            : ""
                        }
                        alt={item && item.altName ? item.altName : ""}
                        className="imgcover"
                      />
                    </div>
                    <div className="content">
                      <div className="d-flex flex-columnn align-items-center gap-3 mb-2">
                        <span className="category">
                          {item && item.subcategory ? item.subcategory : ""}
                        </span>
                        <span className="timeline">
                          {item && item.timeline
                            ? formatDateAndTime(item?.timestamp)
                            : ""}
                        </span>
                      </div>
                      <h2>{item && item.title ? item.title : ""}</h2>
                      <p>{item && item.description ? item.description : ""}</p>
                      <span className="author">
                        {getAuthorText("BY", item?.author, item?.contributor)}
                      </span>
                    </div>
                  </Link>
                );
              })}
            </div>
            {hasMore && (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <button
                  className="btn btn-outline out-btn4"
                  style={{ width: "118px" }}
                  onClick={handleShowMore}
                >
                  <div className="middle-btn"></div>
                  Show More
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
};

export default MainSection;
