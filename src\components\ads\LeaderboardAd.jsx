import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";

const DynamicAds = ({ adUnits, isVideo = false }) => {
  const [currentAd, setCurrentAd] = useState(null);

  const updateAdUnit = () => {
    const windowWidth = window.innerWidth;

    // Find the ad unit that matches the current window width
    const matchedAd = adUnits.find(
      (unit) => windowWidth >= unit.minWidth && windowWidth <= unit.maxWidth
    );

    setCurrentAd(matchedAd || null);
  };

  useEffect(() => {
    updateAdUnit(); // Set initial ad unit
    window.addEventListener("resize", updateAdUnit); // Update on resize

    return () => {
      window.removeEventListener("resize", updateAdUnit); // Cleanup on unmount
    };
  }, [adUnits]);

  if (!currentAd) {
    return null; // Render nothing if no matching ad is found
  }

  return (
    <Link
      href="https://www.rpsg.in/"
      target="_blank"
      className="ad-parent-leaderboard ad-text"
    >
      {isVideo ? (
        <video
          width={currentAd.adSize[0]}
          height={currentAd.adSize[1]}
          alt={"leaderboard-ad-video"}
          style={{ objectFit: "cover" }}
          playsInline
          loop
          muted
          autoPlay
        >
          <source src={currentAd.adImagePath} type="video/mp4" />
        </video>
      ) : (
        <Image
          src={currentAd.adImagePath || ""}
          alt="leaderboard-ad"
          width={currentAd.adSize[0]}
          height={currentAd.adSize[1]}
          style={{ objectFit: "cover" }}
        />
      )}
    </Link>
  );
};

export default DynamicAds;
