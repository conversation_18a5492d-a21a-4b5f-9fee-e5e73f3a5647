import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { FiPlay } from "react-icons/fi";
import style from "./Thr.module.css";
import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import dynamic from "next/dynamic";
import {
  dateFormateWithTimeShort,
  getVideoCount,
  getVideoDuration,
  htmlParser,
} from "@/utils/Util";

const ReactPlayer = dynamic(() => import("react-player"), { ssr: false });

const TopSection = ({ main, mostRecent }) => {
  const [isClient, setIsClient] = useState(false);
  const [showPlayBtn, setShowPlayBtn] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [videoData, setVideoData] = useState({
    mainDuration: "Loading...",
    mainCount: "Loading...",
    recentDurations: [],
    recentCounts: [],
  });

  const videoRef = useRef(null);

  useEffect(() => {
    setIsClient(true);
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent",
        start: "top 60%",
      },
    });
  }, []);

  useEffect(() => {
    const fetchVideoData = async () => {
      const [mainDuration, mainCount] = await Promise.all([
        getVideoDuration(main?.src ?? ""),
        getVideoCount(main?.src ?? ""),
      ]);

      const recentDurations = await Promise.all(
        mostRecent.map((item) => getVideoDuration(item?.src ?? ""))
      );
      const recentCounts = await Promise.all(
        mostRecent.map((item) => getVideoCount(item?.src ?? ""))
      );

      setVideoData({
        mainDuration,
        mainCount,
        recentDurations,
        recentCounts,
      });
    };

    fetchVideoData();
  }, [main, mostRecent]);

  const handleVideoClick = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        setShowPlayBtn(true);
      } else {
        videoRef.current.play();
        setShowPlayBtn(false);
      }
      setIsPlaying(!isPlaying);
    }
  };
  const { mainDuration, mainCount, recentDurations, recentCounts } = videoData;

  return (
    <section
      id="topstories"
      className={style.topStories}
      style={{ backgroundColor: "black" }}
    >
      <div className="container">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div className="col-md-12">
            <h1
              className="heading-title"
              style={{ color: "white", marginBottom: "0.5rem" }}
            >
              THR VIDEO
            </h1>
          </div>
        </div>

        <div className="row">
          {/* Main video section */}
          <div className="col-md-12 col-lg-7 col-xl-8 zoom-cont">
            <div className="image-sec image-zoom-parent pos-rel">
              {isClient && (
                <ReactPlayer
                  ref={videoRef}
                  url={main?.src}
                  width="100%"
                  height="100%"
                  controls
                />
              )}
              <span className="time-video-big" style={{ bottom: "16px" }}>
                {mainDuration}
              </span>
            </div>
            <div className="content-sec">
              <h3 className={style.mainheadtopsection} style={{ margin: "0" }}>
                <Link href={main?.slug ?? "#"}>{main?.title}</Link>
              </h3>
              {main?.excerpt && (
                <p style={{ color: "white" }}>
                  {main?.excerpt
                    ? htmlParser(main?.excerpt.replace(/(<([^>]+)>)/gi, ""))
                    : ""}
                </p>
              )}
              <div>
                <span className="views-big">{mainCount}</span>
              </div>
            </div>
          </div>

          {/* Most Recent Videos */}
          <div className="col-md-12 col-lg-5 col-xl-4 ln-cont card-list-pd">
            <h3 style={{ color: "white", position: "sticky", top: "0" }}>
              Most Recent
            </h3>
            <div className="row right-videos-main">
              <div className="col-md-12">
                {mostRecent.map((item, i) => (
                  <Link
                    key={i}
                    href={item?.slug ?? "#"}
                    className="side-card h-par single-video-tv"
                  >
                    <div
                      className="image h100"
                      style={{ position: "relative" }}
                    >
                      <FiPlay className="playbtnyt" />
                      <div className="time-video">
                        {recentDurations[i] || "Loading..."}
                      </div>
                      <Image
                        fill
                        src={item?.coverImg}
                        alt={item?.title ?? "Video Thumbnail"}
                        className="imgcover"
                      />
                    </div>
                    <div className="content" style={{ color: "white" }}>
                      <h3 className="card-title">{item?.title}</h3>
                      <div>
                        <span
                          className="card-subtitle-thrtv"
                          style={{ color: "#db242a" }}
                        >
                          {recentCounts[i] || "Loading..."}
                        </span>
                        <span
                          className="card-subtitle-thrtv"
                          style={{ color: "white" }}
                        >
                          {dateFormateWithTimeShort(item?.timestamp)}
                        </span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>

        <hr />
      </div>
    </section>
  );
};

export default TopSection;
