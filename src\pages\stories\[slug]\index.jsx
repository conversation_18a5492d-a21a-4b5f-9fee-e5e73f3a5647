import React, { useEffect } from "react";
import useAuthentication from "@/hooks/useAuthentication";
import Hero from "@/components/stories/Hero";
import ReviewWidget from "@/components/stories/ReviewWidget";
import MainContent from "@/components/stories/MainContent";
import YouMayAlsoLike from "@/components/stories/YouMayAlsoLike";
import RelatedStories from "@/components/stories/RelatedStories";
import SeoHeader from "@/components/seo/SeoHeader";
import Layout from "@/components/layout/Layout2";
import { usePathname } from "next/navigation";
import { Const } from "@/utils/Constants";
import { statusLabel } from "@/utils/Util";
import {
  viewArticle,
  getStoriesAuthor,
  getLatestStories,
  getYouMayAlsoLike,
} from "@/pages/api/ArticleApi";
import style from "@/components/stories/Stories.module.css";

const Stories = ({ data, author, breadcrumbs, latest, related, tag, meta }) => {
  const isAuthenticated = useAuthentication();
  const content = data && data.content ? JSON.parse(data.content) : "";
  const pathname = usePathname();
  const handleScroll = () => {
    // window.scrollTo({
    //   top: 660,
    //   behavior: "smooth",
    // });
  };
  useEffect(() => {
    handleScroll();
  }, []);
  if (!isAuthenticated) {
    return (
      <Layout>
        <div className="container" style={{ marginTop: "70px" }}>
          <div className="row" style={{ height: "calc(100vh - 70px)" }}>
            <div className="col-md-12 d-flex align-items-center justify-content-center">
              <h1>Please login to view the content of this page</h1>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <SeoHeader meta={meta} />
      <span className={`status-label sl-${data.status}`}>
        {statusLabel(data.status)}
      </span>
      <Hero
        breadcrumbs={breadcrumbs}
        title={data?.title ?? ""}
        description={data?.excerpt ?? ""}
        author={author ?? []}
        timeline={data?.timestamp ?? ""}
        coverImage={data?.coverImg ?? ""}
        caption={data?.caption ? data?.caption : ""}
        courtesy={data?.courtesy ? data?.courtesy : ""}
        contributor={
          data?.contributor && data?.contributor.length > 0
            ? data?.contributor
            : []
        }
        altName={data?.altName ? data?.altName : ""}
        readTime={data?.readTime ? data?.readTime : 5}
        articleUrl={`${Const.ClientLink}${pathname}`}
      />
      <section id="storiessection" className={style.storiesSection}>
        <div className="container">
          <div className="row">
            <div className="col-md-8">
              <ReviewWidget data={data?.reviews || null} />
              <MainContent
                _id={1}
                content={content}
                note={data?.duplicationNote ?? ""}
                tag={tag}
              />
            </div>
            <div className="col-md-4 ln-cont">
              <RelatedStories data={latest} />
            </div>
          </div>
        </div>
      </section>
      <YouMayAlsoLike data={related} />
    </Layout>
  );
};

export default Stories;

export async function getServerSideProps(context) {
  const { slug } = context.params;
  const url = `/${slug}`;
  const LIMIT = 10;
  try {
    const [storiesRes, authorRes, youMayLikeRes] = await Promise.all([
      viewArticle(url),
      getStoriesAuthor(url),
      getYouMayAlsoLike(url),
    ]);
    if (!storiesRes || Object.keys(storiesRes?.data?.data).length === 0) {
      return {
        notFound: true,
      };
    }

    const latestStoriesPayload = {
      slug: storiesRes?.data?.breadcrumbs[1]?.slug
        ? storiesRes?.data?.breadcrumbs[1]?.slug + url
        : "",
      limit: LIMIT,
    };

    const latestStoriesRes = await getLatestStories(latestStoriesPayload);

    return {
      props: {
        data: storiesRes?.data?.data ?? {},
        breadcrumbs: storiesRes?.data?.breadcrumbs ?? [],
        author:
          authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
        latest: latestStoriesRes?.data ?? [],
        related: youMayLikeRes?.data ?? [],
        tag: storiesRes?.data?.tag ?? [],
        meta: storiesRes?.data?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      notFound: true,
    };
  }
}
