import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import style from "@/components/tag/Tag.module.css";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import MainSection from "@/components/tag/MainSection";
import LatestSection from "@/components/tag/LatestSection";
import Layout from "@/components/layout/Layout2";
import { getTag, getTagLatest } from "@/pages/api/TagApi";
import { Const } from "@/utils/Constants";

const Tag = ({ title, initialData, initialCount, initialLatest, meta }) => {
  const router = useRouter();
  const [data, setData] = useState(initialData);
  const [count, setCount] = useState(initialCount);
  const [latest, setLatest] = useState(initialLatest ?? []);
  const [offset, setOffset] = useState(Const.Offset);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    setData(initialData);
    setCount(initialCount);
    setLatest(initialLatest);
    setOffset(Const.Offset);
    setHasMore(initialData.length < initialCount);
  }, [router.query.category, initialData, initialCount, latest]);

  const handleShowMore = async () => {
    const newOffset = offset + Const.Limit;
    setOffset(newOffset);

    const payload = {
      slug: `/${router.query.slug}`,
      limit: Const.Limit,
      offset: newOffset,
    };

    try {
      const response = await getTag(payload);
      const newItems = response?.data?.data ?? [];
      const totalItems = response?.data?.count ?? 0;
      setData((prev) => [...prev, ...newItems]);
      setOffset(newOffset);
      setCount(totalItems);

      if (newItems.length === 0 || newOffset + Const.Limit >= totalItems) {
        setHasMore(false);
      }
    } catch (e) {
      console.error("Error fetching more tag:", e);
    }
  };

  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <section id="tag-section" className={style.tagSection}>
        <div className="container">
          <div className="row">
            <div className="col-md-8">
              <MainSection
                title={title}
                data={data}
                handleShowMore={handleShowMore}
                hasMore={hasMore}
              />
            </div>
            <div className="col-md-4 ln-cont">
              <LatestSection data={latest} />
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Tag;

export async function getServerSideProps(context) {
  const url = `/${context.params.slug}`;
  const payload = {
    slug: url,
    limit: Const.Limit,
    offset: Const.Offset,
  };

  try {
    const [tagRes, tagLatest] = await Promise.all([
      getTag(payload),
      getTagLatest(url),
    ]);

    if (tagRes?.status !== "success") {
      return {
        notFound: true,
      };
    }
    return {
      props: {
        title: tagRes?.data?.title ?? "",
        initialData: tagRes?.data?.data ?? [],
        initialCount: tagRes?.data?.count ?? 0,
        initialLatest: tagLatest?.data?.data ?? [],
        meta: tagRes?.data?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching category data:", error);
    return { notFound: true };
  }
}
