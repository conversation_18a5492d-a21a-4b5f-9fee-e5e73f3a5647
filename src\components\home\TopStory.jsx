import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { CiPlay1 } from "react-icons/ci";
import { FiPlay } from "react-icons/fi";
import style from "./Home.module.css";
import { IoPlayCircleOutline } from "react-icons/io5";
import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import {
  formatDateAndTime,
  htmlParser,
  getVideoDuration,
  getVideoCount,
} from "@/utils/Util";
import VideoPopup from "../common/VideoPopUp";
import Button from "../common/Button";
import { getYouTubeThumbnail } from "@/helpers/VideoHelper";

const TopStory = ({ showhead, data, seeAll }) => {
  const [open, setOpen] = useState(null);
  const [durations, setDurations] = useState([]);
  const [counts, setCounts] = useState([]);
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent",
        start: "top 60%",
        // markers: true,
      },
    });
  }, []);
  useEffect(() => {
    const fetchDurations = async () => {
      const fetchedDurations = await Promise.all(
        data.map((item) => getVideoDuration(item?.src ?? ""))
      );
      setDurations(fetchedDurations);
    };

    const fetchCounts = async () => {
      const fetchedCounts = await Promise.all(
        data.map((item) => getVideoCount(item?.src ?? ""))
      );
      setCounts(fetchedCounts);
    };

    fetchDurations();
    fetchCounts();
  }, [data]);
  return (
    <>
      {data && data.length > 0 && (
        <>
          <VideoPopup open={open} setOpen={setOpen} videoarray={data} />
          <section id="topstories" className={style.topStories}>
            <div className="container video-block-section">
              {showhead != 0 && (
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <h2>THR VIDEOS</h2>
                  <Button href={seeAll ?? "#"}>SEE ALL</Button>
                </div>
              )}
              <div className="row">
                <div className="col-md-12 col-lg-7 col-xl-8  zoom-cont">
                  <div
                    className="image-sec image-zoom-parent pos-rel"
                    onClick={() => {
                      setOpen(0);
                    }}
                  >
                    {/* <IoPlayCircleOutline className="play-btn" /> */}
                    <div
                      className="play-btn js-btn btn2"
                      href="#"
                      data-btn-scroll="true"
                      data-btn-autoplay="play"
                    >
                      <svg
                        className="svg-thrtv"
                        viewBox="0 0 100 100"
                        preserveAspectRatio="none"
                      >
                        <circle
                          className="c-play-btn_fill c-play-btn_fill_transparent-black"
                          fill="#D32531"
                          stroke="#D32531"
                          style={{ fill: "rgba(0, 0, 0, 0.35)" }}
                          stroke-width="4"
                          cx="50"
                          cy="50"
                          r="48"
                        ></circle>
                        <circle
                          className="js-circle circle"
                          cx="50"
                          cy="50"
                          r="48"
                          stroke="white"
                          stroke-width="4"
                          fill="none"
                        />
                      </svg>
                      <FiPlay className="play-btn-svg" />
                    </div>
                    <div className="time-video-big">
                      {durations[0] || "Loading..."}
                    </div>
                    <Image
                      className="image-zoom imgcover"
                      src={
                        data[0]?.src
                          ? getYouTubeThumbnail(data[0]?.src)
                          : data[0]?.coverImg ?? ""
                      }
                      alt={data[0]?.altName ?? ""}
                      fill
                    />
                  </div>
                  <Link href={data[0]?.slug ?? "/"}>
                    <div className="content-sec">
                      <h3 style={{ margin: "0" }}>{data[0]?.title ?? ""}</h3>
                      {data[0]?.excerpt && (
                        <p>
                          {htmlParser(
                            data[0]?.excerpt?.replace(/(<([^>]+)>)/gi, "") ?? ""
                          )}
                        </p>
                      )}
                      <div>
                        <span className="views-big">
                          {counts[0] || "Loading..."}
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
                <div className="col-md-12 col-lg-5 col-xl-4 ln-cont card-list-pd right-videos-main">
                  <div className="row">
                    <div className="col-md-12">
                      {data?.map(
                        (item, i) =>
                          i !== 0 && (
                            <div
                              className="side-card h-par single-video-tv"
                              key={`thr-video-${i}`}
                            >
                              <div
                                className="image h100"
                                onClick={() => {
                                  setOpen(i);
                                }}
                              >
                                <FiPlay className="play-btn-only play-btn-svg-small" />
                                <div className="time-video">
                                  {durations[i] || "Loading..."}
                                </div>
                                <Image
                                  fill
                                  src={
                                    item?.src
                                      ? getYouTubeThumbnail(
                                          item?.src,
                                          "hqdefault"
                                        )
                                      : item?.coverImg ?? ""
                                  }
                                  alt={item?.altName ?? ""}
                                  className="imgcover"
                                />
                              </div>
                              <Link
                                href={item?.slug ?? "/"}
                                key={`top-story-${i}`}
                              >
                                <div className="content">
                                  {/* <div className="d-flex flex-columnn align-items-center gap-2 fofp">
                                      <span className="cat-video">
                                        {item?.category ?? ""}
                                      </span>
                                      <span className="cat-pub">
                                        {formatDateAndTime(
                                          item?.timestamp ?? ""
                                        )}
                                      </span>
                                    </div> */}
                                  <h3
                                    className="card-title"
                                  >
                                    {item?.title ?? ""}
                                  </h3>
                                  {/* <div>
                                      <span className="card-subtitle-thrtv">
                                        {counts[i] || "Loading..."}
                                      </span>
                                    </div> */}
                                  <div>
                                    <span
                                      className="card-subtitle-thrtv"
                                      style={{ color: "#db242a" }}
                                    >
                                      {counts[i] || "Loading..."}
                                    </span>
                                    <span
                                      className="card-subtitle-thrtv"
                                      style={{ color: "white" }}
                                    >
                                      {formatDateAndTime(item?.timestamp ?? "")}
                                    </span>
                                  </div>
                                </div>
                              </Link>
                            </div>
                          )
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <hr />
            </div>
          </section>
        </>
      )}
    </>
  );
};

export default TopStory;
