import React from 'react'
import <PERSON><PERSON> from '../common/Button'
import Link from 'next/link'

const WrappedBanner2024 = () => {
  return (
    <div id='wrapped-banner-container'>
      <div id='wrapped-banner-vid-container'>
        <video autoPlay muted loop playsInline src="/wrapped/vid.mp4"></video>
        {/* <div id='wrapped-banner-text-container'>
          <h2>THR INDIA WRAPPED 2024</h2>
          <Link target='_blank' href="/wrapped-2024" id='wrapped-banner-button'>
            <h5>Click to open</h5>
          </Link>
        </div> */}
        <div id='wrapped-banner-text-container'>
          <h2>Women in Entertainment The Power List 2025</h2>
          <Link target='_blank' href="/women-in-entertainment-power-list-2025-by-thr-india" id='wrapped-banner-button'>
            <h5>Click to open</h5>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default WrappedBanner2024