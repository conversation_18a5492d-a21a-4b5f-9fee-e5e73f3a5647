import gsap from "gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useRef } from "react";
gsap.registerPlugin(ScrollTrigger);

const Section2 = () => {
  const container = useRef();
  const wrappers = useRef([]);

  const data = [
    {
      name: "The Best Indian Films Of 2024",
      details: [
        "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>gan",
        "Manjummel Boys",
        "Aattam",
        "Lu<PERSON>r Pandhu",
        "Girls Will Be Girls",
        "<PERSON>",
        "Laapataa Ladies",
        "<PERSON><PERSON><PERSON><PERSON>",
        "All We Imagine As Light",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/the-best-indian-films-of-2024-from-all-we-imagine-as-light-to-meiyazhagan-and-amar-singh-chamkila",
      poster: "/wrapped/THE-BEST-INDIAN-FILMS-OF-2024.gif",
      altName: "Best Indian Films Of 2024",
    },
    {
      name: "The 10 Highest-Grossing Films Of 2024",
      details: [
        "Pushpa 2: The Rule (Telugu)",
        "Kalki 2898 AD (Telugu)",
        "Stree 2 (Hindi)",
        "Devara: Part 1 (Telugu)",
        "Bhool Bhulaiyaa 3 (Hindi)",
        "The Greatest of All Time (Tamil)",
        "Singham Again (Hindi)",
        "Amaran (Tamil)",
        "Fighter (Hindi)",
        "Hanu-Man (Telugu)",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/the-10-highest-grossing-films-of-2024-pushpa-2-kalki-2898-ad-stree-2-and-more",
      poster: "/wrapped/Top-10-Highest-Grossing.gif",
      altName: "Highest-Grossing Films Of 2024",
    },
    {
      name: "The 10 Best Hindi Performances of 2024, Ranked",
      details: [
        "Abhishek Bachchan (I Want to Talk)",
        "Konkona Sen Sharma (Killer Soup)",
        "Vivek Gomber (Lootere)",
        "Mukul Chadda (Fairy Folk)",
        "Gyanendra Tripathi (Barah by Barah)",
        "Anjali Anand",
        "Diljit Dosanjh (Amar Singh Chamkila)",
      ],
      more: "https://www.hollywoodreporterindia.com/lists/lists/the-10-best-hindi-performances-of-2024-ranked",
      poster: "/wrapped/HINDI-FILMS.gif",
      altName: "Best Hindi Performances of 2024",
    },
    {
      name: "The 10 Best Tamil Film Performances Of 2024",
      details: [
        "Geetha Kailasam",
        "Manikandan (Lover)",
        "Karthi (Meiyazhagan)",
        "Arvind Swami (Meiyazhagan)",
        "Vijay Sethupathi",
        "Attakathi Dinesh",
        "Anna Ben (Kottukkaali)",
        "Ponvel M. (Vaazhai)",
        "Sai Pallavi (Amaran)",
        "Vikram (Thangalaan)",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/the-10-best-tamil-film-performances-of-2024",
      poster: "/wrapped/Top-10-Tamil-Film.gif",
      altName: "Best Tamil Film Performances Of 2024",
    },
    {
      name: "The 10 Best Malayalam Film Performances Of 2024",
      details: [
        "Asif Ali (Kishkinda Kandam, Level Cross, Adios Amigo) ",
        "Parvathy (Ullozhukku)",
        "Urvashi (Ullozhukku)",
        "Prithviraj Sukumaran (Aadujeevitham)",
        "Kani Kusruti",
        "Vijayaraghavan (Kishkindha Kaandam)",
        "Tovino Thomas (Ajayante Randam Moshanam)",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/the-10-best-malayalam-film-performances-of-2024-urvashi-prithviraj-sukumaran-and-more",
      poster:
        "/wrapped/The-10-Best-Malayalam-Film-Performances-Of-2024 (1).gif",
      altName: "Best Malayalam Film Performances Of 2024",
    },
    {
      name: "The 10 Best Telugu Film Performances of 2024",
      details: [
        "Nivetha Thomas (35 Chinna Katha Kaadu)",
        "Allu Arjun (Pushpa: The Rule) ",
        "Dulquer Salmaan (Lucky Baskhar)",
        "Amitabh Bachchan",
        "Naresh",
        "Sharanya Pradeep",
        "Nani (Saripodha Sanivaaram)",
        "Vishwak Sen (Gaami)",
        "Satya (Mathu Vadalara 2)",
      ],
      more: "https://www.hollywoodreporterindia.com/lists/lists/the-10-best-telugu-film-performances-of-2024-dulquer-salmaan-amitabh-bachchan-and-more",
      poster: "/wrapped/telgu-film-performances.gif",
      altName: "Best Telugu Film Performances of 2024",
    },
    {
      name: "Indian Actors' Fees In 2024",
      details: [
        "The Khans' mode of payment",
        "Where do the others in Bollywood stand?",
        "What is happening in the Tamil and Malayalam industries?",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/from-shah-rukh-khan-allu-arjun-mammooty-to-vijay-star-fees-in-2024",
      poster: "/wrapped/Indian-Actors'-Fees-In-2024.gif",
      altName: "Indian Actors' Fees In 2024",
    },
    {
      name: "Hindi Cinema In 2024",
      details: [
        "When the Cats Are Away…",
        "The Mice Will Play",
        "Mad-dock Or What?",
        "Nayak(s): The Real Heroes",
        "Unsung Melodies",
        "The P word",
        "D Company",
        "Why this Kolaveri Franchise?",
        "Streaming and Tears",
        "Box-of-chocolates Feminism",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/hindi-cinema-in-2024-all-we-imagine-as-bollywood",
      poster: "/wrapped/Hindi-cinema-final-new.gif",
      altName: "Hindi Cinema In 2024",
    },
    {
      name: "The Best Hindi Shows Of 2024, Ranked",
      details: [
        "Killer Soup",
        "Yeh Kaali Kaali Ankhein (Season 2)",
        "Bandish Bandits (Season 2)",
        "Poacher",
        "Raat Jawaan Hai",
      ],
      more: "https://www.hollywoodreporterindia.com/lists/lists/the-best-hindi-shows-of-2024-ranked-raat-jawaan-hai-killer-soup-and-more",
      poster: "/wrapped/Best-hindi-shows-of-2024.gif",
      altName: "Best Hindi Shows Of 2024",
    },
    {
      name: "Kannada Cinema In 2024",
      details: [
        "Shakhahaari",
        "Srinidhi Bengaluru Blink",
        "Moorane Krishnappa",
        "Shivamma Yarehanchinala",
        "Prateek Prajosh's Chilli Chicken",
        "Ibbani Tabbida Ileyali",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/kannada-cinema-in-2024-how-debutant-directors-inspired-confidence-in-promoting-home-grown-talent",
      poster: "/wrapped/Kannada-Cinema-In-2024.gif",
      altName: "Kannada Cinema In 2024",
    },
    {
      name: "Marathi Cinema in 2024",
      details: [
        "Nach Ga Ghuma",
        "Dharmaveer 2",
        "Yek Number",
        "Shivrayancha Chhava",
        "Hya Goshtila Navach Nahi",
        "Gharat Ganpati",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/marathi-cinema-in-2024-how-did-the-industry-fare-this-year",
      poster: "/wrapped/marathi-cinema-in-2024.gif",
      altName: "Marathi Cinema in 2024",
    },
    {
      name: "The Range Of Queer Desire On-Screen in 2024",
      details: [
        "'Sajni' from Laapataa Ladies",
        "Ankhiyaan Gulaab' from Teri Baaton Mein Aisa Uljha Jiya",
        "'Naina' from Crew",
        "'Aaj Ki Raat' from Stree 2",
        "'Tauba Tauba' from Bad Newz",
        "'Tum Se' from Teri Baaton Mein Aisa Uljha Jiya",
        "'Aayi Nai' from Stree 2",
        "'Soni Soni' from Ishq Vishq",
      ],
      more: "https://www.hollywoodreporterindia.com/features/insight/from-bhool-bhulaiyaa-3-to-barzakh-the-range-of-queer-desire-on-screen-in-2024",
      poster: "/wrapped/The-Range-Of-Queer-Desire-On-Screen-in-2024.gif",
      altName: "Range Of Queer Desire On-Screen in 2024",
    },
    {
      name: "Bollywood Celebrity Fashion In 2024",
      details: [
        "Alia Bhatt at the Met Gala",
        "Ananya Panday",
        "Priyanka Chopra Jonas",
        "Ishaan Khatter",
        "Sobhita Dhulipala",
        "Kareena Kapoor Khan",
        "Samantha Ruth Prabhu",
        "Kiara Advani",
        "Saif Ali Khan",
        "Sonam Kapoor Ahuja",
      ],
      more: "https://www.hollywoodreporterindia.com/lifestyle/fashion/bollywood-celebrity-fashion-in-2024-the-10-most-defining-sartorial-moments-of-the-year",
      poster: "/wrapped/Bollywood-Celebrity-Fashion-In-2024.gif",
      altName: "Bollywood Celebrity Fashion In 2024",
    },
    {
      name: "Bollywood Music in 2024",
      details: [
        "'Sajni' from Laapataa Ladies",
        "Ankhiyaan Gulaab",
        "'Naina' from Crew",
        "'Aaj Ki Raat' from Stree 2",
        "'Tauba Tauba' from Bad Newz",
        "'Tum Se' from Teri Baaton Mein Aisa Uljha Jiya",
        "'Aayi Nai' from Stree 2",
        "'Soni Soni' from Ishq Vishq Reloaded",
      ],
      more: "https://www.hollywoodreporterindia.com/features/columns/bollywood-music-in-2024-the-hits-that-ruled-playlists",
      poster: "/wrapped/bollywood-music.gif",
      altName: "Bollywood Music in 2024",
    },
    {
      name: "The Best Indian Pop-Culture Moments Of 2024",
      details: [
        "Dil-Luminati Tour",
        "All We Imagine As Light At Cannes",
        "Eda Mone!",
        "Tauba Tauba",
        "Cringe On Our Phones",
        "India’s Got Latent",
        "Big Dawg’s Big Blow Up",
      ],
      more: "https://www.hollywoodreporterindia.com/features/columns/the-best-indian-pop-culture-moments-of-2024-diljit-dosanjh-hanumankind-tauba-tauba-and-more",
      poster: "/wrapped/The-Best-Indian-Pop-Culture-Moments.gif",
      altName: "Indian Pop-Culture Moments Of 2024",
    },
  ];

  useEffect(() => {
    const wrapHeight = document.querySelectorAll(".wrap-height");
    const wrapHeightCenter = document.querySelector(
      ".wrap-height-center"
    ).offsetHeight;

    wrapHeight.forEach((item) => {
      item.style.height = wrapHeightCenter + "px";
    });

    window.addEventListener("resize", () => {
      wrapHeight.forEach((item) => {
        item.style.height = wrapHeightCenter + "px";
      });
    });
  }, []);

  useEffect(() => {
    if (container.current && wrappers.current.length > 0) {
      wrappers.current.forEach((wrap) => {
        var tl2 = gsap.timeline({
          scrollTrigger: {
            trigger: wrap,
            scroller: "body",
            // markers: true,
            start: "top 80%",
            end: "top 50%",
            scrub: 1,
          },
        });

        tl2

          .from(
            wrap.querySelector(".wrap-center-poster"),
            {
              scale: 0.5,
              opacity: 0,
              y: "80%",
              duration: 1.2,
              ease: "linear",
            },
            "b"
          )
          .from(
            wrap.querySelectorAll(".wrap-height"),
            {
              y: "80%",
              opacity: 0,
              duration: 1.2,
              ease: "linear",
            },
            "b"
          );
      });
    }
  }, []);

  return (
    <div ref={container} id="section2w">
      {data.map((w, i) => (
        <div
          ref={(el) => (wrappers.current[i] = el)}
          key={i}
          className="wrap-slide border-wrap"
        >
          {i === 0 && <div id="wrap-line-top"></div>}
          <div className="wrap-inner wrap-sides wrap-left-remove">
            <div className="wrap-inner-heading wrap-height">
              <h5>[ {i + 1 > 10 ? i + 1 : `0${i + 1}`} ]</h5>
              <h3>{w.name}</h3>
              <Link target="_block" className="readbtn" href={w.more}>
                <h5>Read full article</h5>
              </Link>
            </div>
          </div>
          <div className="wrap-inner wrap-center wrap-height-center">
            <div className="wrap-center-poster">
              {/* <Image
                src={w.poster}
                alt={w.altName}
                layout="intrinsic"
                width={1000} 
                height={500} 
                style={{ width: '100%', height: 'auto' }} 
              /> */}
              <img src={w.poster} alt={w.altName} />
            </div>

            <div className="wrap-text-container-center">
              <h5>[ {i + 1 > 10 ? i + 1 : `0${i + 1}`} ]</h5>
              <h3>{w.name}</h3>
              <Link target="_block" className="readbtn-mb" href={w.more}>
                <h5>Read full article</h5>
              </Link>
            </div>
          </div>
          <div className="wrap-inner wrap-sides">
            <div className="wrap-inner-list wrap-height">
              {w.details.map((d, i) => (
                <div key={i} className="wrap-list-item">
                  <span className="wrap-list-dot"></span>
                  <h4>{d}</h4>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Section2;
