import React, { useEffect } from "react";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Hero from "@/components/chart/Hero";
import HeroImage from "@/assets/images/Graph-new.jpg";
import TopMovies from "@/components/chart/TopMovies";
import TopStreaming from "@/components/chart/TopStreaming";
import Trailar from "@/components/chart/Trailer";
import NewsFeed from "@/components/chart/NewsFeed";
import { getFlaggedPage } from "@/pages/api/HomeApi";

const Charts = ({ meta, sections }) => {
  useEffect(() => {
    if (window.innerWidth >= 992) {
      const items = document.querySelectorAll(".flims-item");
      const largeTexts = document.querySelectorAll(".text--large.is--caps");

      const handleMouseEnter = (item) => {
        items.forEach((otherItem) => {
          if (otherItem !== item) {
            otherItem.classList.add("is--faded");
          }
        });

        const textToHighlight = item.querySelector(".text--large.is--caps");
        if (textToHighlight) {
          textToHighlight.style.color = "red";
        }
      };

      const handleMouseLeave = (item) => {
        items.forEach((otherItem) => {
          otherItem.classList.remove("is--faded");
        });

        const textToReset = item.querySelector(".text--large.is--caps");
        if (textToReset) {
          textToReset.style.color = "";
        }
      };

      items.forEach((item) => {
        item.addEventListener("mouseenter", () => handleMouseEnter(item));
        item.addEventListener("mouseleave", () => handleMouseLeave(item));
      });

      return () => {
        items.forEach((item) => {
          item.removeEventListener("mouseenter", () => handleMouseEnter(item));
          item.removeEventListener("mouseleave", () => handleMouseLeave(item));
        });
      };
    }
  }, [sections]);

  const dummyData = [
    {
      _id: "66eef0df1e2af24f1f82b6c0",
      title: "dsbfd",
      excerpt: "gfdngn",
      coverImg:
        "https://www.hollywoodreporterindia.com/_next/image?url=https%3A%2F%2Fcdn.hollywoodreporterindia.com%2Farticle%2F2025-07-21T04%253A43%253A36.018Z-special-ops-.jpg&w=1920&q=75",
      altName: "fdnkdn'",
      seeAllSlug: "/",
      category: "Features",
      author: [
        {
          name: "Lily Ford",
          slug: "/author/lily-ford",
        },
      ],
      articleTitle: "Special Ops S2",
      articleSubheading: "",
      format: "Fiction Series",
      platform: "JioHotstar",
      views: "6.2 M",
      week: 1,
      weekendBoxOffice: "",
      cumulativeBoxOffice: "",
      slug: "",
      status: 1,
      timestamp: "fsdbfjk",
      sortOrder: null,
      sectionSortOrders: null,
      effectiveSortOrder: "fsdbfjk",
    },
    {
      _id: "66efc181a39f84790ef66a36",
      title: "gsbgjk",
      excerpt: "fsdfjkb",
      coverImg:
        "https://static.vecteezy.com/system/resources/thumbnails/004/141/669/small/no-photo-or-blank-image-icon-loading-images-or-missing-image-mark-image-not-available-or-image-coming-soon-sign-simple-nature-silhouette-in-frame-isolated-illustration-vector.jpg",
      altName: "dfdsjf",
      seeAllSlug: "/",
      category: "Interviews",
      author: [
        {
          name: "Carly  Thomas",
          slug: "/author/carly-thomas",
        },
      ],
      articleTitle: "Mitti - Ek Nayi Pehchaan",
      articleSubheading: "",
      format: "Fiction Series",
      platform: "Amazon MX Player",
      views: "3.2 M",
      week: "",
      weekendBoxOffice: "sdnds",
      cumulativeBoxOffice: "gfgs",
      slug: "",
      status: 1,
      timestamp: "dfdsfj",
      sortOrder: "",
      sectionSortOrders: null,
      effectiveSortOrder: "dfdsfj",
    },
    {
      _id: "66ef10951e99d6a344541df5",
      title:
        "After ‘The Greatest of All Time’, is Vijay the Highest-Paid Asian Actor? \n\n",
      excerpt:
        "The grapevine suggests he charged more than what Robert Downey Jr. was paid for Avengers: Endgame.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-06-24T05%3A33%3A44.797Z-panchayat-2.webp",
      altName: "A Poster from 'Panchayat'",
      seeAllSlug: "/",
      category: "Rambling Reporter",
      author: [
        {
          name: "Team THR India",
          slug: "/author/team-thr-india",
        },
      ],
      articleTitle: "Panchayat S4",
      articleSubheading: "",
      format: "Fiction Series",
      platform: "Amazon Prime Video",
      views: "2.6 M",
      week: 1,
      weekendBoxOffice: "",
      cumulativeBoxOffice: "",
      slug: "/reviews/streaming/jitendra-kumar-and-amazon-primes-panchayat-season-4-series-review-it-takes-a-village-to-break-a-sweat",
      status: 1,
      timestamp: "2024-09-21T18:29:41.704Z",
      sortOrder: 2,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T18:29:41.704Z",
    },
    {
      _id: "66eef6601e99d6a34454160e",
      title: "‘Black Mirror’ Season 7 Cast Revealed",
      excerpt:
        "Awkwafina, Paul Giamatti, Rashida Jones, Issa Rae, Tracee Ellis Ross and returning stars Cristin Milioti and Billy Magnussen are just some of the names leading the 2025 season from creator Charlie Brooker.",
      coverImg: "/the-great-indian-kapil-show-s3.jpeg",
      altName: "A poster from 'The Great Indian Kapil Show S3'",
      seeAllSlug: "/",
      category: "News",
      author: [
        {
          name: "Jackie Strause",
          slug: "/author/jackie-strause",
        },
      ],
      articleTitle: "The Great Indian Kapil Show S3",
      articleSubheading: "",
      format: "Non-Fiction",
      platform: "Netflix",
      views: "2.5 M",
      week: 8,
      weekendBoxOffice: "5000 CR@",
      cumulativeBoxOffice: "@hiu",
      slug: "",
      status: 1,
      timestamp: "2024-09-21T16:37:52.551Z",
      sortOrder: 1,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T16:37:52.551Z",
    },
    {
      _id: "66eef3dd1e99d6a34454152b",
      title:
        "Will Ferrell Says Trans Community’s Support of ‘Will & Harper’ Has “Blown Us Away”",
      excerpt:
        "Ferrell and longtime friend Harper Steele, who recently came out as a trans woman, go on a cross-country road trip in the Netflix doc.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-06-28T07%3A46%3A37.757Z-Untitled%20design%20%281%29.jpg",
      altName:
        "Actors Lee Byung-hun and Lee Jung-jae in stills from season 3 of 'Squid Game'.",
      seeAllSlug: "/",
      category: "News",
      author: [
        {
          name: "Kristen Chuba",
          slug: "/author/kristen-chuba",
        },
      ],
      articleTitle: "Squid Game S3",
      articleSubheading: "",
      format: "Fiction Series",
      platform: "Netflix",
      views: "2.0 M",
      week: 1,
      weekendBoxOffice: "",
      cumulativeBoxOffice: "",
      slug: "/global/interviews/squid-game-season-3-interview-with-actors-lee-byung-hun-and-lee-jung-jae",
      status: 1,
      timestamp: "2024-09-21T16:27:09.875Z",
      sortOrder: 0,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T16:27:09.875Z",
    },
  ];
  const dummyOttData = [
     {
      _id: "66efc181a39f84790ef66a36",
      title:
        "Emily Blunt Says Her Daughters Thought She Was the “Meanest Person” for Her ‘Devil Wears Prada’ Role",
      excerpt:
        "The actress played Emily Charlton, a high-strung assistant at a fictional fashion magazine, in the 2006 film, opposite Meryl Streep and Anne Hathaway.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-07-11T06%3A45%3A27.647Z-Aap%20Jaisa%20Koi.webp",
      altName: "A still from 'Aap Jaisa Koi'",
      seeAllSlug: "/",
      category: "Interviews",
      author: [
        {
          name: "Carly  Thomas",
          slug: "/author/carly-thomas",
        },
      ],
      articleTitle: "Aap Jaisa Koi",
      articleSubheading: "",
      format: "Film",
      platform: "Netflix",
      views: "3.7 M",
      week: 4,
      weekendBoxOffice: "5000 CR",
      cumulativeBoxOffice: "5000000 CR",
      slug: "/reviews/streaming/aap-jaisa-koi-movie-review-a-romcom-thats-watched-too-many-romcoms",
      status: 1,
      timestamp: "2024-09-22T07:04:33.085Z",
      sortOrder: 4,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-22T07:04:33.085Z",
    },
     {
      _id: "66ef10951e99d6a344541df5",
      title:
        "dsfbdfjk",
      excerpt:
        "fdsf",
      coverImg:
        "https://www.hollywoodreporterindia.com/_next/image?url=https%3A%2F%2Fcdn.hollywoodreporterindia.com%2Fundefined2025-06-20T10%253A30%253A51.773Z-Dhanush-Kuberaa%2520review%2520.jpg&w=1920&q=75",
      altName: "dfsdfb'",
      seeAllSlug: "/",
      category: "dgg",
      author: [
        {
          name: "Team THR India",
          slug: "/author/team-thr-india",
        },
      ],
      articleTitle: "Kuberaa",
      articleSubheading: "",
      format: "Theatrical Film",
      platform: "Amazon Prime Video",
      views: "2.5 M",
      week: "",
      weekendBoxOffice: "",
      cumulativeBoxOffice: "",
      slug: "",
      status: 1,
      timestamp: "dfj",
      sortOrder: "",
      sectionSortOrders: null,
      effectiveSortOrder: "dfj",
    },
    {
      _id: "66eef3dd1e99d6a34454152b",
      title:
        "Will Ferrell Says Trans Community’s Support of ‘Will & Harper’ Has “Blown Us Away”",
      excerpt:
        "Ferrell and longtime friend Harper Steele, who recently came out as a trans woman, go on a cross-country road trip in the Netflix doc.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-06-05T09%3A02%3A42.672Z-Lead-20.jpg",
      altName: "A still from 'Thug Life'",
      seeAllSlug: "/",
      category: "News",
      author: [
        {
          name: "Kristen Chuba",
          slug: "/author/kristen-chuba",
        },
      ],
      articleTitle: "Thug Life",
      articleSubheading: "",
      format: "Theatrical Film",
      platform: "Netflix",
      views: "2.0 M",
      week: 1,
      weekendBoxOffice: "",
      cumulativeBoxOffice: "",
      slug: "/reviews/theatrical/thug-life-movie-review-this-mani-ratnam-kamal-haasan-drama-is-conceptually-fascinating-emotionally-distant",
      status: 1,
      timestamp: "2024-09-21T16:27:09.875Z",
      sortOrder: 0,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T16:27:09.875Z",
    },
    {
      _id: "66eef6601e99d6a34454160e",
      title: "‘Black Mirror’ Season 7 Cast Revealed",
      excerpt:
        "Awkwafina, Paul Giamatti, Rashida Jones, Issa Rae, Tracee Ellis Ross and returning stars Cristin Milioti and Billy Magnussen are just some of the names leading the 2025 season from creator Charlie Brooker.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-05-01T05%3A57%3A22.926Z-Untitled%20design%20%287%29.jpg",
      altName: "Ajay Devgn and Riteish Deshmukh Raid 2",
      seeAllSlug: "/",
      category: "News",
      author: [
        {
          name: "Jackie Strause",
          slug: "/author/jackie-strause",
        },
      ],
      articleTitle: "Raid 2",
      articleSubheading: "",
      format: "Theatrical Film",
      platform: "Netflix",
      views: "1.7 M",
      week: 8,
      weekendBoxOffice: "5000 CR@",
      cumulativeBoxOffice: "@hiu",
      slug: "/reviews/theatrical/raid-2-movie-review-ajay-devgns-taxman-thriller-is-taxing-and-overstaffed",
      status: 1,
      timestamp: "2024-09-21T16:37:52.551Z",
      sortOrder: 1,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T16:37:52.551Z",
    },
   
    {
      _id: "66eef0df1e2af24f1f82b6c0",
      title:
        "Disney to Spend $1B a Year in Europe, Warner Bros. Discovery Exec Looks to 2026 Max Launch in U.K.\n",
      excerpt:
        "Disney's Nami Patel spoke about the \"knock-on impact\" of investment in the U.K., citing hit blockbuster 'Deadpool & Wolverine' which was filmed at London's Pinewood Studios.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-07-02T10%3A21%3A02.903Z-heads%20of.png",
      altName:
        "John Cena, Idris Elba and Priyanka Chopra Jonas in 'Heads of State'",
      seeAllSlug: "/",
      category: "Features",
      author: [
        {
          name: "Lily Ford",
          slug: "/author/lily-ford",
        },
      ],
      articleTitle: "Heads of State",
      articleSubheading: "",
      format: "Film",
      platform: "Amazon Prime Video",
      views: "1.5 M",
      week: 1,
      weekendBoxOffice: "",
      cumulativeBoxOffice: "",
      slug: "/reviews/streaming/heads-of-state-movie-review-priyanka-chopra-steals-the-show-in-cena-and-elbas-banter-filled-comedy",
      status: 1,
      timestamp: "2024-09-21T16:14:23.898Z",
      sortOrder: null,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T16:14:23.898Z",
    },
   
  ];
  const dummyTitle = {
    title: "Top 5 Original Series",
    subhead:
      "The most-watched shows making waves on OTT platforms in India this week.",
  };
  const dummyOttTitle = {
    title: "Top 5 Films on OTT",
    subhead:
      "The most-watched movies dominating OTT platforms in India this week.",
  };
  const dummyTitleTopMovies = {
    title: "Top 5 Theatrical Hits",
    subhead: "The most-watched films ruling the big screen in India this week.",
  };

  const dummyTopMovies = [
      {
      _id: "67164e2aa665b7562df67c1b",
      title: "ugisud",
      excerpt: "ibob j h jk gjh ihjjhh. h",
      coverImg: "https://www.hollywoodreporterindia.com/_next/image?url=https%3A%2F%2Fcdn.hollywoodreporterindia.com%2Farticle%2F2025-07-18T11%253A49%253A36.257Z-Tanvi%2520%25282%2529.jpg&w=1920&q=75",
      altName: "Kajol in a still from 'Superman'",
      seeAllSlug: "/",
      category: "Interviews",
      author: [
        {
          name: "Lily Ford",
          slug: "/author/lily-ford",
        },
      ],
      articleTitle: "Saiyaara",
      articleSubheading: "",
      originalLanguage: "Hindi",
      platform: "",
      views: "",
      week: 1,
      weekendBoxOffice: "98Cr",
      cumulativeBoxOffice: "98Cr",
      slug: "",
      status: 1,
      timestamp: "",
      sortOrder: null,
      sectionSortOrders: {
        sectionId: "",
        sortOrder: 1,
        _id: "",
      },
      effectiveSortOrder: 1,
    },
    {
      _id: "66ef10951e99d6a344541df5",
      title:
        "After ‘The Greatest of All Time’, is Vijay the Highest-Paid Asian Actor? \n\n",
      excerpt:
        "The grapevine suggests he charged more than what Robert Downey Jr. was paid for Avengers: Endgame.",
      coverImg: "/jurassic-world-rebirth.jpeg",
      altName: "A Still from 'Jurassic World Rebirth'",
      seeAllSlug: "/",
      category: "Rambling Reporter",
      author: [
        {
          name: "Team THR India",
          slug: "/author/team-thr-india",
        },
      ],
      articleTitle: "Jurassic World Rebirth",
      articleSubheading: "",
      originalLanguage: "Hollywood",
      platform: "",
      views: "",
      week: 3,
      weekendBoxOffice: "12Cr",
      cumulativeBoxOffice: "113Cr",
      slug: "",
      status: 1,
      timestamp: "2024-09-21T18:29:41.704Z",
      sortOrder: 2,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T18:29:41.704Z",
    },
    {
      _id: "67164e2aa665b7562df67c1b",
      title: "ugisud",
      excerpt: "ibob j h jk gjh ihjjhh. h",
      coverImg: "/superman.jpeg",
      altName: "Kajol in a still from 'Superman'",
      seeAllSlug: "/",
      category: "Interviews",
      author: [
        {
          name: "Lily Ford",
          slug: "/author/lily-ford",
        },
      ],
      articleTitle: "Superman",
      articleSubheading: "",
      originalLanguage: "Hollywood",
      platform: "",
      views: "",
      week: 2,
      weekendBoxOffice: "10Cr",
      cumulativeBoxOffice: "53Cr",
      slug: "",
      status: 1,
      timestamp: "2024-10-21T12:50:50.183Z",
      sortOrder: null,
      sectionSortOrders: {
        sectionId: "670c1fe812ae4d10c33bb945",
        sortOrder: 1,
        _id: "67164e2aa665b7562df67c1c",
      },
      effectiveSortOrder: 1,
    },
    {
      _id: "66eef6601e99d6a34454160e",
      title: "‘Black Mirror’ Season 7 Cast Revealed",
      excerpt:
        "Awkwafina, Paul Giamatti, Rashida Jones, Issa Rae, Tracee Ellis Ross and returning stars Cristin Milioti and Billy Magnussen are just some of the names leading the 2025 season from creator Charlie Brooker.",
      coverImg:
        "https://cdn.hollywoodreporterindia.com/article/2025-06-25T08%3A54%3A28.929Z-f1%20movie2.jpg",
      altName: "A still from 'F1'",
      seeAllSlug: "/",
      category: "News",
      author: [
        {
          name: "Jackie Strause",
          slug: "/author/jackie-strause",
        },
      ],
      articleTitle: "F1",
      articleSubheading: "",
      originalLanguage: "Hollywood",
      platform: "",
      views: "",
      week: 4,
      weekendBoxOffice: "8Cr",
      cumulativeBoxOffice: "98Cr",
      slug: "/reviews/theatrical/f1-movie-review-brad-pitts-sports-drama-is-a-classic-summer-blockbuster",
      status: 1,
      timestamp: "2024-09-21T16:37:52.551Z",
      sortOrder: 1,
      sectionSortOrders: null,
      effectiveSortOrder: "2024-09-21T16:37:52.551Z",
    },
     {
      _id: "67164e2aa665b7562df67c1b",
      title: "ugisud",
      excerpt: "ibob j h jk gjh ihjjhh. h",
      coverImg: "https://www.hollywoodreporterindia.com/_next/image?url=https%3A%2F%2Fcdn.hollywoodreporterindia.com%2Farticle%2F2025-07-18T10%253A29%253A29.096Z-Untitled%2520design-66.jpg&w=1920&q=75",
      altName: "kggg",
      seeAllSlug: "/",
      category: "Interviews",
      author: [
        {
          name: "Lily Ford",
          slug: "/author/lily-ford",
        },
      ],
      articleTitle: "Ekka",
      articleSubheading: "",
      originalLanguage: "Kannada",
      platform: "",
      views: "",
      week: 1,
      weekendBoxOffice: "7Cr",
      cumulativeBoxOffice: "7Cr",
      slug: "",
      status: 1,
      timestamp: "",
      sortOrder: null,
      sectionSortOrders: {
        sectionId: "",
        sortOrder: 1,
        _id: "",
      },
      effectiveSortOrder: 1,
    },
    
  ];

  return (
    <>
      <Layout>
        <SeoHeader meta={meta} />
        <BreadcrumbSchema />
        <Hero title={"Charts"} coverImg={HeroImage} />
        <section className="Chart_section">
          <TopMovies
            // title={data[sections[0]]?.title ?? ""}
            title={dummyTitleTopMovies.title ?? ""}
            // description={data[sections[0]]?.description ?? ""}
            description={dummyTitleTopMovies.subhead ?? ""}
            // data={data[sections[0]]?.data ?? []}
            data={dummyTopMovies ?? []}
          />
          <TopStreaming
            // title={data[sections[1]]?.title ?? ""}
            title={dummyOttTitle.title ?? ""}
            description={dummyOttTitle.subhead ?? ""}
            // description={data[sections[1]]?.description ?? ""}
            // data={data[sections[1]]?.data ?? []}
            data={dummyOttData ?? []}
          />
          <TopStreaming
            // title={data[sections[1]]?.title ?? ""}
            title={dummyTitle.title ?? ""}
            description={dummyTitle.subhead ?? ""}
            // description={data[sections[1]]?.description ?? ""}
            // data={data[sections[1]]?.data ?? []}
            data={dummyData ?? []}
          />
        </section>
        <section className="py-2">
          <div className="container">
            <div className="row">
              <div className="col-md-12">
                <div className="Chart--heading-cntr text-center">
                  <h2 className="data-source-heading">
                    Data Source: Ormax Media
                  </h2>
                  <p className="chart--small--title m-0">
                    All figures are estimated Gross box office collections in
                    India,
                    <br />
                    accounting for all language versions of films that are
                    released in multiple languages.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        <Trailar data={sections[2]?.stories ?? []} />
        <NewsFeed
          title={sections[3]?.title ?? ""}
          data={sections[3]?.stories ?? []}
        />
      </Layout>
    </>
  );
};

export default Charts;

export async function getServerSideProps(context) {
  try {
    const response = await getFlaggedPage("/charts");
    if (response.status !== "success") {
      return {
        props: {
          notFound: true,
        },
      };
    }
    const { meta, sections } = response?.data;
    return {
      props: {
        sections: sections && sections.length > 0 ? sections : [],
        meta: meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        data: [],
        meta: {
          title: "Highest-Grossing Films & Top Streaming Originals | THR",
          description:
            "Explore The Hollywood Reporter India's exclusive weekly guide to the highest-grossing films and top streaming originals that people are watching across India.",
          keywords: [],
          author: "RPSG Media",
          robots: "index, follow",
        },
      },
    };
  }
}
