import { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import { getInfiniteStories } from "@/pages/api/ArticleApi";

export const useContinuousScroll = ({
  _id,
  data,
  author,
  breadcrumbs,
  latest,
  related,
  tag,
  slug,
  meta,
}) => {
  const router = useRouter();
  console.log(router.query, " in contiunuors scorll");

  const [stories, setStories] = useState([
    { _id, data, author, breadcrumbs, latest, related, tag, slug, meta },
  ]);
  const [loading, setLoading] = useState(false);
  const [hasMoreBottom, setHasMoreBottom] = useState(true);
  const [visibleSlug, setVisibleSlug] = useState(slug);

  const lastUrlUpdate = useRef(Date.now());
  const currentVisibleSlug = useRef(slug);

  const bottomObserver = useRef(null);
  const storyRefs = useRef({});
  const visibilityObserver = useRef(null);

  // Track last non-promotional article for pagination
  const lastNonPromotionalId = useRef({
    // cursor: data.isPromotional ? null : _id,
    cursor: null,
    slug,
  });
  const baseStory = useRef(_id);
  const lastPromotionalId = useRef(null);
  // const lastPromotionalId = useRef(data.isPromotional ? _id : null);

  // Fetch more stories when reaching the bottom
  useEffect(() => {
    if (stories[stories.length - 1]?._id) {
      fetchStories(
        lastNonPromotionalId.current.cursor,
        lastNonPromotionalId.current.slug,
        lastPromotionalId.current,
        baseStory.current
        // stories[stories.length - 1]?._id,
        // stories[stories.length - 1]?.slug
      );
    }
  }, []);

  useEffect(() => {
    if (!visibleSlug) return;

    currentVisibleSlug.current = visibleSlug;

    router.push(`${visibleSlug}`, `${visibleSlug}`, {
      shallow: true,
      scroll: false,
    });
  }, [visibleSlug]);

  // Visibility detection to work in both scroll directions
  useEffect(() => {
    if (visibilityObserver.current) {
      visibilityObserver.current.disconnect();
    }

    // Create a new observer with multiple thresholds for better accuracy
    visibilityObserver.current = new IntersectionObserver(
      (entries) => {
        // Track all stories and their visibility percentages
        const visibilityMap = {};

        // Process all entries to update visibility map
        entries.forEach((entry) => {
          const storyId = entry.target.getAttribute("data-id");
          const storySlug = entry.target.getAttribute("data-slug");
          const markerPosition = entry.target.getAttribute("data-position");
          const isLast = entry.target.getAttribute("islast");

          if (!visibilityMap[storyId]) {
            visibilityMap[storyId] = {
              slug: storySlug,
              markers: {},
              totalVisibility: 0,
            };
          }

          if (isLast) {
            bottomObserver.current = entry.target;
          }

          // Update marker visibility
          visibilityMap[storyId].markers[markerPosition] = entry.isIntersecting
            ? entry.intersectionRatio
            : 0;

          // Recalculate total visibility score
          // Give more weight to markers in the middle of the viewport
          const weights = {
            top: 1,
            upperMiddle: 2,
            middle: 3,
            lowerMiddle: 2,
            bottom: 1,
          };

          let totalScore = 0;
          let totalWeight = 0;

          Object.entries(visibilityMap[storyId].markers).forEach(
            ([position, ratio]) => {
              totalScore += +ratio * weights[position];
              totalWeight += weights[position];
            }
          );

          visibilityMap[storyId].totalVisibility =
            totalWeight > 0 ? totalScore / totalWeight : 0;
        });

        // Find the most visible story
        let maxVisibility = 0.1; // Minimum threshold to consider a story visible
        let mostVisibleSlug = null;

        Object.values(visibilityMap).forEach((story) => {
          if (story.totalVisibility > maxVisibility) {
            maxVisibility = story.totalVisibility;
            mostVisibleSlug = story.slug;
          }
        });

        // Only update if we have a visible story and it's different from current
        if (mostVisibleSlug && mostVisibleSlug !== currentVisibleSlug.current) {
          // Apply time-based throttling to prevent rapid URL changes
          const now = Date.now();
          if (now - lastUrlUpdate.current > 200) {
            // 200ms minimum between updates
            // Always update URL regardless of scroll direction
            setVisibleSlug(mostVisibleSlug);
            lastUrlUpdate.current = now;

            // Determine scroll direction by finding indexes
            const currentIndex = stories.findIndex(
              (story) => story.slug === currentVisibleSlug.current
            );
            const newIndex = stories.findIndex(
              (story) => story.slug === mostVisibleSlug
            );
            const isScrollingDown = newIndex > currentIndex;

            // Only fetch more stories when scrolling DOWN to the last item
            if (
              isScrollingDown &&
              bottomObserver.current &&
              bottomObserver.current.getAttribute("islast") === "true" &&
              hasMoreBottom
            ) {
              fetchStories(
                lastNonPromotionalId.current.cursor,
                lastNonPromotionalId.current.slug,
                lastPromotionalId.current,
                baseStory.current
                // stories[stories.length - 1]?._id,
                // stories[stories.length - 1]?.slug
              );
            }
          }
        }
      },
      {
        // Added more thresholds for finer-grained visibility detection
        threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
        rootMargin: "-10% 0px", // Adjusted rootMargin to focus on center of viewport
      }
    );

    // Improved marker placement for better visibility detection in both directions
    Object.entries(storyRefs.current).forEach(([id, storyEl], index) => {
      if (!storyEl) return;

      const slug = storyEl.getAttribute("data-slug");
      const isLast = storyEl.getAttribute("islast") === "true";

      // Create 5 observation points with named positions
      const positions = [
        { name: "top", percent: 10 },
        { name: "upperMiddle", percent: 30 },
        { name: "middle", percent: 50 },
        { name: "lowerMiddle", percent: 70 },
        { name: "bottom", percent: 90 },
      ];

      // Remove any existing markers first
      const existingMarkers = storyEl.querySelectorAll('[data-marker="true"]');
      existingMarkers.forEach((marker) => marker.remove());

      // Create new markers
      positions.forEach(({ name, percent }) => {
        const markerEl = document.createElement("div");
        markerEl.style.position = "absolute";
        markerEl.style.left = "0";
        markerEl.style.width = "100%";
        markerEl.style.height = "10px";
        markerEl.style.top = `${percent}%`;
        markerEl.style.pointerEvents = "none";
        markerEl.style.zIndex = "-1";
        markerEl.setAttribute("data-id", id);
        markerEl.setAttribute("data-slug", slug);
        markerEl.setAttribute("data-position", name);
        markerEl.setAttribute("data-marker", "true");
        markerEl.setAttribute("islast", isLast ? "true" : "false");

        // Make sure the story container has position relative
        if (getComputedStyle(storyEl).position === "static") {
          storyEl.style.position = "relative";
        }

        storyEl.appendChild(markerEl);
        visibilityObserver.current.observe(markerEl);
      });

      // Also observe the story container itself
      visibilityObserver.current.observe(storyEl);
    });

    return () => {
      if (visibilityObserver.current) {
        visibilityObserver.current.disconnect();
      }
    };
  }, [stories]);

  const fetchStories = useCallback(
    async (cursor, slug, promotionalCursor, baseStory) => {
      if (loading || !hasMoreBottom) return;
      setLoading(true);
      try {
        const { data } = await getInfiniteStories(
          cursor,
          slug,
          promotionalCursor,
          baseStory
        );

        if (data.data?.length > 0) {
          // Process the new articles and update state
          setStories((prev) => [...prev, ...data.data]);

          // Track the last non-promotional ID for better pagination
          data.data.forEach((article) => {
            if (!article.data?.isPromotional) {
              lastNonPromotionalId.current = {
                cursor: article._id,
                slug: article.slug,
              };
            } else {
              lastPromotionalId.current = article._id;
            }
          });

          setHasMoreBottom(true);
        } else {
          // If the API returns an empty array or no data, there are no more stories
          setHasMoreBottom(false);
        }
      } catch (error) {
        console.error("Error fetching infinite stories:", error);
        setHasMoreBottom(false); // Also stop fetching on error
      } finally {
        setLoading(false);
      }
    },
    [loading, hasMoreBottom]
  );

  // Function to register refs for each story
  const registerStoryRef = useCallback(
    (element, id, storySlug) => {
      if (element) {
        storyRefs.current[id] = element;
        element.setAttribute("data-id", id);
        element.setAttribute("data-slug", storySlug);
        element.setAttribute(
          "islast",
          id.toString() === stories[stories.length - 1]?._id.toString()
            ? "true"
            : "false"
        );
      } else {
        delete storyRefs.current[id];
      }
    },
    [stories]
  );

  return {
    stories,
    visibleSlug,
    registerStoryRef,
    lastNonPromotionalId,
    lastPromotionalId,
    baseStory,
    fetchStories,
  };
};
