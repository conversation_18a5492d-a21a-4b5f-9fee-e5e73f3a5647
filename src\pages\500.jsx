import React, { useState } from "react";
import SeoHeader from "@/components/seo/SeoHeader";
import Link from "next/link";
import Layout from "@/components/layout/Layout";
import { FaInstagram } from "react-icons/fa6";
import { RiInstagramFill } from "react-icons/ri";
import { BsTwitterX } from "react-icons/bs";
import { PiYoutubeLogo } from "react-icons/pi";
import { FaYoutube } from "react-icons/fa";

const Custom500 = ({ meta }) => {
  const [hover, setHover] = useState(null);
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <div className="not-found-div">
        <div className="error-head">
          <p className="error-p-main">Under Maintainance</p>
          <p className="error-p-main3">
            Sorry, our site is under maintainance, meanwhile you can checkout
            our social media handles.
          </p>
          <div className="social-500">
            <div
              className="social-div-main"
              onMouseEnter={() => {
                setHover(1);
              }}
              onMouseLeave={() => {
                setHover(null);
              }}
            >
              {hover == 1 ? (
                <Link
                  href={"https://instagram.com/hollywoodreporterindia"}
                  target="_blank"
                  className="tn-elm"
                >
                  <RiInstagramFill />
                </Link>
              ) : (
                <Link
                  href={"https://instagram.com/hollywoodreporterindia"}
                  target="_blank"
                  className="tn-elm"
                >
                  <FaInstagram />
                </Link>
              )}
            </div>

            <div
              className="social-div-main"
              onMouseEnter={() => {
                setHover(3);
              }}
              onMouseLeave={() => {
                setHover(null);
              }}
              style={{ position: "relative" }}
            >
              <Link
                href={"https://twitter.com/thrindia_"}
                target="_blank"
                className={hover == 3 ? "tn-elm" : "tn-elm visihidden"}
                style={{ position: "absolute", left: "-7px" }}
              >
                <img src="/x.png" className="xsvg"></img>
              </Link>

              <Link
                href={"https://twitter.com/thrindia_"}
                target="_blank"
                className={hover == 3 ? "tn-elm visihidden" : "tn-elm"}
              >
                <BsTwitterX style={{ fontSize: "18px" }} />
              </Link>
            </div>
            <div
              className="social-div-main"
              onMouseEnter={() => {
                setHover(4);
              }}
              onMouseLeave={() => {
                setHover(null);
              }}
            >
              {hover == 4 ? (
                <Link
                  href={"https://www.youtube.com/@HollywoodReporterIndia"}
                  target="_blank"
                  className="tn-elm"
                >
                  <FaYoutube />
                </Link>
              ) : (
                <Link
                  href={"https://www.youtube.com/@HollywoodReporterIndia"}
                  target="_blank"
                  className="tn-elm"
                >
                  <PiYoutubeLogo />
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Custom500;

export async function getStaticProps(context) {
  const url = context.resolvedUrl;
  try {
    const metaData = {
      title:
        "The Hollywood Reporter - Movie news, TV news, awards news, lifestyle news, business news and more from The Hollywood Reporter.",
      desctiption:
        "Movie news, TV news, awards news, lifestyle news, business news and more from The Hollywood Reporter.",
      keywords:
        "Movie news, TV news, awards news, lifestyle news, business news and more from The Hollywood Reporter.",
      author: "THR",
      robots: "noindex,nofollow"
    };
    return {
      props: {
        meta: metaData,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
  }
}
