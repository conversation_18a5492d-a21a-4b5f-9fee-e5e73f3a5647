import React, { useRef, useState, useEffect } from "react";
import Link from "next/link";
import { FiPlay } from "react-icons/fi";
import Image from "next/image";
import {
  dateFormateWithTimeShort,
  getVideoDuration,
  getVideoCount,
} from "@/utils/Util";
import VideoPopup from "../common/VideoPopUp";

const YoutubeSubcat = ({ key, heading, data }) => {
  const [open, setOpen] = useState(null);
  const [durations, setDurations] = useState([]);
  const [counts, setCounts] = useState([]);
  const videoRefs = useRef([]);

  useEffect(() => {
    const fetchDurations = async () => {
      const fetchedDurations = await Promise.all(
        data.map((item) => getVideoDuration(item?.src ?? ""))
      );
      setDurations(fetchedDurations);
    };

    const fetchCounts = async () => {
      const fetchedCounts = await Promise.all(
        data.map((item) => getVideoCount(item?.src ?? ""))
      );
      setCounts(fetchedCounts);
    };

    fetchDurations();
    fetchCounts();
  }, [data]);

  const handleMouseEnter = (index) => {
    const video = videoRefs.current[index];
    if (video && video.play) {
      video.play();
    }
  };

  const handleMouseLeave = (index) => {
    const video = videoRefs.current[index];
    if (video && video.pause) {
      video.pause();
      video.currentTime = 0;
    }
  };

  // const handleThumbnailClick = (link) => {
  //   window.scrollTo({ top: 0, behavior: "smooth" });
  //   setPlayVideo(link);
  // };

  return (
    <>
      <section id="top-videos" key={key}>
        <div className="container">
          <div className="row">
            <div
              className="col-md-12 flex-all"
              // style={{ marginBottom: "50px" }}
            >
              <h1 className="heading-title" style={{ margin: "10px 0" }}>{heading}</h1>
            </div>
          </div>
          <div className="row">
            {data.map((item, i) => (
              <div className="col-md-3 rn-card" key={`top-videos-${i}`}>
                <div className="card-wrapper">
                  <div className="feature-box" onClick={() => setOpen(i)}>
                    <FiPlay className="playbtnyt" />
                    <div className="video-timestamp">
                      {durations[i] || "Loading..."}
                    </div>
                    <Image
                      src={item?.coverImg ?? ""}
                      alt={item?.title ?? ""}
                      fill
                    />
                  </div>
                  <Link href={item?.slug ?? "#"} className="content-sec">
                    <h3 className="card-title">{item?.title ?? ""}</h3>
                    <span
                      className="view-timeline"
                      style={{ color: "#db242a" }}
                    >{`${counts[i] || "Loading..."} `}</span>
                    <span className="view-timeline">
                      | {dateFormateWithTimeShort(item?.timestamp ?? "")}
                    </span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      <VideoPopup open={open} setOpen={setOpen} videoarray={data} />
    </>
  );
};

export default YoutubeSubcat;
