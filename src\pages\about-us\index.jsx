import React from "react";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";

const AboutUs = ({ meta }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div id="content-wrapper" className="lrv-a-wrapper lrv-u-margin-tb-2">
        <div className="lrv-a-grid a-cols3@desktop u-grid-gap-3@desktop-xl">
          <div className="a-span2@desktop">
            <h1 className="lrv-u-font-family-secondary lrv-u-font-size-50 lrv-u-line-height-small lrv-u-font-weight-normal">
              Masthead
            </h1>
            <div className="a-content lrv-u-line-height-copy lrv-u-font-family-body lrv-u-font-size-16 lrv-u-font-size-18@desktop">
              <div>
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}F
                >
                  THE HOLLYWOOD REPORTER INDIA
                </strong>
                {"  "}

                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Avarna Jain
                  </strong>
                  {"  "}
                  Chairperson
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Jamal Shaikh
                  </strong>
                  {"  "}
                  Chief Operating Officer
                </p>
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Anupama Chopra
                  </strong>
                  {"  "}
                  Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Sneha Menon Desai
                  </strong>
                  {"  "}
                  Deputy Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong className="heading-masthead" style={{ fontWeight: "700" }}>
                    Gautam Sunder
                  </strong>{"  "}
                  Digital Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong className="heading-masthead" style={{ fontWeight: "700" }}>
                    Arshia Dhar
                  </strong>{"  "}
                  Associate Editor
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Reviews
                </strong>
                {"  "}
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Vishal Menon
                  </strong>
                  {"  "}
                  Associate Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Rahul Desai
                  </strong>
                  {"  "}
                  Senior Film Critic
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Sruthi Ganapathy Raman  
                  </strong>
                  {"  "}
                  Film Critic
                  </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Prathyush Parasuraman
                  </strong>
                  {"  "}
                  Contributing Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Suchin Mehrotra
                  </strong>
                  {"  "}
                  Critic, Streaming Shows
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong className="heading-masthead" style={{ fontWeight: "700" }}>Kairam Vashi (Rajiv B G.)</strong>{"  "}
                  Film Critic, Kannada & Telugu
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Copy
                </strong>
                {"  "}
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong className="heading-masthead" style={{ fontWeight: "700" }}>Shilajit Mitra  </strong>{"  "}
                  Assistant Digital Editor and Copy Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong className="heading-masthead" style={{ fontWeight: "700" }}>Justin Rao</strong>{"  "}
                  Digital Writer
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Ananya Shankar
                  </strong>
                  {"  "}
                  Features Writer and Sub-editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Anushka Halve
                  </strong>
                  {"  "}
                  Junior Writer
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Photos & Video
                </strong>
                {"  "}
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Arpit Sawant
                  </strong>
                  {"  "}
                  Producer
                </p>
                 <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Anubhav Shukla
                  </strong>
                  {"  "}
                  Director of Photography and Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Aashray Rao
                  </strong>
                  {"  "}
                  Director of Photography
                </p>
               
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Tasha Jaiswal
                  </strong>
                  {"  "}
                  Post Production Editor
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Virendra Suryawanshi
                  </strong>
                  {"  "}
                  Production Supervisor
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Digital Media
                </strong>
                {"  "}
                <br />
               
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Suvigya Buch
                  </strong>
                  {"  "}
                  Social Media Lead & Content Producer
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong className="heading-masthead" style={{ fontWeight: "700" }}>Keerat Kohli</strong>{"  "}
                  Senior Social Media Executive
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Shaurya Shikhar
                  </strong>
                  {"  "}
                  YouTube Strategist
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Art & Web Design
                </strong>
                {"  "}
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Jit Ray
                  </strong>
                  {"  "}
                  Creative Director
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Sunil Kumar
                  </strong>
                  {"  "}
                  Art Director
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Amit Malik
                  </strong>
                  {"  "}
                  Art Director
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Azad Mohan Panwar
                  </strong>
                  {"  "}
                  Associate Art Director
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Kishore Rawat
                  </strong>
                  {"  "}
                  Designer
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Rohit Tiwari
                  </strong>
                  {"  "}
                  Web Design & Development
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Vikas Gupta
                  </strong>
                  {"  "}
                  Web Design & Development
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Avaneesh Kumar
                  </strong>
                  {"  "}
                  SEO Strategist
                </p>
                <br />
                <p>
                  <span style={{ color: "#d92128", fontSize: "20px" }}>
                    ♦♦♦♦♦
                  </span>
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Raas Taneja
                  </strong>
                  {"  "}
                  Chief Finance Officer
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Jabir Merchant
                  </strong>
                  {"  "}
                  Chief Revenue Officer
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Akansha Joshi
                  </strong>
                  {"  "}
                  Business Head
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Advertising and Sales
                </strong>
                {"  "}
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Khush Lulla
                  </strong>
                  {"  "}
                  Manager (Ad Sales)
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Ashma Sharma
                  </strong>
                  {"  "}
                  Assistant Ad Sales Manager
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Lucita D'cruz
                  </strong>
                  {"  "}
                  Deputy Manager, Brand Solutions
                </p>
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Amit Vasant Rane
                  </strong>
                  {"  "}
                  Office Admin Head
                </p>
                <br />
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  Events and Marketing
                </strong>
                {"  "}
                <br />
                <p className="contact-p2" style={{ margin: "0" }}>
                  <strong
                    className="heading-masthead"
                    style={{ fontWeight: "700" }}
                  >
                    Anupam Sehgal
                  </strong>
                  {"  "}
                  Head of Marketing and Events
                </p>

                <br />
                <p>
                  <span style={{ color: "#d92128", fontSize: "20px" }}>
                    ♦♦♦♦♦
                  </span>
                </p>
                <p
                  className="contact-p"
                  style={{ margin: "0", color: "#d92128", fontWeight: "700" }}
                >
                  The Hollywood Reporter India is published by Business Media
                  Pvt Ltd under licence from Penske Media Corporation.
                </p>
                <br />
                <p
                  className="contact-p"
                  style={{ margin: "0", fontWeight: "700" }}
                >
                  RPSG Lifestyle Media
                </p>
                <p
                  className="contact-p"
                  style={{ margin: "0", fontWeight: "400" }}
                >
                  Thapar House, 3rd floor,
                </p>
                <p
                  className="contact-p"
                  style={{ margin: "0", fontWeight: "400" }}
                >
                  Janpath Lane,
                </p>
                <p
                  className="contact-p"
                  style={{ margin: "0", fontWeight: "400" }}
                >
                  New Delhi - 110001.
                </p>

                <a href="tel:+************">
                  <span className="contact-p">
                    Phone no:{" "}
                    <span style={{ color: "#3475de" }}>+91-11-23486700</span>
                  </span>
                </a>
              </div>
            </div>
          </div>

          <aside className="lrv-a-grid-item lrv-a-space-children-vertical lrv-a-space-children--2 u-margin-b-2@mobile-max">
            {/* You can add content here for the aside */}
          </aside>
        </div>
      </div>
    </Layout>
  );
};

export default AboutUs;

export async function getStaticProps() {
  const meta = {
    title: "About Us | The Hollywood Reporter India",
    description:
      "Discover The Hollywood Reporter India, your trusted source for the latest in Bollywood, Hollywood, entertainment news, celebrity insights, and industry updates.",
    keywords: [],
    author: "THR INDIA",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
