.popup {
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  width: 100vw;
  height: 100vh;
  z-index: 8;
  transition: all 0.6s ease;
  transform: translateY(-100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2vh;
}
.video-cont {
  min-height: 70vh;

  width: 100%;
}
.video-cont video {
  height: 100%;
  width: fit-content;
}
.video-exts {
  /* height: 20vh; */
  width: 100%;
  /* background-color: rebeccapurple; */
  gap: 15px;
}
.vid-action {
  width: 50px;
  height: 50px;
  background-color: white;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
.vid-action:hover {
  color: red;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px,
    rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
}
.vid-mid {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 35px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  background-color: white;
}
.vid-mid:hover {
  color: red;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
  box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px,
    rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
}
.opdi {
  width: 855px;
  height: 500px;
}
@media screen and (max-width: 900px) {
  .opdi {
    width: 365px;
    height: 205px;
  }
}
