import React from "react";
import Image from "next/image";
import style from "./Category.module.css";
import { dateFormateWithTimeShort } from "@/utils/Util";
import Link from "next/link";
import SponsoredTag from "../common/SponsoredTag";

const LatestSection = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <>
          <div className="row">
            <div className="col-md-12">
              <h2>Latest News</h2>
            </div>
            <div className="col-md-12">
              {data.map((item, i) => {
                return (
                  <Link
                    className="side-card h-par"
                    key={`latest-${i}`}
                    href={item?.slug ?? "#"}
                  >
                    <div className="image h100">
                      <Image
                        className="imgcover"
                        src={
                          item?.croppedImg
                            ? item?.croppedImg
                            : item?.coverImg
                            ? item?.coverImg
                            : ""
                        }
                        alt={item?.altName ?? ""}
                        fill
                      />
                    </div>
                    <div className="content">
                      {item.isPromotional && (
                        <SponsoredTag
                          customStyle={{
                            lineHeight: 1,
                            fontWeight: 700,
                          }}
                        />
                      )}
                      <div className="d-flex flex-columnn align-items-center gap-2 ">
                        <span className="category">
                          {item?.subcategory ?? ""}
                        </span>
                        <span className="timeline">
                          {dateFormateWithTimeShort(item?.timestamp ?? "")}
                        </span>
                      </div>
                      <h3 className="card-title">{item?.title ?? ""}</h3>
                    </div>
                  </Link>
                );
              })}
            </div>
            {/* <div>
              <div
                className="btn btn-outline out-btn"
                onClick={handlerShowMore}
              >
                <div className="middle-btn"></div>
                Show More
              </div>
            </div> */}
          </div>
        </>
      )}
    </>
  );
};

export default LatestSection;
