import React, { useEffect } from "react";

const LeaderboardAd = () => {
  useEffect(() => {
    if (window.googletag && googletag.apiReady) {
      googletag.cmd.push(function () {
        googletag
          .defineSlot(
            "/23172162065/RPSG_THR_Desktop/RPSG_THR_Leaderboard_Desktop",
            [
              [728, 90],
              [970, 250],
            ],
            "div-gpt-ad-1725618343846-0"
          )
          .addService(googletag.pubads());

        googletag.pubads().enableSingleRequest();
        googletag.enableServices();
        googletag.display("div-gpt-ad-1725618343846-0");
      });
    }
  }, []);

  return (
    <>
      <div className="ad-parent-leaderboard">
        <div
          id={"div-gpt-ad-1725618343846-0"}
          className="ad-main"
          style={{ minHeight: "90px" }}
        ></div>
      </div>
    </>
  );
};

export default LeaderboardAd;
