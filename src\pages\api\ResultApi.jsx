import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Author List
export const getResults = async (query, pageNo, resultsLimit, body) => {
  const res = await fetch(Const.Link + `api/tenant/get-results?query=${query}&pageNo=${pageNo}&resultsLimit=${resultsLimit}`, new Headers("POST",body))
  let result = await res.json();
  return result;
};
export const getFilters = async (query, body) => {
  const res = await fetch(Const.Link + `api/tenant/get-filters?query=${query}`, new Headers("POST",body))
  return ProcessAPI(res);
};