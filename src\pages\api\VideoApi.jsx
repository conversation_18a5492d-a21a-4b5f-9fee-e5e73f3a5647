import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Video Data
export const getVideo = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/videos?slug=${slug}`, new Headers("GET"));
  return ProcessAPI(res);
};

// Get THR TV 
export const getVideoMeta = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/video-meta", new Headers("GET"));
  return ProcessAPI(res);
};

// Get THR TV Most Recent
export const getVideoRecent = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/video-recent?slug=${slug}`, new Headers("GET"));
  return ProcessAPI(res);
};

// Get Video Post Sitemap
export const getVideoPostSitemap = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/video-sitemap", new Headers("GET"));
  return ProcessAPI(res);
};
