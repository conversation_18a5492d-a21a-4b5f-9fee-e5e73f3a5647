import Head from "next/head";
import Image from "next/image";
import { Inter } from "next/font/google";
import { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import { RiMenuFill } from "react-icons/ri";
import Link from "next/link";
import { SplitText } from "gsap/dist/SplitText";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { AiOutlinePlus } from "react-icons/ai";
const inter = Inter({ subsets: ["latin"] });
import trivialogo from "../../../public/women/assets/trivialogoimagesvg.svg";
import rpsglogo from "../../../public/women/assets/rpsgrouplogo.webp";
import rpsgmediaimage from "../../../public/women/assets/rpsg-media-logo-dark.png";
import rpsgmediatoplogo from "../../../public/women/assets/rpsgmediaimg.png";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import { Const } from "@/utils/Constants";
gsap.registerPlugin(ScrollTrigger, SplitText);

const imageObjects = [
  {
    url: "/women/assets/Aishwarya.jpg",
    name: "Aishwarya Rai Bachchan - Actor",
    desc: " Aishwarya Rai Bachchan, one of Indian cinema’s most recognised global icons, has seamlessly balanced commercial blockbusters with critically acclaimed films. After winning Miss World in 1994, she swiftly transitioned into acting, delivering memorable performances in <i>Hum Dil De Chuke Sanam</i> (1999), <i>Devdas</i> (2002), <i>Guru</i> (2007), and the <i>Panniyin Selvam</i> films (2022, 2023). Her versatility extends beyond Bollywood, with acclaimed roles in Tamil, Bengali and international films like <i>Bride &Prejudice</i> (2004) and <i>The Pink Panther 2</i> (2009). Known for her ethereal beauty and graceful screen presence, Rai Bachchan has portrayed a wide range of characters — from a historical queen in <i>Jodhaa Akbar</i> (2008) to a contemporary, independent woman in <i>Ae Dil Hai Mushkil</i> (2016). Her enduring influence extends beyond cinema, making her a regular at the Cannes film festival, a global ambassador for major brands, and a celebrated cultural figure. Decades into her career, she remains one of India’s most revered actor, who has continuously enraptured audiences with her elegance and talent.",
    trivia:
      "In 2003, Aishwarya Rai Bachchan became the first Indian actor to serve on the jury at the Cannes Film Festival.",
  },
  {
    url: "/women/assets/Alia Bhat.jpg",
    name: "Alia Bhatt - Actor and Producer",
    desc: "Thirteen years after her debut in Karan Johar’s <i>Student of the Year,</i> Alia Bhatt isn’t just an actor anymore. She now juggles the roles of a producer with her venture Eternal Sunshine Productions — founded in 2019 to make “happy films” — along with being an entrepreneur; she launched a sustainable clothing brand for children and maternity wear called Ed-a-Mamma in 2020. A bona fide icon especially for young women, Bhatt has accomplished professional milestones by not only garnering widespread acclaim for her performances in films like <i>Highway</i> (2014), <i>Udta Punjab</i> (2016), <i>Gully Boy</i> (2019), and <i>Gangubai Kathiawadi</i> (2022), but also by making her Hollywood debut alongside Gal Gadot in <i>Heart of Stone</i> in 2023, in what one could argue is a major leap at a fairly early stage in a career. In 2023, Bhatt won the National Award for her performance in <i>Gangubai Kathiawadi,</i> which further cemented her status as one of the most coveted actors in India, portraying fierce women leads from different walks of life.",
    trivia:
      "At the age of nine, Alia Bhatt auditioned for a role in Sanjay Leela Bhansali’s <i>Black</i> (2005), which eventually went to German-Indian actor Ayesha Kapur.",
  },
  {
    url: "/women/assets/Anita Shroff.jpg",
    name: "Anaita Shroff Adajania - Costume Designer and Stylist",
    desc: "Bollywood’s fairy godmother, Anaita Shroff Adajania is the costume designer behind the coolest celebrity looks in recent years. From styling Alia Bhatt for the 2024 Met Gala to Isha Ambani Piramal in Schiaparelli’s first-ever custom couture sari, she’s also turned costume designer for films like 𝘋𝘩𝘰𝘰𝘮 <i>2</i> (2006), <i>Cocktail</i> (2012), <i>Dear Zindagi</i> (2016), <i>Gehraiyaan</i> (2022), <i>Mr. & Mrs. Mahi</i> (2024), and shows like <i>Call Me Bae</i> (2024) — many of which set the trend for an entire generation. A political science major in college Adajania spent her early years in and around her grandfather’s bespoke tailoring shop and worked with Maybelline after graduation. She has also been fashion director at publications like <i>Vogue</i> and fashion editor at <i>Elle</i> and <i>L’Officiel</i>. While the world of fashion styling is far more established today, with many players in the field, Adajania’s certainly one of the OGs who paved the way.",
    trivia:
      "Besides her work behind the scenes, Anaita Shroff Adajania has appeared on the screen as well — as Kajol’s friend in <i>Dilwale Dulhania Le Jayenge</i> (1995), a salon customer in <i>Everybody Says I'm Fine!</i> (2001) and a student in <i>Kal Ho Naa Ho</i> (2003).",
  },
  {
    url: "/women/assets/Ananya P.jpg",
    name: "Ananya Panday - Actor",
    desc: "The face of Gen Z in Bollywood today, Ananya Panday debuted with Punit Malhotra’s <i>Student of the Year 2</i> back in 2019 but was only taken seriously after a stellar performance in <i>Kho Gaye Hum Kahan</i> (2023). With <i>Call Me Bae</i> and <i>CTRL</i> making waves, 2024 has been a turning point, proving her mettle beyond the “nepo kid” tag. Daughter to actors Chunky Panday and Bhavana Pandey, the actor is also the face of several national and international brands, including Jimmy Choo, Swarovski, Lakme and Beats. She’s a regular at fashion weeks and the founder of So Positive — a unique initiative about the importance of kindness and positivity on social media. The young star is quite the fashionista, inspired by the timeless Rekha, and is particularly a big fan of jewellery. In fact, the first piece she bought with her own money was a necklace from Hanut Singh. With a global presence and a growing fanbase, she’s the next big thing in Indian cinema.",
    trivia:
      "Ananya Panday debuted at Le Bal des Débutantes gala in Paris in 2017, two years before her debut in films. The actor also recently bought her own house in the same building her parents live in, one that was designed by Gauri Khan.",
  },
  {
    url: "/women/assets/Aneesha Baig.jpg",
    name: "Aneesha Baig - Head of Content (non-fiction), Dharmatic Entertainment",
    desc: "Aneesha Baig, head of content (non-fiction) at Dharmatic Entertainment, is known for creating compelling storylines from real-life, unscripted events. With a background in journalism at NDTV, Baig’s passion for production shines through the hit shows, <i>The Fabulous Lives of Bollywood Wives</i> and <i>The Tribe</i>. She expanded Dharmatic’s non-fiction slate with <i>Searching for Sheela</i> (2021), a gripping documentary about Ma Anand Sheela, Osho’s notorious aide. She has often admitted to enjoying production more than anchoring, making her a perfect fit for the role. In an interview with <i>Firstpost,</i> she said, “It was always shows over stories, and features over reports for me. And so, this [role at Dharmatic Entertainment] felt like just this amazing opportunity to try all the things and attempt all the ideas I ever had.” Baig particularly embraces the creative freedom of OTT platforms, blending the signature Dharma glamour with bold, fresh narratives, positioning her as a dynamic force in India’s evolving non-fiction landscape.",
    trivia:
      "One night, several years ago, Aneesha Baig bumped into her then bosses, (founders and executive co-chairpersons of NDTV) Prannoy and Radhika Roy. They suggested, since she enjoyed partying so much, she should take a camera along. That led to the birth of <i>Night Out,</i> an NDTV exclusive that launched her career of interviewing celebrities.",
  },
  {
    url: "/women/assets/Aparna Purohit.jpg",
    name: "Aparna Purohit - CEO, Aamir Khan Productions",
    desc: "Aparna Purohit’s storied career has seen her emerge as one of the leading entertainment executives in the country. From starting her own production venture Chaar Yaar Productions in 2008 to becoming a consultant at the National Film Development Corporation, she has donned various hats. Prior to her current role as CEO of Aamir Khan Productions, she spent eight years at Amazon Prime Video, rising to become the head of originals for India in 2019, adding Southeast Asia to her plate in 2022. Her time at Amazon has seen the launch of critically acclaimed shows like <i>Mirzapur,</i> <i>The Family Man,</i> <i>Made in Heaven</i> and <i>Paatal Lok,</i> which have transformed the OTT landscape and made the streaming platform a household name in the country. Purohit has been vocal about her passion to bring more diversity in the entertainment space and has championed the inclusion of more women and platforming the stories of women.",
    trivia:
      "Aparna Purohit has spent time as an assistant director to notable names like Aparna Sen and Naseeruddin Shah.",
  },
  {
    url: "/women/assets/Archana Kalpathi.jpg",
    name: "Archana Kalpathi - CEO, AGS Entertainment Network",
    desc: "The CEO of the AGS Entertainment Network, Archana Kalpathi is one of the more prominent women in film production and distribution in South India. Helming her family’s movie business, she has brought blockbusters like <i>Thani Oruvan</i> (2015), <i>Bigil</i> (2019) and <i>Love Today</i> (2022) to the big screens. Having ventured into the field at the age of 23, Kalpathi has seen the business grow leaps and bounds since with film production, distribution and the operation of four theatres across locations in Chennai. Kalpathi’s tenure has seen AGS work with some of the industry’s leading stars, including Vijay, Dhanush, Nayanthara and Ravi Mohan. Her bet on newcomer Pradeep Ranganathan led to the production of two of Tamil cinema’s biggest hits in recent times: <i>Love Today,</i> which has since been remade in Hindi as <i>Loveyapa</i>. and the 2025 college drama <i>Dragon</i>.",
    trivia:
      "Archana Kalpathi is a software engineer by training and holds a BE and an MSc in computer science.",
  },
  {
    url: "/women/assets/Deepika Padukone.jpg",
    name: "Deepika Padukone - Actor and Producer",
    desc: "It’s not often you can say that one of Hindi cinema’s biggest and most profitable stars is also one of Indian cinema’s finest performers. But Deepika Padukone is that sort of rare commerce-weds-art icon. Following a famous Bollywood debut opposite Shah Rukh Khan in <i>Om Shanti Om</i> (2008), Padukone fast established her reputation as a modern-day heroine, becoming an all-out force of nature in landscape-altering hits by Sanjay Leela Bhansali, Shoojit Sircar, Imtiaz Ali, Siddharth Anand, Ayan Mukerji, Rohit Shetty and Shakun Batra. More importantly, her identity has extended beyond language — with roles in Kannada, Tamil, Telugu and Hollywood blockbusters — and beyond the screen as well, as an outspoken advocate for mental health.",
    trivia:
      "Deepika Padukone became the ninth Indian to serve on a Cannes Film Festival jury in 2022 — and only the fifth Indian actor to do so.",
  },
  {
    url: "/women/assets/Ekta Kapoor.jpg",
    name: "Ektaa Kapoor - Joint Managing Director, Balaji Telefilms",
    desc: "Imagine men, women and kids finishing their chores and assembling to sit in front of the TV to watch...daily soaps. That’s the power of Ektaa Kapoor, one of the most influential producers who revolutionised the small screen in the 2000s. Through Balaji Telefilms, Kapoor was the pioneer of the desi K drama, as several titles of her shows began with the same letter — <i>Kyunki Saas Bhi Kabhi Bahu Thi,</i> <i>Kahaani Ghar Ghar Kii</i> and <i>Kasautii Zindagii Kay</i> among others. After her first success with the sitcom <i>Hum Paanch</i> in 1995, Kapoor changed the grammar of TV in the decade that followed with her productions that spoke about the traditional Indian family setup. The Kapoor-backed daily soaps set the template for Indian TV, as her shows continue to dominate TRP lists. Kapoor also transitioned to producing movies, backing films like <i>Love Sex Aur Dhoka</i> (2010), <i>The Dirty Picture</i> (2011) and last year’s hit, <i>Crew</i>.",
    trivia:
      "Ektaa Kapoor started her career at age of 17 and found her first success with the 1995 TV show 𝘏𝘶𝘮 𝘗𝘢𝘢𝘯𝘤𝘩, which marked the acting debut of Vidya Balan.",
  },
  {
    url: "/women/assets/Geetu Mohandas.jpg",
    name: "Geetu Mohandas - Director",
    desc: "“Rocking Star” Yash, fresh off the success of the two monstrously successful <i>KGF</i> films, could virtually have worked with any Indian film director for his follow up. Yet it’s a testament to Geetu Mohandas’s craft as a director and writer that he chose to put the full weight of his stardom behind <i>Toxic,</i> one of the biggest Indian films being made today. What has added to the period gangster film’s curiosity is the tagline, “A Fairy Tale for Grown-ups”, suggesting that it could be far more than the hyper-masculine brand of cinema that such big-budget star vehicles inevitably end up becoming. She already subverted such notions with her previous film <i>Mothoon</i> (2019), starring Nivin Pauly, for the way it dealt with the topic of homosexuality on the islands of Lakshadweep with utmost sincerity and sensitivity. <i>Toxic</i> is also reportedly being simultaneously shot in English and Kannada, suggesting that it could have an ambitious international release that appeals to a mix of both Indian and global audiences.",
    trivia:
      "Long before Geetu Mohandas debuted as a director with <i>Liar’s Dice</i> (2013) and even before she became a lead actors with <i>Life Is Beautiful</i> (2000), she carried an entire film on her shoulders at the age of five. That movie was 1986’s <i>Onnu Muthal Poojyam Vare,</i> in which her co-star was Mohanlal.",
  },
  {
    url: "/women/assets/Guneet Monga Kapoor.jpg",
    name: "Guneet Monga Kapoor - CEO and Founder, Sikhya Entertainment",
    desc: "The trailblazing producer is on a glorious global journey of putting Indian films on the map. A cofounder of Sikhya Entertainment,  Monga Kapoor has put her might behind some of the most landmark films of the last decade, including <i>Gangs of Wasseypur</i> (2012), <i>The Lunchbox</i> (2013), <i>Masaan</i> (2015) and <i>Pagglait</i> (2021).  Monga Kapoor was also one of the executive producers behind the documentary short film <i>Period. End of Sentence.,</i> which won an Academy Award in 2019. Four years later, she became the first producer from India to win an Academy Award for best documentary short, for <i>The Elephant Whisperers.</i> In 2023, she co-produced the action film <i>Kill,</i> which garnered critical acclaim, achieved box office success, and prompted Lionsgate and 87Eleven — which had previously collaborated on the <i>John Wick</i> franchise — to announce an English remake.",
    trivia:
      "At the age of 21, Guneet  Monga Kapoor borrowed ₹50 lakh from her neighbour to co-produce the 2007 cricket drama <i>Say Salaam India,</i> which was the directorial debut of Subhash Kapoor, who went on to make films like <i>Phas Gaye Re Obama</i> (2010) and <i>Jolly LLB</i> (2013).",
  },
  {
    url: "/women/assets/Ishita Moitra.jpg",
    name: "Ishita Moitra - Writer and Showrunner",
    desc: "Before becoming a writer, Ishita Moitra, did her journalism honours from Delhi University, interning at publications like <i>India Today,</i> <i>Outlook,</i> and     <i>The Asian Age</i>. After completing her studies in mass communications from Jamia MCRC, she decided to move to Mumbai to write. Moitra is a writer known for her work on movies like <i>Half Girlfriend</i> (2017), <i>Unpaused</i> (2020), <i>Shakuntala Devi</i> (2020) and <i>Rocky Aur Rani Kii Prem Kahaani</i> (2023), as well as series like <i>Four More Shots Please</i> (2019) and <i>Call Me Bae</i> (2024), among many others. Her protagonists are often women who find ways of navigating the world’s pressures without giving into cynicism. Hope holds them together. She also uses humour to bring out issues and topics that are otherwise not palatable, as she believes that “comedy is fertile ground for social commentary”.",
    trivia:
      "A thorough Karan Johar fan, after she watched <i>Kuch Kuch Hota Hai</i> in 1998, Ishita Moitra started wearing the red headband that Kajol's character wears to school.",
  },
  {
    url: "/women/assets/Jyoti Deshpande.jpg",
    name: "Jyoti Deshpande - President, Jio Studios",
    thirdline: " (Media & Content Business, RIL)",
    desc: "Jyoti Deshpande produced last year’s much-loved entry to the Oscars, <i>Laapataa Ladies,</i> directed by Kiran Rao, and is currently the president of media and content business at Reliance Industries. She has over three decades of experience in the media and entertainment business and she doesn’t look at her role at promoting female content as a mere responsibility; as studio head, she prefers the term “ownership” and is known to actively seek out “stories with a purpose”, with subjects that address women empowerment, sustainability and more. The result is there for all to see; apart from the international success of <i>Laapataa Ladies,</i> she also backed <i>Stree 2,</i> a box office storm that earned ₹871 crore worldwide, that too without an A-list superstar or a hyper-masculine narrative. She was previously the CEO of Viacom18, the first and only woman CEO of a ‘Big 4’ media company.",
    trivia:
      "As a producer, Jyoti Deshpande has been a part of creating content in eight languages — including Bengali and Gujarati.",
  },
  {
    url: "/women/assets/Jyothika.jpg",
    name: "Jyotika - Actor and Producer",
    desc: "Jyotika’s prolific career in four languages has made her a household name across the country. She found her big break in Tamil cinema and quickly established herself as a leading actor in the early 2000s. With iconic films like <i>Kushi</i> (2000), <i>Kaakha Kaakha</i> (2003) and <i>Chandramukhi</i> (2005) as part of her repertoire, she has cemented her status as one of Tamil cinema’s biggest stars. Jyotika’s long career has seen her straddle a filmography that features some of the biggest commercial blockbusters while also making space for roles that allowed her to showcase her acting prowess as witnessed in her portrayal of the hearing- and speech-impaired Archana in <i>Mozhi</i> (2007). After her marriage to Tamil star Suriya, she took a nearly decade-long break from acting. She made her comeback in 2015 and has since starred in Tamil, Telugu, Malayalam and Hindi movies, delivering notable performances in films such as <i>Kaatrin Mozhi</i> (2018) and <i>Kaathal – The Core</i> (2023). She has also entered the realm of film production with 2D Entertainment, a production house started with her husband, producing a range of hits, including <i>Soorarai Pottru</i> (2020), <i>Jai Bhim</i> (2021) and <i>Meiyazhagan</i> (2024).",
    trivia:
      "While narrowly missing out on a National Award for acting for her role in <i>Mozhi,</i> Jyotika won acclaim as a producer for <i>Soorarai Pottru</i> in 2020.",
  },
  {
    url: "/women/assets/Kangana Ranaut.jpg",
    name: "Kangana Ranaut - Actor, Producer and Director",
    desc: "Kangana Ranaut is a prominent Indian actor, filmmaker and producer, renowned for portraying strong-willed, unconventional women in Hindi cinema. She made her acting debut in 2006 with <i>Gangster</i> and has since delivered critically acclaimed performances in films like <i>Fashion</i> (2008), <i>Tanu Weds Manu</i> (2011) and <i>Queen</i> (2013). Her work has earned her several accolades, including four National Film Awards. In addition to her acting career, Ranaut ventured into filmmaking with the historical drama <i>Manikarnika: The Queen of Jhansi</i> in 2019. In June 2024, Ranaut transitioned into politics and now serves as a Member of Parliament in the Lok Sabha for the Mandi constituency. Known for her outspoken nature and fearless choices, she has consistently pushed boundaries, both on-screen and off. Whether in cinema or politics, Ranaut remains a formidable presence, shaping conversations and challenging conventions in the Indian entertainment and political landscape.",
    trivia:
      "Kangana Ranaut’s parents wanted her to become a doctor. Coming from a family of academicians, she was preparing for medical entrance exams, but after failing her chemistry exam in school, she decided to move to Delhi and pursue theatre.",
  },
  {
    url: "/women/assets/Kanika Dhillon.jpg",
    name: "Kanika Dhillon - Writer and Producer",
    desc: "Screenwriter Kanika Dhillon got acclaim in 2018 with <i>Manmarziyaan</i> — for which she was also the creative producer — and immediately followed it up with films like <i>Kedarnath</i> (2018), <i>Judgementall Hai Kya</i> (2019), <i>Haseen Dillruba</i> (2021), <i>Rashmi Rocket</i> (2021), and <i>Dunki</i> (2023). Before this, she was one of the writers on <i>Ra.One</i> (2011); it was at Red Chillies Entertainment that she began her career, as an intern and then as an assistant director. She is also the author of the bestselling novel <i>Bombay Duck is a Fish,</i> published by Tranquebar Press and Westland. She has written three books. Exhausted by the bubbly girl caricature that Hindi cinema usually puts forward, she chooses to make her female protagonists questioning and nonconformist, full of agency and a joyful recklessness. In 2023, she founded her own production house, Kathha Pictures, with films like <i>Phir Aayi Hasseen Dillruba</i> (2024) and <i>Do Patti</i> (2024).",
    trivia:
      "Kanika Dhillon became the first writer to get top billing on a Netflix India release with <i>Haseen Dillruba,</i> something most production houses still do not do.",
  },

  {
    url: "/women/assets/Kareena2.jpg",
    name: "Kareena Kapoor Khan - Actor and Producer",
    desc: "Beyond the mathematics of hits and flops, Kareena Kapoor Khan has always been a star. Despite being one of the top-billed actors in the country who has mostly acted in commercially mainstream films and gifted her fans cultural icons like Poo from <i>Kabhi Khushi Kabhie Gham...</i> (2001) and Geet from <i>Jab We Met</i> (2007), Kapoor Khan has also built her credibility with scripts and performances that were considered unconventional, even “bold”, especially in her early years in the industry. From playing a sex worker in <i>Chameli</i> (2003), a young girl caught in communal crossfires in <i>Dev</i> (2004), to the short but memorable role as Dolly (or Desdemona) in <i>Omkara</i> (2006) — Vishal Bhardwaj’s adaptation of <i>Othello</i> — Kapoor Khan has never shied away from taking a risk or two. The actor — who now also co-owns Korean skincare brand Quench Botanics with Vineeta Singh, CEO of Sugar Cosmetics — has been an advocate for body positivity. She has publicly spoken about not adhering to societal pressures to be a certain size, especially after being in the public eye during and after her pregnancy. Through her talk show <i>What Women Want</i> (YouTube) Kapoor Khan foregrounds the issues and experiences of women working in the largely male-dominated entertainment industry.",
    trivia:
      "The actress has co-authored two books based on her life experiences: <i>The Style Diary of a Bollywood Diva</i> (2012) with Rochelle Pinto, and <i>Kareena Kapoor Khan's Pregnancy Bible: The Ultimate Manual for Moms-To-Be</i> (2021), with Aditi Shah Bhimjyani.",
  },
  {
    url: "/women/assets/katrina.jpg",
    name: "Katrina Kaif - Actor",
    desc: "Katrina Kaif emerged as one of the biggest stars of Hindi cinema, overcoming the language barrier, even though when she started in the early 2000s, her films didn’t exactly set the box office on fire. But Kaif’s stardom was meant to be, as she soon exploded on the big screen with consecutive hit films, cementing herself as an undisputed force. Her filmography has been a mix of tent-pole blockbusters like <i>Ek Tha Tiger</i> (2012), <i>Dhoom 3</i> (2013), and <i>Sooryavanshi</i> (2021), and much-loved dramas like <i>New York</i> (2009), <i>Bharat</i> (2019), and <i>Merry Christmas</i> (2024), which got her critical acclaim, including the 2018 Shah Rukh Khan–starrer <i>Zero,</i>, in which Kaif was a standout performer. The actor, who has worked with major A-listers in her career, went beyond the image of a Bollywood star and became an entrepreneur in 2019, when she launched a successful cosmetic line, Kay Beauty.",
    trivia:
      "Katrina Kaif was the first Bollywood actor to have a Barbie doll modelled after her.",
  },
  {
    url: "/women/assets/Kavya Maran Sun Network.jpg",
    name: "Kaviya Kalanithi Maran - Executive Director, Sun TV Network",
    desc: "Imagine every single day being at the top of two of India’s biggest addictions — cricket and cinema.<strong> That’s what life looks like for billionaire Kaviya Maran, CEO of Sunrisers Hyderabad, who is also in charge of operations at Sun Network, Sun Music and a slew of FM channels.</strong> In 2025, not only does she have the IPL to look forward to but also one of the year’s biggest films, <i>Coolie,</i> starring Rajinikanth and directed by Lokesh Kanagaraj. Her popularity and influence among sports and cinema fans are such that Superstar Rajinikanth famously joked about how he feels bad seeing Maran’s expressions each time her IPL team underperforms. The amusement must only have tripled since because Maran is now also the owner of Northern Superchargers in The Hundred league, organised by the England and Wales Cricket Board, apart from running Sunrisers Eastern Cape, a cricket team in the SA20 league in South Africa.",
    trivia:
      "Kaviya Maran’s company, Sun TV Network, is among the country’s biggest, with 32 TV channels and 45 FM stations.",
  },
  {
    url: "/women/assets/Kiara Advani.jpg",
    name: "Kiara Advani - Actor",
    desc: "One of India’s most sought-after actors, Kiara Advani has worked in both Hindi and Telugu cinema since her debut in Kabir Sadanand’s <i>Fugly</i> (2014). However, it was her role as Sakshi in <i>M.S. Dhoni: The Untold Story</i> (2016) two years later that truly put her on the map, followed by <i>Kabir Singh</i> (2019), which cemented her position as one of the leading actors of her generation. She has since worked on <i>Shershaah</i> (2021), with her now husband, actor Sidharth Malhotra; <i>Bhool Bhulaiyaa 2</i> (2022); and <i>Satyaprem Ki Katha</i> (2023). In 2025, the actor appears alongside Ram Charan in <i>Game Changer</i> and Hrithik Roshan in <i>War 2,</i> and is also filming <i>Don 3</i> with Ranveer Singh. Beyond acting, she’s the ambassador for many fashion and beauty brands like Tira and Pond’s, and FMCG brands like Slice.",
    trivia:
      "Kiara Advani’s real name is Alia Advani. She decided to adopt a professional moniker, before the release of her debut film, to avoid confusion with Alia Bhatt, who was already an established star at that point.",
  },
  {
    url: "/women/assets/KIran Rao.jpg",
    name: "Kiran Rao - Director and Producer",
    desc: "Kiran Rao, an Indian filmmaker with a distinct voice and a penchant for women-centric narratives has certainly made her mark as a producer, director and screenwriter. Renowned for her work in critically acclaimed films like <i>Swades</i> (2004), <i>Taare Zameen Par</i> (2007), <i>Dhobi Ghat</i> (2010), <i>Delhi Belly</i> (2011), <i>Dangal</i> (2016), <i>Secret Superstar</i> (2017) and <i>Laal Singh Chaddha</i> (2022) to name a few, Rao’s filmography reflects her sensitivity. Her most recent project, <i>Laapataa Ladies</i> (2023) was chosen to be India’s official entry for the 2025 Oscars, following in the footsteps of <i>Lagaan</i> (2001) and <i>Peepli Live</i> (2010) — both of which she was also associated with. Besides her cinematic achievements, she co-produced the television series <i>Satyamev Jayate</i> (2012) with ex-husband actor-producer Aamir Khan. The filmmaker also co-founded the Paani Foundation, which is a non-profit, non-governmental organisation that works towards drought prevention and watershed management in Maharashtra.",
    trivia:
      "Kiran Rao hails from royalty: Her paternal grandfather, J. Rameshwar Rao, was the former Raja of Wanaparthy, a district in modern-day Telangana.",
  },
  {
    url: "/women/assets/Kriti Sanon.jpg",
    name: "Kriti Sanon - Actor and Producer",
    desc: "An engineer turned star — that’s actor Kriti Sanon, who leapt from being a middle-class Delhi girl to a Bollywood diva within a decade. Starting with a Telugu action film starring Mahesh Babu in 2014, <i>1: Nenokkadine,</i> Sanon switched to Bollywood the same year, debuting alongside Tiger Shroff in <i>Heropanti</i>. The start was rocky, as her next two outings — the Shah Rukh Khan–led <i>Dilwale</i> in 2015 and producer Dinesh Vijan’s directorial debut <i>Raabta</i> in 2017 — didn’t add much to her career. But the actor found her groove with the game-changing <i>Bareilly Ki Barfi</i> (2017), which set her on an assured path, leading to a National Award for playing a surrogate mother in <i>Mimi</i> (2021). Just last year, Sanon had a sensational streak of two hit films (<i>Crew, Teri Baaton Mein Aisa Uljha Jiya</i>) and also turned a producer with the Netflix thriller <i>Do Patti</i>. She is currently filming <i>Tere Ishk Mein,</i> an intense romantic drama co-starring Dhanush.",
    trivia:
      "Kriti Sanon’s ventures go beyond film production, as the actor launched a skincare brand, Hyphen, and is also the cofounder of The Tribe, a fitness startup.",
  },
  {
    url: "/women/assets/Leena Gangopadhyay.jpg",
    name: "Leena Gangopadhyay - Writer, Producer and Director",
    desc: "Writer-producer Leena Gangopadhyay’s career is a glowing example of how artistic vision when married with social commitment can help in bridging the gap between popular culture and public service. For over two decades, she has been a force to reckon with in the Bengali film and television industry, as the writer of some of the most wildly popular serials like <i>Ishti Kutum, Kusum Dola, Andarmahal</i> and <i>Icche Putul,</i> to name a few. Gangopadhyay insists on prioritising “relatable storytelling” over labelling entertainment as “mass” or “class”, with the greatest evidence of her success being several of her Bengali originals being remade in Hindi and South Indian languages. At the top of this roster, of course, stands <i>Sreemoyee,</i> whose Hindi remake <i>Anupamaa</i> is currently in its fifth year. Gangopadhyay’s ethos of staying true to her roots follows her into her life in public service as well. As the chairperson of the West Bengal Commission for Women, Gangopadhyay has an ear to the ground, engaging with women across various strata of society, blending her real and reel lives in more ways than one. For Gangopadhyay, life and art mimic each other far too often, in ways that have only allowed her impact to transcend West Bengal’s borders and resonate across cultures.",
    trivia:
      "<i>Bhalo Theko</i> (2003) — Vidya Balan’s debut film — was based on <i>Janmadin,</i> a novel written by Leena Gangopadhyay.",
  },
  {
    url: "/women/assets/Monika Shergill (Netflix).jpg",
    name: "Monika Shergill - Vice President of Content, Netflix India",
    desc: "Monika Shergill is the current vice president of content at Netflix India. She started off as a journalist and documentary filmmaker and has since worked with big names in the entertainment landscape such as Sony, Star India and Viacom18 in various roles. At Netflix, Shergill leads the development, creation and acquisition of Indian content. She has helped diversify the content to create a slate that balances originals and partnerships with leading production houses like Yash Raj Films and Excel Entertainment. Some fan favourites to feature on Netflix in her time include <i>Guns and Gulaabs</i> (2023), <i>The Railway Men</i> (2023), <i>Amar Singh Chamkila</i> (2024), <i>Heeramandi</i> (2024), and even reality shows like <i>The Fabulous Lives of Bollywood Wives.</i>. With the WWE finding a home in Netflix India with Hindi commentary, viewership is set to grow in 2025. Awards and critical acclaim have also followed, with the docuseries <i>The Elephant Whisperers</i> distributed by Netflix winning an Oscar, along with crime-drama series 𝘋𝘦𝘭𝘩𝘪 𝘊𝘳𝘪𝘮𝘦 and Vir Das’s comedy special <i>Vir Das: Landing</i> claiming International Emmy Awards.",
    trivia:
      "Early on in her career, Monika Shergill was a correspondent for the Whitley Award–winning environment series, <i>Living on the Edge</i>.",
  },
  {
    url: "/women/assets/Mrinalini Jain.jpg",
    name: "Mrinalini Jain - Group Chief Development Officer, Banijay Asia and EndemolShine India",
    desc: "Mrinalini Jain has become one of the youngest names to rise to the top of India’s content development space. Jain’s journey has been an exceptional one, from starting out as a creative producer to, currently, the group chief development officer at production companies Banijay Asia and EndemolShine India. Which means that she has been at the forefront of shaping the modern Hindi streaming landscape across both fiction and non-fiction formats. This includes partnership deals with BBC Studios and CBS — from <i>The Trial,</i> an adaptation of <i>The Good Wife,</i> to the <i>Night Manager</i> remake, as well as an upcoming <i>Monk</i> remake — a new collaboration with Nadiadwala Grandson Entertainment, and hit reality shows like <i>Khatron Ke Khiladi</i> and <i>MTV Roadies</i>.",
    trivia:
      "Mrinalini Jain started her career as an assistant costume designer and assistant director for Bollywood movies like <i>Hasee Toh Phasee</i> (2014) and Salman Khan– starrer <i>Kick</i> (2014).",
  },
  {
    url: "/women/assets/Nayantara.jpg",
    name: "Nayanthara - Actor and Producer",
    desc: "Nayanthara has redefined stardom for female actors in the industry. With a career spanning nearly two decades, she has anchored women-led films like <i>Maya</i> (2015), <i>Aramm</i> (2017) and <i>Kolamavu Kokila</i> (2018) while also headlining blockbusters like <i>Raja Rani</i> (2013),  <i>Bigil</i> (2019) and  <i>Jawan</i> (2023). Beyond acting, she has ventured into production, cofounding Rowdy Pictures, which has backed offbeat projects like  <i>Kaathuvaakula Rendu Kaadhal</i> (2022) and <i>Connect</i> (2022). Known for fiercely guarding her privacy, Nayanthara rarely engages in public discourse, letting her performances speak for themselves. Her ability to balance mass appeal with meaningful roles has made her one of Indian cinema’s most influential stars. Whether playing a supernatural heroine, a determined activist, or a charismatic romantic lead, she continues to push the boundaries of female representation in mainstream cinema, proving that stardom can be both powerful and self-defined.",
    trivia:
      "Nayanthara, along with her husband Vignesh Shivan, produced the 2021 Tamil drama <i>Koozhangal</i> (Pebbles) directed by P.S. Vinothraj that won the Tiger Award, the top prize at the International Film Festival Rotterdam.",
  },
  {
    url: "/women/assets/Nazriya Stills 2.jpg",
    name: "Nazriya Nazim Fahadh - Actor and Co-owner, Fahadh Faasil and Friends",
    desc: "A testament to the growing legacy of Nazriya Nazim Fahadh — once dubbed “Kerala’s sweetheart” due to her penchant for cutesy, girl-next-door but deceptively spunky roles — is that Fahadh Faasil can just as readily be identified as “Nazriya Nazim’s husband”. The actor has been paired opposite male stars ranging from Dhanush, Arya and Nivin Pauly to Dulquer Salmaan, Nani and Prithviraj Sukumaran, but the heft of the quintessential Nazriya role suggests that they’re the ones paired with her. Her striking screen presence has acquired a cult status of sorts, one that subverts the male gaze rather than supplying it. After breaking out with <i>Ohm Shanthi Oshaana</i> and <i>Bangalore Days</i> in 2014, her post-hiatus phase has continued this distinct impression with romcoms like <i>Ante Sundaraniki</i> (2022) and last year’s hit thriller, <i>Sookshmadarshini</i>. Her producing career only widens her imprint; she co-owns Fahadh Faasil and Friends, the company behind modern classics like <i>Kumbalangi Nights</i> (2019), <i>C U Soon</i> (2020) and <i>Aavesham</i> (2024).",
    trivia:
      "Nazriya Nizam Fahadh began her acting career as a child artiste in movies starring Mammootty and Mohanlal.",
  },
  {
    url: "/women/assets/Nita Mukesh Ambani.jpg",
    name: "Nita Ambani - Chairperson, JioStar",
    desc: "No power list is ever complete without Nita Ambani. As a businesswoman and philanthropist, she has established herself as one of India’s most influential women business leaders. A founder and chairperson of the Reliance Foundation, the corporate social responsibility arm of Reliance Industries, Ambani has successfully led the non-profit organisation to glory. Under her leadership, the Reliance Foundation was also awarded the prestigious Rashtriya Khel Protsahan Puruskar in 2017 by the President of India for its initiatives in promoting grassroots sport. Though the mention of the Ambani family paints a picture of wealth and luxury, Nita Ambani is known for much more as she has forged her own path. She is an avid art collector as well as the owner of the Indian Premier League cricket team Mumbai Indians. Born to a middle-class Gujarati family and now known globally for her work, Ambani’s ascent to the top is one for the books.",
    trivia:
      "A year after getting married to Mukesh Ambani in 1985, Nita Ambani was working as a school teacher at Sunflower Nursery on a monthly salary of ₹800.",
  },
  {
    url: "/women/assets/Nithya.jpg",
    name: "Nithya Menen - Actor and Producer",
    desc: "Last year’s National Award for best actress went to Nithya Menen for playing the delightful Shobana in <i>Thiruchitrambalam</i> (2022), breaking the decades- long belief that the award must only go to people for portraying intense sadness or pain. She remains a sought-after actor in all four South Indian languages, and she got the top billing in this year’s mature romantic comedy <i>Kadhalikka Neramillai</i> in Tamil. Apart from timeless romances and comedies, Menen even produced a wacky sci-fi comedy titled <i>Skylab</i> in 2021. Coming up this year are two Tamil films, one being directed by Dhanush titled <i>Idly Kadai</i> and an as yet untitled film with Vijay Sethupathi, her co-star in 2019’s <i>19(1)(𝘢)</i>. With a filmography that’s filled with experiments, she was also among the first A-list actors to fully embrace movies and shows that were being specifically made for OTT platforms. Another factor that makes her stand out is that she is among a handful of actors who dub for themselves in as many as five languages.",
    trivia:
      "Apart from being an actor, Nithya Menen has also sung over 20 film songs across five languages.",
  },
  {
    url: "/women/assets/Parvathy Thiruvothu.jpg",
    name: "Parvathy Thiruvothu - Actor",
    desc: "Parvathy Thiruvothu has been among Malayalam cinema’s top performers for over 20 years, but her resilience at the top is not because her films come attached to male superstars. She remains the primary draw of her films to this day, and this has continued even with last year’s intense drama <i>Ullozhukku,</i> co-starring Urvashi. As one of the leaders of the Women in Cinema Collective, her influence has pushed the Malayalam film industry towards tough conversations, while bringing about changes that make the industry safer for women. The most important among these include the fight that led to the Kerala state government instituting the Hema Committee to investigate issues related to sexual violence and gender inequality in the Malayalam cinema industry. She continues to fight the good fight, even if it comes with consequences and continuously being denied work in the Malayalam movie industry, a punishment for her activism.",
    trivia:
      "Parvathy Thiruvothu has been a part of five anthologies, having done two each in Malayalam and Tamil and one in Telugu.",
  },
  {
    url: "/women/assets/Payal Kapadia.jpg",
    name: "Payal Kapadia - Writer and Director",
    desc: "Payal Kapadia is the writer and director of <i>All We Imagine as Light,</i> a fiction feature debut that won the Grand Prix at the 77th Cannes Film Festival in 2024, and went on to have a glorious run on the film festival and award circuits. It also earned her a nomination for the Golden Globe award for best director. Before this, she made <i>A Night of Knowing Nothing</i> (2021), her feature film debut about student life and the protests they waged against the institutions that pushed back violently. This film blurred genres, mixing footage of violence on students, and protest gatherings with fictional love letters; she called it a “hybrid documentary”. The film won the Golden Eye award for best documentary film at the 74th Cannes Film Festival. She is inspired by filmmakers like Miguel Gomes and Chris Marker whose movies exist on the edge of genres.",
    trivia:
      "An alumnus of the Film and Television Institute of India (FTII), Payal Kapadia's first feature film 𝘈 𝘕𝘪𝘨𝘩𝘵 𝘰𝘧 𝘒𝘯𝘰𝘸𝘪𝘯𝘨 𝘕𝘰𝘵𝘩𝘪𝘯𝘨 was inspired by her participation in the 2015 student protests, standing against the choice of the new FTII chairperson.",
  },
  {
    url: "/women/assets/Priyanka Chopra.jpg",
    name: "Priyanka Chopra Jonas - Actor and Producer",
    desc: "She started out as an international beauty queen and went on to achieve global domination. Priyanka Chopra Jonas is a multihyphenate who not only straddles different roles — actor, producer and entrepreneur, with a hot moment as a singer too — but shuttles between Bollywood and Hollywood as well. Besides cementing her position as one of the most bankable and versatile performers in Bollywood with her roles in <i>Aitraaz</i> (2004), <i>Don</i> (2006), <i>Fashion</i> (2008), <i>𝘉𝘢𝘳𝘧𝘪!</i> (2012) and <i>Bajirao Mastani</i> (2015), Chopra Jonas soon ventured to foreign shores by becoming the first South Asian woman to headline a major TV series on an American network with <i>Quantico</i> (ABC). Chopra Jonas has long been a proponent of women empowerment, whether it’s through her advocacy work with Unicef, or the roles she portrays on screen. The actor has also championed young storytellers from quieter corners of India through Purple Pebble Pictures, her production company that focuses on regional Indian films, and has produced critically acclaimed projects like <i>Ventilator</i> (2016), <i>Paani</i> (2019), <i>The Sky is Pink</i> (2019), <i>To Kill a Tiger</i> (2022) and <i>Anuja</i> (2024).",
    trivia:
      "Contrary to popular belief, Priyanka Chopra Jonas did not make her acting debut in Bollywood, but with a Tamil film <i>Thamizhan</i> (2002), co-starring Vijay.",
  },
  {
    url: "/women/assets/Priyanka & Swapna.jpg",
    name: "Priyanka C. Dutt and Swapna C. Dutt - Producers, Vyjayanthi Movies",
    desc: "Born into the illustrious Vyjayanthi Movies legacy as daughters of producer C. Ashwini Dutt, sisters Priyanka C. Dutt and Swapna C. Dutt are known for their work in Telugu cinema. Priyanka, a UCLA graduate, honed her craft under filmmaker Shoojit Sircar before co-producing films like <i>Balu</i> (2005) and <i>Jai Chiranjeeva</i> (2005). She also founded Three Angels Studio, producing <i>Baanam</i> (2009) and <i>Om Shanti</i> (2010). Her short film <i>Yaadon Ki Baarat</i> was even screened at Cannes in 2013. Swapna, equally visionary, established Vyjayanthi Televentures in 2008 and in 2014, she launched Swapna Cinema alongside Priyanka. They co-produced <i>Yevade Subramanyam</i> (2015), and the duo’s magnum opus <i>Mahanati</i> (2018), a biopic on the iconic Telugu actor Savitri, won national acclaim. The sisters, who have often deemed their father to be their source of energy and motivation have continued their winning streak with <i>Sita Ramam</i> (2022) and the blockbuster hit, <i>Kalki 2898 AD</i> (2024).",
    trivia:
      "The Dutt sisters’ film, <i>Kalki 2898 AD,</i> is the fourth highest-grossing Telugu film in India.",
  },
  {
    url: "/women/assets/Rashmika Mandanna.jpg",
    name: "Rashmika Mandanna - Actor",
    desc: "The biggest hits of 2023 (<i>Animal</i>), 2024 (<i>Pushpa 2: The Rule</i>) and 2025 (<i>Chhaava</i>) featured Rashmika Mandanna in extremely important roles. Cumulatively, these three hits have contributed upwards of ₹3,000 crore, with <i>Chhaava</i> still running successfully in theatres. Her upcoming releases include some of this year’s biggest films as well, including Salman Khan’s <i>Sikandar,</i> directed by A.R. Murugadoss and the Tamil-Telugu bilingual <i>Kubera,</i> with Dhanush and Nagarjuna in the lead. She also has <i>Thama</i> in Hindi, apart from the leading role in Telugu romantic drama <i>The Girlfriend</i> this year. With more than 45 million followers on Instagram, she is equally powerful as an influencer with brands across product categories, including 7Up, Epson, Kalyan Jewellers, Agoda, Cetaphil and more. Needless to say, that the hook steps from her dance videos are designed to go viral, whenever they come out.",
    trivia:
      "Rashmika Mandanna’s viral song “Saami Saami” from <i>Pushpa: The Rise,</i> in its three versions, have amassed over 115 crore views.",
  },
  {
    url: "/women/assets/Reema Kagti.jpg",
    name: "Reema Kagti - Writer, Producer and Director",
    desc: "Reema Kagti is one of Bollywood’s most distinctive writer-directors, known for films like <i>Honeymoon Travels Pvt. Ltd</i>. (2007), <i>Talaash</i> (2012), and <i>Gold</i> (2018) each of which showcases her unique narrative style. Alongside Zoya Akhtar, she cofounded Tiger Baby Films, a production house committed to telling stories with sharp social commentary. Its projects include the acclaimed series <i>Made in Heaven,</i> the cultural phenomenon <i>Gully Boy</i> (2019), and the gripping crime drama series <i>Dahaad</i>. As a writer, Kagti thrives on complexity, often crafting narratives around flawed, morally ambiguous characters. Whether exploring the supernatural in <i>Talaash,</i> the aspirations of the streets in <i>Gully Boy,</i> or the institutional struggles of a female cop in <i>Dahaad,</i> she brings nuance and depth to every story. With an ever-evolving body of work, Kagti continues to redefine contemporary Indian cinema with bold, thought-provoking storytelling.",
    trivia:
      "Before stepping into filmmaking, Reema Kagti worked as an assistant director on <i>Lagaan</i> (2001). It was only after watching the film that her father finally accepted her decision to pursue a career in filmmaking.",
  },
  {
    url: "/women/assets/ReshmaShetty(Matrix).png",
    name: "Reshma Shetty - Founder, Matrix",
    desc: "Reshma Shetty is a name synonymous with celebrity management in India. As a founder of Matrix, she has been instrumental in shaping the careers of some of the biggest stars in the country, from Alia Bhatt and Priyanka Chopra Jonas to Shahid Kapoor, Abhishek Bachchan and Vicky Kaushal. Beyond actors, Matrix represents directors like Rohit Shetty and Zoya Akhtar and even cricketer KL Rahul. The company has expanded into endorsements, digital platforms, live events and celebrity-led labels like Katrina Kaif’s Kay Beauty in association with Nykaa, Alia Bhatt’s Ed-a-Mamma in association with Reliance Brands, and Priyanka Chopra Jonas’s Anomaly. Shetty’s strategic foresight was evident in Matrix’s merger with Bling, adding industry heavyweights like Vasan Bala, Vidya Balan, Twinkle Khanna, Dimple Kapadia and Konkona Sen Sharma to an already formidable roster. In a traditionally male-dominated field, Shetty has built a women-led agency with industry-first initiatives.",
    trivia:
      "Reshma Shetty dropped out of college to start a model management company called Models Plus in 1994 and merged it with Matrix in 2001.",
  },
  {
    url: "/women/assets/Rima das.jpg",
    name: "Rima Das - Director, Producer and Writer",
    desc: "An acclaimed Indian filmmaker from Assam, Rima Das is celebrated for her authentic storytelling and grassroots approach to cinema. She gained international recognition with her 2017 film <i>Village Rockstars,</i> which won the National Film Award for best feature film and was India’s official entry for the 91st Academy Awards in the best foreign language film category. Das’s work often explores rural life in Assam, capturing the nuances of her native culture. Her subsequent film, <i>Bulbul Can Sing</i> (2018), also premiered at the Toronto International Film Festival and garnered critical acclaim. Operating largely as a one-woman crew, she writes, directs, produces and edits her films, showcasing a distinctive independent filmmaking style. With her ability to craft intimate, deeply human stories on a modest budget, Das has redefined regional cinema’s global reach, proving that powerful narratives can emerge from the simplest, most rooted settings.",
    trivia:
      "Before embarking on her filmmaking journey, Rima Das moved to Mumbai in 2003 with aspirations of becoming an actor. She acted in plays, including an adaptation of Premchand’s <i>Godaan</i> staged at Prithvi Theatre.",
  },
  {
    url: "/women/assets/Sai Pallavi.jpg",
    name: "Sai Pallavi - Actor",
    desc: "After having the most sensational debut with her role as “Malar miss” in the Malayalam film <i>Premam</i> in 2015, Sai Pallavi emerged as one of the best actors of her generation in the decade that followed. Growing up in Coimbatore, she first gained popularity as a dancer on a few reality TV shows, and then balanced a fledgling film career along with studying medicine at Tbilisi State Medical University in Georgia. Pallavi has also earned plaudits for her refusal to endorse fairness creams and for speaking out about society’s fixation with fair skin. Her performances in recent films like <i>Virata Parvam</i> (2022), <i>Gargi</i> (2022) and <i>Amaran</i> (2024) received universal acclaim, and the actor is set to enter Bollywood soon with one of Indian cinema’s most ambitious projects ever: Nitesh Tiwari’s adaptation of the Ramayana.",
    trivia:
      "Sai Pallavi and Dhanush’s song “Rowdy Baby” from <i>Maari 2</i> (2018) is among the top 10 most-viewed YouTube Indian music videos of all time.",
  },
  {
    url: "/women/assets/Samantha.jpg",
    name: "Samantha Ruth Prabhu - Actor and Producer",
    desc: "Since debuting in <i>Ye Maaya Chesave</i> 15 years ago, Samantha Ruth Prabhu has gone on to become one of South India’s most bankable stars, starring in several Tamil and Telugu blockbusters such as <i>Eega</i> (2012), <i>Theri</i> (2016) and <i>Rangasthalam</i> (2018). Furthermore, her performances in films like <i>Mahanati</i> (2018) and <i>Super Deluxe</i> (2019) brought her critical acclaim. More recently, starring in the second season of <i>The Family Man</i> and <i>Citadel: Honey Bunny,</i> along with the viral highs of <i>Pushpa: The Rise</i>’s “Oo Antava” endeared her to Hindi-speaking audiences. She is set to continue her impressive run on streaming by headlining Raj & DK’s fantasy drama <i>Rakt Brahmand – The Bloody Kingdom</i> next. Prabhu has earned much praise from fans and film-industry colleagues alike for being vocal about her health setbacks, raising awareness about the autoimmune disease myositis, and addressing the need for actors to take breaks and focus on their mental and physical wellness amidst demanding schedules.",
    trivia:
      "Like everyone else last year, Samantha Ruth Prabhu fell in love with pickleball and even co-owns a team, Chennai Super Champs, which competed in the inaugural World Pickleball League earlier this year.",
  },
  {
    url: "/women/assets/Shraddha Kapoor.jpg",
    name: "Shraddha Kapoor - Actor",
    desc: "One of Hindi cinema’s most popular and highest paid actors, Shraddha Kapoor is synonymous with the record-breaking success of the star-studded <i>Stree</i> franchise. But her legacy as a bankable name goes beyond the current horror-comedy boom. Despite belonging to the nepo-kid ecosystem, Kapoor has always been more of an outsider even within the insider bracket. Over the years, she has established her own independent space within this glamorous multiverse, one that’s devoid of prolific studio deals and godfathers. Kapoor’s hits range from Luv Ranjan and Mohit Suri musicals to Nitesh Tiwari and Vishal Bhardwaj dramas. She is also a singer and a fashion icon, but it’s the controlled fragility that she brings to her roles that have subverted Bollywood’s damsel-in-distress stereotype. Her rise has been shaped by the advent of social media and digital discourse.",
    trivia: "Shraddha Kapoor is the most followed Indian woman on Instagram.",
  },
  {
    url: "/women/assets/Shreya Ghoshal.jpg",
    name: "Shreya Ghoshal - Singer",
    desc: "For over two decades, Shreya Ghoshal has been the unwavering voice in an industry where voices often fade with time. She doesn’t just sustain excellence, she redefines it, song after song, year after year. From “Bairi Piya” in <i>Devdas</i> to “Angaaron” in <i>Pushpa 2: The Rule</i> (which she sang in six languages), her voice has remained the gold standard, effortlessly adapting to changing musical landscapes while never losing its signature depth and finesse. Ghoshal has also long championed independent music, stepping beyond playback to explore sounds on her own terms. As Bollywood music became increasingly male-dominated, concerns grew over the shrinking space for female singers; women were often left with just two to four lines in a supposed “duet”. Ghoshal openly addressed the issue, pointing out that if female characters were sidelined in the film’s narrative, it was inevitable that their musical presence would suffer too. At a time when the industry was becoming disappointingly lopsided, she turned to independent music, reclaiming her voice beyond the restrictions of film.",
    trivia:
      "It was director Sanjay Leela Bhansali’s mother who was first enchanted by a young Shreya Ghoshal on TV. She insisted that Bhansali cast her voice for <i>Devdas</i>.",
  },
  {
    url: "/women/assets/Sophia Paul.jpg",
    name: "Sophia Paul - CEO, Weekend Blockbusters",
    desc: "It’s hard to be an actor turned producer in all the male-dominated Indian film industries. It’s harder to be a solo woman producer in the Malayalam film industry. Sophia Paul is perhaps the most popular name in this rarified space today. Since kickstarting her production company Weekend Blockbusters with the unprecedented success of Anjali Menon’s <i>Bangalore Days</i> in 2014, Paul has spent the last decade being a hit machine — or having “that Midas touch”. But it’s the diversity of her post-debut slate that’s earned her national recognition. From a film-festival darling like <i>Kaadu Pookkunna Neram</i> (2016) to a superstar-fronted drama like <i>Munthirivallikal Thalirkkumbol</i> (2017) and a clutter-breaking superhero saga like 2021’s <i>Minnal Murali</i> — the first film of the “Weekend Cinematic Universe” — Paul’s progression has been anything but conventional or safe. Controversies with the film notwithstanding, she cemented her reputation with action thriller <i>RDX</i> (2023), one of the highest grossing Malayalam movies of all time.",
    trivia:
      "Married into a family of film producers, Sophia Paul has lived in Dubai since 1986. She is also a restaurateur; her Dubai restaurant specialises in Kollam cuisine.",
  },
  {
    url: "/women/assets/Sudha-Kongara.jpg",
    name: "Sudha Kongara - Director and Writer",
    desc: "Award-winning director Sudha Kongara has been in the film industry for the better part of the last two decades, starting off as a screenwriter before her directorial ventures catapulted her in the public eye. Her most famous films — <i>Irudhi Suttru</i> (2016) and <i>Soorarai Pottru</i> (2020) — draw loosely from real life stories of the everyman’s personal triumph over extraordinary adversity. They have seen critical and commercial acclaim and have been remade in multiple languages, with <i>Soorarai Pottru</i> garnering five national awards including one for Kongara for the screenplay. Her films have been lauded for their depiction of strong female protagonists. Her upcoming feature, <i>Parasakthi,</i> starring Sivakarthikeyan, is a period drama that is reportedly set to tackle events surrounding the anti-Hindi protests that took place in Tamil Nadu in the 1960s.",
    trivia:
      "Prior to her full-fledged directorial debut, Sudha Kongara spent close to a decade working with veteran director Mani Ratnam.",
  },
  {
    url: "/women/assets/Sunidhi Chauhan.jpg",
    name: "Sunidhi Chauhan - Singer",
    desc: "Sunidhi Chauhan is the powerhouse voice behind some of Bollywood’s most iconic tracks — “Beedi”, “Dhoom Machale”, “Kamli”, and “Sheila Ki Jawani”, to name a few. With her unmistakable vocal texture, she has shaped the sound of contemporary Hindi film music. A child prodigy, she got her big break with the song “Ruki Ruki Si Zindagi” from <i>Mast</i> (1999), and there has been no looking back since. From high-energy dance anthems to soulful ballads, she has lent her voice to a vast range of genres, effortlessly adapting to different musical styles. Beyond Bollywood, she has sung in multiple Indian languages and ventured into independent music, showcasing her versatility as an artiste. She also disrupted the Indian concert scene with her I Am Home tour, where she danced on stage while singing live — an uncommon practice in Indian concerts. Decades into her career, she remains one of India’s most influential and sought-after vocalists.",
    trivia:
      "Sunidhi Chauhan was discovered by the late veteran actress Tabassum who introduced her to iconic music composers like Naushad and duo Kalyanji-Anandji.",
  },
  {
    url: "/women/assets/Supriya Yarlagadda.jpg",
    name: "Supriya Yarlagadda - Producer and Executive Director, Annapurna Studios",
    desc: "A producer and executive director of Annapurna Studios, Supriya Yarlagadda is also a Telugu film actor. Under her stewardship as executive director since 2000, the family’s studio, founded by her grandfather, the late Akkineni Nageswara Rao, churned out over 17 films, including distributing films like last year’s <i>Premalu</i> and <i>Kalki 2898 AD</i>. The studio also has its own post-production facilities. As a producer Yarlagadda is attempting to bridge the gap between “small” and “big” films, “Tollywood” and “Bollywood”. She has released Telugu films in other languages, and believes that regional and linguistic barriers are blurring today.",
    trivia:
      "Supriya Yarlagadda’s co-star in her debut film <i>Akkada Ammayi Ikkada Abbayi</i> (1996) was Pawan Kalyan, who is the current deputy chief minister of Andhra Pradesh.",
  },
  {
    url: "/women/assets/Taapsee Pannu.jpg",
    name: "Taapsee Pannu - Actor and Producer",
    desc: "Fearless, sharp, strong and unstoppable — Taapsee Pannu is all that and much more. Even a cursory glance at her career (and her off-screen persona) signals Pannu’s terrific rise to the top, marking her as an actor with substance. Pannu has straddled the world of larger-than-life commercial films and acclaimed mid-sized outings. Even before she made her Bollywood debut in 2013 with <i>Chashme Baddoor,</i> the actor had already featured in 10 films across the Tamil, Telugu and Malayalam film industries. Pannu was a true “pan India” actor much before the term came into regular film discourse, as she continued to feature in movies of other languages even as her Hindi movie career took off with <i>Pink</i> in 2016. Since then, Pannu has headlined several socially relevant movies like <i>Mulk</i> (2018) and <i>Thappad</i> (2020) and even turned producer with <i>Blurr</i> in 2022, followed by the much-loved <i>Dhak Dhak</i> in 2023.",
    trivia:
      "Before she started acting in films, Taapsee Pannu was a software engineer.",
  },
  {
    url: "/women/assets/Trisha.jpg",
    name: "Trisha Krishnan - Actor",
    desc: "When Trisha Krishnan won the Miss Chennai pageant as a teenager in 1999, she had absolutely no aspirations of becoming an actor and found the idea silly. Twenty-six years later, despite her reluctant entry into films, she remains the Tamil film industry’s numero uno star. Along with commercially successful ventures like <i>Saamy</i> (2003), <i>Ghilli</i> (2004), <i>Mankatha</i> (2011), <i>Leo</i> (2023), and the <i>Ponniyin Selvan</i> duology (2022, 2023), the actor also gave Tamil cinema two of its most iconic, memorable and oft-quoted characters in <i>Vinnaithaandi Varuvaayaa</i>'s Jessie (2010) and <i>96</i>'s Jaanu (2018).<br/> A Mani Ratnam favourite, Krishnan is currently working on her fourth film with the director, <i>Thug Life,</i> headlined by Kamal Haasan, which is set to release this year. The actor also enjoys considerable fandom in Telugu after hits like <i>Varsham</i> (2004) and <i>Nuvvostanante Nenoddantana</i> (2005); she made her web-series debut last year in the same language with the crime-thriller <i>Brinda</i>. One of South India’s highest-paid stars, Krishnan is also a major advocate for pet adoption and constantly bats for the cause and supports Peta.",
    trivia:
      "In 2017, Trisha Krishnan became the first actor from South India to be bestowed with Unicef’s celebrity advocate status, to support the rights of children and youngsters, especially girls, in Tamil Nadu and Kerala.",
  },
  {
    url: "/women/assets/Vidya Balan.jpg",
    name: "Vidya Balan - Actor",
    desc: "It wouldn’t be wrong to argue that Vidya Balan sparked a whole new era for women in Hindi cinema by playing author-backed roles right from the start, instead of heroines who play second fiddle to a film’s narrative. With her National Award–winning performance in <i>The Dirty Picture</i> (2011), Balan showcased her audacious range as an artiste who did not shy away from playing a sex symbol only years after making her Hindi film debut with her tender portrayal of a woman in 1960s’ Kolkata in <i>Parineeta</i> (2005), which won her a Filmfare award. Balan’s early winning streak attained its peak in 2012 with Sujoy Ghosh’s thriller <i>Kahaani</i> (2012), where her portrayal of a seemingly hapless pregnant woman looking for her missing husband redefined the way roles for women were written. Balan’s Padma Shri win in 2014 is a testament to why she’s considered a pioneer among leading ladies who didn’t have to let go of their femininity to draw in audiences to theatres and win at the box office.",
    trivia:
      "Vidya Balan’s acting debut was with the Bengali film <i>Bhalo Theko</i> (2003), directed by Goutam Halder, and co-starring Soumitra Chatterjee and Parambrata Chattopadhyay, who also appeared in <i>Kahaani</i>.",
  },
  {
    url: "/women/assets/Entertainment Power List.jpg",
    name: "Women in Cinema Collective",
    desc: "The Women in Cinema Collective (WCC) is an organisation for women working in the Malayalam cinema industry formed on 1 November 2017 following a sexual assault case involving a prominent Malayali actress. While its work is geared toward immediate goals, providing a platform for immediate redressal, like strongly condemning the decision of the Association of Malayalam Movie Artistes (AMMA) to reinstate actor Dileep, who was accused of the above abduction and sexual assault, they also work towards bringing structural changes in the workplace — formalising wage structures, welfare benefits, providing technical courses to provide direct employment opportunities for women, and so on. The WCC includes prominent names like Manju Warrier, Parvathy Thiruvothu, Anjali Menon, Geetu Mohandas, and Rima Kallingal. Because of their campaigning, the Kerala High Court announced a verdict of strict adherence to the Protection of Women from Sexual Harassment at Workplace (PoSH) Act in all film units.",
    trivia:
      "The WCC’s efforts also led to the creation of the Hema Committee, formed by the state government to study the problems faced by women in the Malayalam film industry.",
  },
  {
    url: "/women/assets/Zoya Akhtar.jpg",
    name: "Zoya Akhtar - Director, Writer and Producer",
    desc: "Since her directorial debut with <i>Luck by Chance</i> (2009), Zoya Akhtar has done it all — from screenwriting and producing to ultimately launching her own production company Tiger Baby Films in 2015, with long-time collaborator Reema Kagti. Despite hailing from a family of film writers and actors, Akhtar has proved her mettle beyond just being an industry insider by delivering consecutive hits. It all started with 2011’s <i>Zindagi Na Milegi Dobara</i> boasting a starry ensemble cast that served serious life questions in the shape of a frothy road movie. She followed it up with <i>Dil Dhadakne Do</i> in 2015, this time on a luxury cruise, poking holes into the insular lives of the super rich, until she turned the script on its head and made <i>Gully Boy</i> (2019) on the Indian street-rapping subculture in the slums of Mumbai’s Dharavi. It took the awards season and box office by storm, earning the movie 13 Filmfare Awards — the most ever won by a Hindi film. It was also India’s official entry to the Oscars in 2020.",
    trivia:
      "Aamir Khan, who back in the day had a strict “no cameo” policy, made an exception for Zoya Akhtar’s debut film <i>Luck by Chance</i>.",
  },
];

const WomenInEntertainment = ({ meta }) => {
  const [ContentIndex, setContentIndex] = useState(0);
  // const [mblContentIndex, setmblContentIndex] = useState(0);
  // const [isOpen, setIsOpen] = useState(false);
  // const [windowWidth, setWindowWidth] = useState(0);
  // const [currentIndex, setCurrentIndex] = useState(0); // Start with the large slide (index 3)
  // const [slides, setSlides] = useState([]);
  let breakword = imageObjects[ContentIndex].name;
  // Split the name at " - "
  const [firstPart, secondPart] = breakword.split(" - ");
  useEffect(() => {
    const textContent = document.querySelectorAll(".heroHeading span");
    var split = new SplitText(textContent, {
      type: "words,lines",
      linesClass: "split-line",
    });
    // gsap.set(split, { yPercent: 200 });
    gsap.fromTo(
      ".split-line div",
      {
        transform: "translateY(100%)",
        opacity: 0,
      },
      {
        transform: "translateY(0%)",
        duration: 0.5,
        opacity: 1,
        delay: 0.5,
        stagger: {
          amount: 0.5,
        },
      }
    );
  }, []);
  useEffect(() => {
    gsap.to(".split-line div", {
      transform: "translateY(0%)",
      duration: 0.5,
      stagger: {
        amount: 0.5,
      },
      // delay: 1,
    });
  }, []);

  var dimensions = {
    itemSize: 0,
    containerSize: 0,
    indicatorSize: 0,
  };
  var maxTranslate = 0;
  var currentTranslate = 0;
  var targetTranslate = 0;
  var isClickMove = false;
  var currentImageIndex = 0;
  const activeItemOpacity = 1;
  useEffect(() => {
    // const container = document.querySelector(".left");
    var container = document.querySelector(".Slidercontainer");
    var containerMinimap = document.querySelector(".minimap");
    var items = document.querySelector(".items");
    var indicator = document.querySelector(".indicator");
    var itemElements = document.querySelectorAll(".item");
    var previewImage = document.querySelector(".img-preview img");
    var itemImages = document.querySelectorAll(".item img");
    var isHorizontal = window.innerWidth <= 991;

    function onRunRabbit() {
      // Check if elements exist before using them
      if (
        !container ||
        !items ||
        !indicator ||
        itemElements.length === 0 ||
        !previewImage ||
        itemImages.length === 0
      ) {
        console.error("Some required elements are missing in the DOM");
        return; // Exit if necessary elements are missing
      }

      // lerp function for smooth animation
      function lerp(start, end, factor) {
        return start + (end - start) * factor;
      }

      // Update dimensions based on window size
      function updateDimensions() {
        isHorizontal = window.innerWidth <= 992;
        if (isHorizontal) {
          dimensions = {
            itemSize: itemElements[0].getBoundingClientRect().width,
            containerSize: items.scrollWidth,
            indicatorSize: indicator.getBoundingClientRect().width,
          };
        } else {
          dimensions = {
            itemSize: itemElements[0].getBoundingClientRect().height,
            containerSize: items.getBoundingClientRect().height,
            indicatorSize: indicator.getBoundingClientRect().height,
          };
        }
        return dimensions;
      }

      dimensions = updateDimensions();
      maxTranslate = dimensions.containerSize - dimensions.indicatorSize;

      // Get the item that is currently inside the indicator
      function getItemInIndicator() {
        itemImages.forEach((img) => (img.style.opacity = 0.2));
        const indicatorStart = -currentTranslate;
        const indicatorEnd = indicatorStart + dimensions.indicatorSize;
        let maxOverlap = 0;
        let selectedIndex = 0;

        itemElements.forEach((item, index) => {
          const itemStart = index * dimensions.itemSize;
          const itemEnd = itemStart + dimensions.itemSize;
          const overlapStart = Math.max(indicatorStart, itemStart);
          const overlapEnd = Math.min(indicatorEnd, itemEnd);
          const overlap = Math.max(0, overlapEnd - overlapStart);

          if (overlap > maxOverlap) {
            maxOverlap = overlap;
            selectedIndex = index;
          }
        });

        itemImages[selectedIndex].style.opacity = activeItemOpacity;
        setContentIndex(selectedIndex);

        return selectedIndex;
      }

      // Update preview image when index changes
      function updatePreviewImage(index) {
        // Check if the item exists
        const targetItem = itemElements[index];
        if (!targetItem) {
          console.error("Target item not found at index " + index);
          return; // Exit early if the target item doesn't exist
        }

        // Ensure there's an image inside the item
        const img = targetItem.querySelector("img");
        if (!img) {
          console.error("No image found in the target item at index " + index);
          return;
        }

        // Update the preview image only if the source is different
        const targetSrc = img.getAttribute("src");
        if (targetSrc !== previewImage.getAttribute("src")) {
          // Fade out the preview image
          previewImage.setAttribute("src", targetSrc);
          gsap.fromTo(
            previewImage,
            {
              scale: 1,
            },
            {
              scale: 1,
              duration: 0.5,
            }
          );
        }
      }

      // Animation loop
      function animate() {
        const lerpFactor = isClickMove ? 0.05 : 0.075;
        currentTranslate = lerp(currentTranslate, targetTranslate, lerpFactor);
        if (Math.abs(currentTranslate - targetTranslate) > 0.01) {
          const transform = isHorizontal
            ? `translateX(${currentTranslate}px)`
            : // : "";
              `translateY(${currentTranslate}px)`;
          items.style.transform = transform;
          const activeIndex = getItemInIndicator();
          updatePreviewImage(activeIndex);
        } else {
          isClickMove = false;
        }
        requestAnimationFrame(animate);
      }

      // Touch start event
      let touchStartX = 0;
      let touchStartY = 0;
      containerMinimap.addEventListener("touchstart", (e) => {
        if (isHorizontal) {
          touchStartX = e.touches[0].clientX;
        } else {
          touchStartY = e.touches[0].clientY; // Capture starting Y position of touch
        }
      });

      // Touch move event for mobile
      containerMinimap.addEventListener(
        "touchmove",
        (e) => {
          if (isHorizontal) {
            const touchX = e.touches[0].clientX;
            const deltaX = touchStartX - touchX;
            const scrollVelocity = Math.min(Math.max(deltaX * 0.5, -50), 50);
            targetTranslate = Math.min(
              Math.max(targetTranslate - scrollVelocity, -maxTranslate),
              0
            );
            touchStartX = touchX;
            e.preventDefault();
          } else {
            const touchY = e.touches[0].clientY;
            const deltaY = touchStartY - touchY;
            const scrollVelocity = Math.min(Math.max(deltaY * 0.5, -20), 20);
            targetTranslate = Math.min(
              Math.max(targetTranslate - scrollVelocity, -maxTranslate),
              0
            );
            touchStartY = touchY;
            e.preventDefault();
          }
        },
        { passive: false }
      );

      // Item click event
      itemElements.forEach((item, index) => {
        item.addEventListener("click", () => {
          isClickMove = true;
          targetTranslate =
            -index * dimensions.itemSize +
            (dimensions.indicatorSize - dimensions.itemSize) / 2;
          targetTranslate = Math.max(
            Math.min(targetTranslate, 0),
            -maxTranslate
          );
          document.querySelector(".paragraphcntr").scrollTo(0, 0);
        });
      });

      // Resize event
      window.addEventListener("resize", () => {
        dimensions = updateDimensions();
        const newMaxTranslate =
          dimensions.containerSize - dimensions.indicatorSize;
        targetTranslate = Math.min(
          Math.max(targetTranslate, -newMaxTranslate),
          0
        );
        currentTranslate = targetTranslate;
        const transform = isHorizontal
          ? `translateX(${currentTranslate}px)`
          : `translateY(${currentTranslate}px)`;
        items.style.transform = transform;
      });

      // Observe when the items container enters/exits the viewport
      // const observer = new IntersectionObserver(
      //   ([entry]) => {
      //     if (entry.isIntersecting) {
      //       // Re-enable the wheel event listener when .items enters the viewport
      //       container.addEventListener("wheel", handleWheel, {
      //         passive: false,
      //       });
      //     } else {
      //       // Disable the wheel event listener when .items exits the viewport
      //       container.removeEventListener("wheel", handleWheel, {
      //         passive: false,
      //       });
      //     }
      //   },
      //   { threshold: 0.1 } // You can adjust this threshold based on how much of the element should be visible
      // );

      // observer.observe(items); // Start observing the .items container

      updatePreviewImage(0);
      animate();
    }

    onRunRabbit();
  }, []);

  // newwebsitecode
  useGSAP(() => {
    ScrollTrigger.matchMedia({
      "(min-width: 768px)": function () {
        if (ScrollTrigger.isTouch !== 1) {
          // touch-only device
          var heroPin = gsap.timeline({
            scrollTrigger: {
              trigger: ".hero-section",
              pin: ".hero-heading-content",
              start: "top top",
              end: "bottom top",
              pinType: "Transform",
            },
          });

          window.addEventListener("resize", function () {
            ScrollTrigger.sort();
            ScrollTrigger.refresh();
          });
        }
      },
    });
    var split = new SplitText("[data-text-fly-heading]", { type: "words" });

    var videoTextPin = gsap.timeline({
      scrollTrigger: {
        trigger: ".text-section",
        start: "top bottom",
        end: "bottom top",
        scrub: true,
        pin: ".video-pin-wrapper",
        invalidateOnRefresh: true,
        ignoreMobileResize: true,
      },
    });

    var videoText = gsap.timeline({
      scrollTrigger: {
        trigger: ".text-section",
        start: "top bottom",
        end: "bottom -=75%",
        scrub: true,
        invalidateOnRefresh: true,
      },
    });
    videoText.fromTo(
      ".hero-video",
      {
        width: () => window.innerWidth,
        height: () => window.innerHeight,
        transform: "scale(1)",
      },
      {
        //   scale: 0.6,
        width: () => 0,
        height: () => 0,
        transform: "scale(0)",
        ease: "power1.inOut",
      }
    );

    videoText.fromTo(
      split.words,
      {
        "will-change": "transform, opacity",
        z: () => gsap.utils.random(750, 850),
        // opacity: 0,
        xPercent: (pos) => gsap.utils.random(-75, 75),
        yPercent: (pos) => gsap.utils.random(-25, 25),
        // rotationX: (pos) => gsap.utils.random(-90, 90),
      },
      {
        ease: "expo.inOut",
        // opacity: 1,
        xPercent: 0,
        yPercent: 0,
        // rotateX: 0,
        z: 0,
        stagger: {
          each: 0.006,
          from: "random",
        },
      },
      "<"
    );

    videoText.fromTo(
      "[data-text-fly-heading]",
      {
        opacity: -1.2,
      },
      {
        opacity: 1,
        easing: "expo.out",
        duration: 0.2,
        // delay: 0.06,
      },
      "<25%"
    );

    videoText.to(".text-fly-wrapper", {
      yPercent: () => -75,
      ease: "power1.inOut",
    });

    // videoText.fromTo(
    //   ".fly-in-image",
    //   {
    //     opacity: -1.2,
    //   },
    //   {
    //     opacity: 1,
    //     easing: "expo.out",
    //     duration: 0.1,
    //     // delay: 0.06,
    //   },
    //   "<90%"
    // );
    const flyInImages = gsap.utils.toArray(".fly-in-image");

    flyInImages.forEach((image) => {
      gsap.to(image.querySelector(".fly-in-tag"), {
        y: () =>
          (1 - parseFloat(image.getAttribute("data-speed"))) *
          window.innerHeight,
        scrollTrigger: {
          trigger: image,
          scrub: 0,
          start: "top bottom",
          end: () =>
            `bottom -=${
              (1 - parseFloat(image.getAttribute("data-speed"))) *
              window.innerHeight
            }px`,
          ease: "none",
        },
      });
    });

    // capsScroller
    var capsScroller = gsap.timeline({
      scrollTrigger: {
        trigger: ".capabilities-section",
        start: "top top",
        end: "bottom top",
        scrub: true,
        pin: ".cap-pin",
        invalidateOnRefresh: true,
        ignoreMobileResize: true,
      },
    });

    var capsPin = gsap.timeline({
      scrollTrigger: {
        trigger: ".capabilities-section",
        start: "top bottom",
        end: "bottom top",
        scrub: true,
        invalidateOnRefresh: true,
        ignoreMobileResize: true,
        ease: "none",
      },
    });

    capsPin.fromTo(
      ".cap-track",
      {
        x: () => window.innerWidth * 0.8,
      },
      {
        x: () =>
          -gsap.getProperty(".cap-track", "width") + window.innerWidth * 0.2,
        ease: "none",
      }
    );

    var cards = gsap.utils.toArray("[data-cap-card]");

    cards.forEach(function (card, index) {
      gsap
        .timeline({
          scrollTrigger: {
            trigger: card,
            start: () => `left 70%`,
            end: () => `left 40%`,
            scrub: true,
            containerAnimation: capsPin,
            ease: "none",
            invalidateOnRefresh: true,
            ignoreMobileResize: true,
          },
        })
        .fromTo(
          card.querySelectorAll("[data-cap-fadein]"),
          {
            opacity: 0,
          },
          {
            opacity: 1,
          }
        )
        .fromTo(
          card.querySelector(".cap-list-item-bar"),
          {
            height: (i, el) => (el.style.height = "15%"),
            width: 1,
            backgroundColor: "rgba(0, 0, 0, 0.26)",
          },
          {
            height: (i, el) => (el.style.height = "100%"),
            backgroundColor: "#ff1e00",
            width: 2,
          },
          "<"
        );
    });
  });

  // newwebsitecode
  // State to track whether the volume is on or off
  const [volumeClassAdd, setVolumeClassAdd] = useState(false);

  // State to determine if we are on mobile
  const [isMobile, setIsMobile] = useState(false);

  // Detect if the device is mobile based on window width

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 0); // Adjust based on your breakpoint for mobile
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial check

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const volumeOnOffHandler = () => {
    setVolumeClassAdd((prevState) => {
      const newVolumeState = !prevState; // Toggle the volume state

      // Access video elements via id or class
      const desktopVideo = document.querySelector("#desktop-video");

      if (desktopVideo) {
        if (newVolumeState) {
          // If volume is on, play and unmute desktop video
          desktopVideo.play();
          desktopVideo.muted = false;
        } else {
          // If volume is off, pause and mute desktop video
          desktopVideo.pause();
          desktopVideo.muted = true;
        }
      }

      return newVolumeState; // Return the new state for volume
    });
  };
  const [topSoundClass, setTopSoundClass] = useState("bottom_sound");

  useEffect(() => {
    const handleScroll = () => {
      const threshold = window.innerWidth >= 991 ? 2000 : 1500;
      // 100vh in pixels
      if (window.scrollY >= window.innerHeight + threshold) {
        setTopSoundClass("Top_sound");
      } else {
        setTopSoundClass("bottom_sound");
      }
    };

    // Add event listener for scroll
    window.addEventListener("scroll", handleScroll);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useGSAP(() => {
    const items = document.querySelector(".items");
    const container = document.querySelector(".Slidercontainer");
    const thresholdH = window.innerWidth >= 0;
    const maxheight = items.scrollHeight;
    const endPoint = maxheight + window.innerHeight;
    // let isWheelActive = true;
    setTimeout(() => {
      ScrollTrigger.sort();
      ScrollTrigger.refresh();
    }, 300);
    if (thresholdH) {
      //   e.preventDefault();
      // isClickMove = false;
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: ".Slidercontainer",
          start: "top top",
          end: window.innerWidth >= 991 ? "top -400%" : "top -200%", // Adjust this value based on required scroll effect
          markers: false,
          pin: true,
          scrub: true,
          pinSpacer: false,

          onUpdate: (self) => {
            // Check if the container is pinned (progress > 0)
            if (self.progress > 0) {
              targetTranslate = -(self.progress * maxTranslate);
              // console.log(targetTranslate);
              document.querySelector(".paragraphcntr").scrollTo(0, 0);
            }
          },
        },
      });
    }
    // Handle the wheel event to log deltaY and update the translation
    // const handleWheel = (e) => {
    //   e.preventDefault();
    //   // if (!isWheelActive) return; // If wheel event is not active, prevent any actions
    //   // isClickMove = false;
    //   var delta = e.deltaY;
    //   console.log("Scroll deltaY:", delta);
    //   // if (container.getBoundingClientRect().top > 0) {
    //   //   return;
    //   // }

    //   // Adjust translation with scroll velocity
    //   const scrollVelocity = Math.min(Math.max(delta * 0.5, -30), 30);
    //   targetTranslate = Math.min(
    //     Math.max(targetTranslate - scrollVelocity, -maxTranslate),
    //     0
    //   );

    //   // Log or apply any changes to the targetTranslate here if needed
    //   console.log("Current target translate:", targetTranslate);
    // };
    // Observe when the items container enters/exits the viewport
    // const observer = new IntersectionObserver(
    //   ([entry]) => {
    //     if (entry.isIntersecting) {
    //       // Re-enable the wheel event listener when .items enters the viewport
    //       container.addEventListener("wheel", handleWheel, {
    //         passive: false,
    //       });
    //     } else {
    //       // Disable the wheel event listener when .items exits the viewport
    //       container.removeEventListener("wheel", handleWheel, {
    //         passive: false,
    //       });
    //     }
    //   },
    //   { threshold: 0.1 } // You can adjust this threshold based on how much of the element should be visible
    // );

    // observer.observe(items);
  });

  // const [activeIndexAcc, setActiveIndexAcc] = useState(null); // Track the active accordion index
  // const accordionRef = useRef(null); // Ref to target the accordion section

  // const toggleAccordion = (index) => {
  //   setActiveIndexAcc((prevIndex) => (prevIndex === index ? null : index)); // Toggle accordion
  //   ScrollTrigger.refresh();
  // };

  // useEffect(() => {
  //   const accordionSection = accordionRef.current; // Get the accordion container
  //   const accordionBodies =
  //     accordionSection.querySelectorAll(".accordion-body");
  //   const accordionIcons = accordionSection.querySelectorAll(
  //     ".accordion-header-icon-image"
  //   );

  //   accordionBodies.forEach((body, index) => {
  //     if (activeIndexAcc === index) {
  //       // Animate open state
  //       gsap.to(body, {
  //         height: "auto",
  //         duration: 0.5,
  //         ease: "power1.out",
  //       });
  //       gsap.to(accordionIcons[index], {
  //         rotation: 45,
  //         duration: 0.3,
  //         ease: "power1.out",
  //       });
  //     } else {
  //       // Animate close state
  //       gsap.to(body, {
  //         height: 0,
  //         duration: 0.5,
  //         ease: "power1.out",
  //       });
  //       gsap.to(accordionIcons[index], {
  //         rotation: 0,
  //         duration: 0.3,
  //         ease: "power1.out",
  //       });
  //     }
  //   });
  // }, [activeIndexAcc]);

  const [activeIndexAcc, setActiveIndexAcc] = useState(null); // Track the active accordion index
  const accordionRef = useRef(null); // Ref to target the accordion section

  const toggleAccordion = (index) => {
    setActiveIndexAcc((prevIndex) => (prevIndex === index ? null : index)); // Toggle accordion
    setTimeout(() => {
      ScrollTrigger.sort();
      ScrollTrigger.refresh();
    }, 300);
  };

  useEffect(() => {
    const accordionSection = accordionRef.current; // Get the accordion container
    const accordionBodies =
      accordionSection.querySelectorAll(".accordion-body");
    const accordionIcons = accordionSection.querySelectorAll(
      ".accordion-header-icon-image"
    );

    accordionBodies.forEach((body, index) => {
      if (activeIndexAcc === index) {
        // Set the body to display block
        body.style.display = "block";
        accordionIcons[index].style.transform = "rotate(45deg)";
      } else {
        // Set the body to display none
        body.style.display = "none";
        accordionIcons[index].style.transform = "rotate(0deg)";
      }
    });
  }, [activeIndexAcc]);

  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <nav>
        <Link href={"/"} className="nav_innercntr">
          <img
            src="https://www.hollywoodreporterindia.com/thr-logo.png"
            alt="The Hollywood Reporter India"
          />
          {/* <p>
            <RiMenuFill onClick={handleClick} />
          </p> */}
        </Link>
      </nav>
      {/* newwebsitecode */}
      <div className="page-wrapper">
        <div className={topSoundClass}>
          <div>
            <div className="volume" onClick={volumeOnOffHandler}>
              <div className="lines">
                <img
                  className={`on ${volumeClassAdd ? "active" : ""}`}
                  alt="lines"
                  src="/women/assets/Waves.png"
                />
                <img
                  className={`off ${volumeClassAdd ? "active" : ""}`}
                  alt="lines"
                  src="/women/assets/WavesOff.png"
                />
              </div>
              <div className="status">
                <p>{volumeClassAdd ? "on" : "off"}</p>
              </div>
              <div className="back-left" />
              <div className="back-right" />
            </div>
          </div>
          <p>
            {" "}
            Tо make this experience more
            <br /> immersive we use sound effects{" "}
          </p>
        </div>

        <section id="top-of-page" className="hero-section">
          <div className="hero-heading-content heroheadingcnt">
            <h1
              data-hero-heading="0"
              className="main-text-heading heroHeading "
            >
              <span className="dala-span">
                Women <br /> in Entertainment <br /> The Power List 2025
              </span>
            </h1>
            <a
              aria-label="Scroll down"
              href="#text-section"
              className="snw-chevron w-inline-block"
            ></a>
            <div className="Rpsgmediatoplogo">
              <Image
                width={1000}
                height={1000}
                src={rpsgmediatoplogo}
                alt="rapsgmedialogo"
              />
            </div>
          </div>

          <div className="hero-heading-content mobile">
            <h1 className="main-text-heading">
              <span className="dala-span">
                Women in Entertainment The Power List 2025
              </span>
            </h1>
          </div>
        </section>
        <section id="text-section" className="text-section">
          <div className="image-fly-in-wrapper back w-dyn-list">
            <div role="list" className="image-fly-in-list w-dyn-items">
              <div
                data-slug="magna-labore-ipsum"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  <style
                    dangerouslySetInnerHTML={{
                      __html:
                        '\n                  [data-slug="magna-labore-ipsum"] {\n                    width= 24vw;\n                    height: 15vw;\n                    top: 46%;\n                    left: 15%;\n                  }\n                ',
                    }}
                  />
                </div>
                <div data-speed="0.8" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages6.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 24vw"
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="adipiscing-amet-magna-ipsum"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  <style
                    dangerouslySetInnerHTML={{
                      __html:
                        '\n                  [data-slug="adipiscing-amet-magna-ipsum"] {\n                    width: 9vw;\n                    height: 11vw;\n                    top: 29%;\n                    left: 35%;\n                  }\n                ',
                    }}
                  />
                </div>
                <div data-speed="" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages7.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 24vw"
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="drones"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  <style
                    dangerouslySetInnerHTML={{
                      __html:
                        '\n                  [data-slug="drones"] {\n                    width: 13vw;\n                    height: 20vh;\n                    top: 94%;\n                    left: 49%;\n                  }\n                ',
                    }}
                  />
                </div>
                <div data-speed="1.12" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages9.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="sed-lorem-ipsum"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  <style
                    dangerouslySetInnerHTML={{
                      __html:
                        '\n                  [data-slug="sed-lorem-ipsum"] {\n                    width: 15vw;\n                    height: 20vw;\n                    top: 78%;\n                    left: 78%;\n                  }\n                ',
                    }}
                  />
                </div>
                <div data-speed="0.7" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages10.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt="Festival"
                    sizes="(max-width: 767px) 100vw, 24vw"
                    className="fly-in-tag"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="video-pin-wrapper">
            <div className="text-fly-wrapper">
              <h2
                data-text-fly-heading=""
                className="main-text-heading text-fly-in splitTextanim"
              >
                <span className="dala-span">
                  <span>THE HOLLYWOOD REPORTER INDIA</span>'S INAUGURAL WOMEN IN
                  ENTERTAINMENT POWER LIST HONOURS SOME OF THE MOST INFLUENTIAL
                  WOMEN IN THE INDUSTRY WHO HAVE RESHAPED THE COUNTRY'S CREATIVE
                  LANDSCAPE, DRIVING CHANGE, ONE STEP AT A TIME
                  <br />
                  <br />
                  By Team <span>THR India</span>
                </span>
              </h2>
            </div>
            {/* <div className="image-fly-in-wrapper old">
              <div data-post-anim={0} className="fly-in-image one">
                <img
                  src="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7/6671bc0f59ed26fd66919474_image%2013.png"
                  loading="lazy"
                  alt=""
                  className="fly-in-tag"
                />
              </div>
              <div data-post-anim={0} className="fly-in-image two">
                <img
                  src="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7/6671bc0f13b88e3ec75da71b_image%208.png"
                  loading="lazy"
                  alt="Soren West"
                  className="fly-in-tag"
                />
              </div>
              <div data-post-anim={0} className="fly-in-image three">
                <img
                  src="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7/6671bc0fab456fe1544d0ef4_image%2010.png"
                  loading="lazy"
                  alt="Panel Discussion"
                  className="fly-in-tag"
                />
              </div>
              <div data-post-anim={0} className="fly-in-image four">
                <img
                  src="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7/6671bc0ff8349f94d90f3fe6_image%209.png"
                  loading="lazy"
                  alt="HBO Max Theatre"
                  className="fly-in-tag"
                />
              </div>
              <div data-post-anim={0} className="fly-in-image five">
                <img
                  src="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7/6671bc0ff8349f94d90f3fd2_image%2011.png"
                  loading="lazy"
                  alt="Monochrome Femme"
                  className="fly-in-tag"
                />
              </div>
              <div data-post-anim={0} className="fly-in-image six">
                <img
                  src="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7/6671bc0f543236a4b4c871ea_image%207.png"
                  loading="lazy"
                  alt="Nike SNKRs Installation"
                  className="fly-in-tag"
                />
              </div>
            </div> */}
            {!isMobile && (
              <div
                data-poster-url="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7%2F66c8ab2eb701db9c8fda344c_SNW%20Landscape%20Slide%20V02-poster-00001.jpg"
                data-video-urls="https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7%2F66c8ab2eb701db9c8fda344c_SNW%20Landscape%20Slide%20V02-transcode.mp4,https://cdn.prod.website-files.com/666c938255c9ed6610cdf0d7%2F66c8ab2eb701db9c8fda344c_SNW%20Landscape%20Slide%20V02-transcode.webm"
                data-autoplay="false" // Update this to false
                data-loop="true"
                data-wf-ignore="true"
                data-post-anim={0}
                className="hero-video w-background-video w-background-video-atom"
              >
                <video
                  id="desktop-video"
                  loop
                  // preload="auto"
                  src="/women/assets/WIE_Intro_website.MP4"
                  type="video/mp4"
                  poster="/women/assets/THR Anupama Thumbnail.jpg"
                  playsInline
                  data-wf-ignore="true"
                  data-object-fit="cover"
                ></video>
              </div>
            )}
          </div>

          <div className="image-fly-in-wrapper front w-dyn-list">
            <div role="list" className="image-fly-in-list w-dyn-items">
              <div
                data-slug="consectetur-lorem-sit"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  {/* <style>
                  [data-slug="consectetur-lorem-sit"] {
                    width: 21vw;
                    height: 16vw;
                    top: 20%;
                    left: 83%;
                  }
                </style> */}
                </div>
                <div data-speed="1.08" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages11.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 21vw"
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="dolore-incididunt"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  {/* <style>
                  [data-slug="dolore-incididunt"] {
                    width: 14vw;
                    height: 19vw;
                    top: 15%;
                    left: 10%;
                  }
                </style> */}
                </div>
                <div data-speed="1.24" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages4.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 21vw"
                    // srcSet=""
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="elit"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  {/* <style>
                  [data-slug="elit"] {
                    width: 14vw;
                    height: 14vw;
                    top: 57%;
                    left: 90%;
                  }
                </style> */}
                </div>
                <div data-speed="" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages2.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 21vw"
                    // srcSet=" "
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="eiusmod-consectetur-ut-elit"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  {/* <style>
                  [data-slug="eiusmod-consectetur-ut-elit"] {
                    width: 7vw;
                    height: 13vw;
                    top: 39%;
                    left: 74%;
                  }
                </style> */}
                </div>
                <div data-speed="0.8" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages3.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 21vw"
                    className="fly-in-tag"
                  />
                </div>
              </div>
              <div
                data-slug="sed-incididunt"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed">
                  {/* <style>
                  [data-slug="sed-incididunt"] {
                    width: 13vw;
                    height: 7vw;
                    top: 62%;
                    left: 40%;
                  }
                </style> */}
                </div>
                {/* <div data-speed="1.1" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages4.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 21vw"
                    className="fly-in-tag"
                  />
                </div> */}
              </div>
              <div
                data-slug="aliqua-ipsum-sit"
                role="listitem"
                className="fly-in-list-item w-dyn-item"
              >
                <div className="w-embed"></div>
                <div data-speed="0.84" className="fly-in-image">
                  <Image
                    src="/women/assets/GettyImages5.jpg"
                    loading="lazy"
                    width={1000}
                    height={1000}
                    alt=""
                    sizes="(max-width: 767px) 100vw, 21vw"
                    className="fly-in-tag"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="capabilities-section">
          <div className="cap-pin">
            <div className="cap-header">
              <h2 className="cap-heading">Methodology</h2>
            </div>
            <div className="cap-frame">
              <div className="cap-track">
                <div className="cap-content">
                  <div className="cap-list w-dyn-list">
                    <div role="list" className="cap-list-wrapper w-dyn-items">
                      <div
                        data-cap-card={0}
                        role="listitem"
                        className="cap-list-item w-dyn-item"
                      >
                        <div className="cap-list-item-stack">
                          <div className="w-layout-vflex cap-list-item-text">
                            <div className="cap-list-num">01.</div>
                            <h3 className="cap-list-heading">
                              Identifying the Top Media Properties
                            </h3>
                            <div
                              data-cap-fadein={0}
                              className="cap-list-description"
                            >
                              To create the <i>THR India</i> List for the 50
                              Most Powerful Women in Indian Media &amp;
                              Entertainment, we first compiled a list of the top
                              200 media properties released in 2024, spanning
                              various platforms and formats. These properties
                              were selected based on viewership or footfalls.
                            </div>
                          </div>
                          <img
                            src="/women/assets/GettyImages9.jpg"
                            loading="eager"
                            data-cap-fadein={0}
                            alt="Creative Direction"
                            sizes="(max-width: 767px) 100vw, (max-width: 991px) 54vw, 41vw"
                            className="cap-list-image"
                          />
                        </div>
                        <div className="cap-list-item-bar" />
                      </div>
                      <div
                        data-cap-card={0}
                        role="listitem"
                        className="cap-list-item w-dyn-item"
                      >
                        <div className="cap-list-item-stack">
                          <div className="w-layout-vflex cap-list-item-text">
                            <div className="cap-list-num">02.</div>
                            <h3 className="cap-list-heading">
                              Shortlisting Female Talent & Executives
                            </h3>
                            <div
                              data-cap-fadein={0}
                              className="cap-list-description"
                            >
                              Women actors, directors, technicians and corporate
                              executives associated with these properties were
                              identified. For actors, directors and technicians,
                              this process was extended to two previous years,
                              that is 2022 and 2023, to identify women who
                              didn’t have a significant release in 2024, but
                              have been associated with the top properties of
                              the preceding years.
                            </div>
                          </div>
                          <img
                            src="/women/assets/GettyImages2.jpg"
                            loading="eager"
                            data-cap-fadein={0}
                            alt="Entertainment Technology "
                            sizes="(max-width: 767px) 100vw, (max-width: 991px) 54vw, 41vw"
                            className="cap-list-image"
                          />
                        </div>
                        <div className="cap-list-item-bar" />
                      </div>
                      <div
                        data-cap-card={0}
                        role="listitem"
                        className="cap-list-item w-dyn-item"
                      >
                        <div className="cap-list-item-stack">
                          <div className="w-layout-vflex cap-list-item-text">
                            <div className="cap-list-num">03.</div>
                            <h3 className="cap-list-heading">
                              Impact Scoring & Rankings
                            </h3>
                            <div
                              data-cap-fadein={0}
                              className="cap-list-description"
                            >
                              Each talent or executive associated with these
                              properties was assigned points from one to five,
                              based on their impact on the property. For
                              example, a supporting actor may be assigned one
                              point, an editor may be assigned three points,
                              while a director may be assigned five points.
                              <br />
                              This was also done in accordance with the content
                              of the property itself, for example, the
                              production designer of a historical film could be
                              assigned more points than that of a romcom. The
                              cumulative points of each such talents or
                              executives were calculated, to identify the most
                              powerful women in entertainment. The top 35 women
                              from this list were selected.
                            </div>
                          </div>
                          <img
                            src="/women/assets/GettyImages11.jpg"
                            loading="eager"
                            data-cap-fadein={0}
                            alt="Entertainment Production"
                            sizes="(max-width: 767px) 100vw, (max-width: 991px) 54vw, 41vw"
                            className="cap-list-image"
                          />
                        </div>
                        <div className="cap-list-item-bar" />
                      </div>
                      <div
                        data-cap-card={0}
                        role="listitem"
                        className="cap-list-item w-dyn-item"
                      >
                        <div className="cap-list-item-stack">
                          <div className="w-layout-vflex cap-list-item-text">
                            <div className="cap-list-num">04.</div>
                            <h3 className="cap-list-heading">
                              Expanding the List Through Influence Metrics
                            </h3>
                            <div
                              data-cap-fadein={0}
                              className="cap-list-description"
                            >
                              This list was thereafter supplemented with
                              additional names identified through other aspects
                              of work such as social media presence, brand
                              endorsements, event appearances, news coverage,
                              online search volumes, awards, and social impact,
                              using data from online research tools, and Ormax
                              Media research conducted over the last 12 months.
                              These parameters were used to calculate a
                              cumulative score for each name, and the top 15
                              women from this list were selected.
                              <br />
                              Combining these methodologies, an integrated list
                              of the most powerful women in entertainment was
                              curated. Presented below, in alphabetical order,
                              is the 2025 power list of women in entertainment.
                            </div>
                          </div>
                          <img
                            src="/women/assets/GettyImages13.jpg"
                            loading="eager"
                            data-cap-fadein={0}
                            alt="Brand Communications"
                            sizes="(max-width: 767px) 100vw, (max-width: 991px) 54vw, 41vw"
                            className="cap-list-image"
                          />
                        </div>
                        <div className="cap-list-item-bar" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section className="capabilities-mobile">
          <div className="cap-header">
            <h2 className="cap-heading">Methodology</h2>
          </div>
          <div className="capability-accordions w-dyn-list" ref={accordionRef}>
            <div role="list" className="accordion-wrapper w-dyn-items">
              <div role="listitem" className="accordion w-dyn-item">
                <button
                  data-w-id="6c3fa506-1c69-5429-e4fe-79e38c2a4a29"
                  className="accordion-header"
                  onClick={() => toggleAccordion(0)}
                >
                  <div className="accordion-header-column">
                    <div className="accordion-number">01.</div>
                    <h4 className="accordion-heading">
                      Identifying the Top Media Properties
                    </h4>
                  </div>
                  <div className="accordion-header-icon">
                    <div className="accordion-header-icon-image w-embed">
                      <AiOutlinePlus />
                    </div>
                  </div>
                </button>
                <div className="accordion-body">
                  <div className="accordion-content">
                    <div className="accordion-description">
                      To create the <i>THR India</i> List for the most powerful
                      women in the media and entertainment industry in the
                      country, a list of the top 200 media properties released
                      in 2024, across platforms and formats, was prepared, based
                      on viewership or footfalls.
                    </div>
                    <img
                      src="/women/assets/GettyImages9.jpg"
                      loading="eager"
                      alt=""
                      sizes="(max-width: 479px) 94vw, (max-width: 767px) 95vw, 100vw"
                      className="image-2"
                    />
                  </div>
                </div>
              </div>
              <div role="listitem" className="accordion w-dyn-item">
                <button
                  data-w-id="6c3fa506-1c69-5429-e4fe-79e38c2a4a29"
                  className="accordion-header"
                  onClick={() => toggleAccordion(1)}
                >
                  <div className="accordion-header-column">
                    <div className="accordion-number">02.</div>
                    <h4 className="accordion-heading">
                      Shortlisting Women Talent & Executives
                    </h4>
                  </div>
                  <div className="accordion-header-icon">
                    <div className="accordion-header-icon-image w-embed">
                      <AiOutlinePlus />
                    </div>
                  </div>
                </button>
                <div className="accordion-body">
                  <div className="accordion-content">
                    <div className="accordion-description">
                      Women actors, directors, technicians and corporate
                      executives associated with these properties were
                      identified. For actors, directors and technicians, this
                      process was extended to two previous years, that is 2022
                      and 2023, to identify women who didn’t have a significant
                      release in 2024, but have been associated with the top
                      properties of the preceding years.
                    </div>
                    <img
                      src="/women/assets/GettyImages2.jpg"
                      loading="eager"
                      alt=""
                      sizes="(max-width: 479px) 94vw, (max-width: 767px) 95vw, 100vw"
                      className="image-2"
                    />
                  </div>
                </div>
              </div>
              <div role="listitem" className="accordion w-dyn-item">
                <button
                  data-w-id="6c3fa506-1c69-5429-e4fe-79e38c2a4a29"
                  className="accordion-header"
                  onClick={() => toggleAccordion(2)}
                >
                  <div className="accordion-header-column">
                    <div className="accordion-number">03.</div>
                    <h4 className="accordion-heading">
                      Impact Scoring & Rankings
                    </h4>
                  </div>
                  <div className="accordion-header-icon">
                    <div className="accordion-header-icon-image w-embed">
                      <AiOutlinePlus />
                    </div>
                  </div>
                </button>
                <div className="accordion-body">
                  <div className="accordion-content">
                    <div className="accordion-description">
                      Each talent or executive associated with these properties
                      was assigned points from one to five, based on their
                      impact on the property. For example, a supporting actor
                      may be assigned one point, an editor may be assigned three
                      points, while a director may be assigned five points.
                      <br />
                      This was also done in accordance with the content of the
                      property itself, for example, the production designer of a
                      historical film could be assigned more points than that of
                      a romcom. The cumulative points of each such talents or
                      executives were calculated, to identify the most powerful
                      women in entertainment. The top 35 women from this list
                      were selected.
                    </div>
                    <img
                      src="/women/assets/GettyImages11.jpg"
                      loading="eager"
                      alt=""
                      sizes="(max-width: 479px) 94vw, (max-width: 767px) 95vw, 100vw"
                      className="image-2"
                    />
                  </div>
                </div>
              </div>
              <div role="listitem" className="accordion w-dyn-item">
                <button
                  data-w-id="6c3fa506-1c69-5429-e4fe-79e38c2a4a29"
                  className="accordion-header"
                  onClick={() => toggleAccordion(3)}
                >
                  <div className="accordion-header-column">
                    <div className="accordion-number">04.</div>
                    <h4 className="accordion-heading">
                      Expanding the List Through Influence Metrics
                    </h4>
                  </div>
                  <div className="accordion-header-icon">
                    <div className="accordion-header-icon-image w-embed">
                      <AiOutlinePlus />
                    </div>
                  </div>
                </button>
                <div className="accordion-body">
                  <div className="accordion-content">
                    <div className="accordion-description">
                      This list was thereafter supplemented with additional
                      names identified through other aspects of work such as
                      social media presence, brand endorsements, event
                      appearances, news coverage, online search volumes, awards,
                      and social impact, using data from online research tools,
                      and Ormax Media research conducted over the last 12
                      months. These parameters were used to calculate a
                      cumulative score for each name, and the top 15 women from
                      this list were selected.
                      <br />
                      Combining these methodologies, an integrated list of the
                      most powerful women in entertainment was curated.
                      Presented below, in alphabetical order, is the 2025 power
                      list of women in entertainment.
                    </div>
                    <img
                      src="/women/assets/GettyImages13.jpg"
                      loading="eager"
                      alt=""
                      sizes="(max-width: 479px) 94vw, (max-width: 767px) 95vw, 100vw"
                      className="image-2"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <div className="Slidercontainer">
          <div className="main-contentcntr">
            <div className="left">
              <div className="img-preview">
                <img
                  // width={1000}
                  // height={1000}
                  src="/women/assets/Kareena1.jpg"
                  alt=""
                />
              </div>
            </div>
            <div className="right">
              <div className="text_content">
                <div className="titleheading">
                  <h2>{firstPart}</h2>
                  <h5>{secondPart}</h5>
                  {imageObjects[ContentIndex].thirdline && (
                    <h5>{imageObjects[ContentIndex].thirdline}</h5>
                  )}
                </div>

                <div className="paragraphcntr">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: imageObjects[ContentIndex].desc,
                    }}
                  />

                  <br />
                  <br />
                  <div className="triviaCntr">
                    <span className="trivalogocntr">
                      <Image
                        src={trivialogo}
                        width={100}
                        height={100}
                        alt="trivialogo"
                        loading="lazy"
                      />
                    </span>
                    <div
                      className="triviapara"
                      dangerouslySetInnerHTML={{
                        __html: imageObjects[ContentIndex].trivia,
                      }}
                    />
                    {/* {imageObjects[ContentIndex].trivia} */}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="minimap">
            <div className="indicator"></div>
            <div className="items">
              {imageObjects.map((item, i) => (
                <div key={i} className="item">
                  <Image
                    width={1000}
                    height={1000}
                    className="js-img js-bg u-br p-cate__tmb-img is-loaded"
                    src={item.url}
                    alt=""
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        <section data-section-container="true" className="FooterCntr">
          <div className="FooterCntrInner">
            <div className="FooterCntrWrap">
              <div className="FooterCntrImageWrapper">
                <div className="footerTextCntr">
                  <h1 className="">Thank you</h1>
                  <p className="">
                    Thank you for celebrating the power and influence of women
                    in entertainment with us. We hope this inspires
                    conversations, recognition, and change. If this list
                    resonates with you, share it and celebrate these incredible
                    women with the world!
                  </p>
                </div>
              </div>

              <div className="sub-footer-micro">
                <div className="container sub-footer_innercntr sub-footer_innercntrft-pd">
                  <div className="sub-footer_innercntrrow">
                    <div className="sub-footer_innercntrcol-md-10">
                      <div className="sub-footer_innercntrft-end-sec">
                        <div className="sub-footer_innercntrmob-view">
                          <div className="sub-footer_innercntrmob-viewcntr">
                            <div className="sub-footer_innercntrft-end-img">
                              <Image
                                alt="RP - Sanjiv Goenka Group Logo"
                                loading="lazy"
                                decoding="async"
                                data-nimg="fill"
                                width={1000}
                                height={1000}
                                sizes="100vw"
                                src={rpsglogo}
                              />
                            </div>
                            <div className="sub-footerhr-mob" />
                            <div className="sub-footerft-end-logo">
                              <Image
                                alt="RPSG MEDIA"
                                loading="lazy"
                                decoding="async"
                                data-nimg="fill"
                                width={1000}
                                height={1000}
                                sizes="100vw"
                                src={rpsgmediaimage}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="sub-footer_innercntrdesk-view">
                          <div className="desktop-viewft-end-img">
                            <Image
                              alt="RP - Sanjiv Goenka Group Logo"
                              loading="lazy"
                              decoding="async"
                              data-nimg="fill"
                              width={1000}
                              height={1000}
                              sizes="100vw"
                              src={rpsglogo}
                            />
                          </div>
                        </div>
                        <div className="linevr" />
                        <div className="desktop-viewft-end-content">
                          <p>
                            The Hollywood Reporter India is published by RP -
                            Sanjiv Goenka Group under license from The Hollywood
                            Reporter, LLC, a subsidiary of Penske Media
                            Corporation.
                          </p>
                        </div>
                        <div className="linevr"></div>
                        <div className="sub-footerft-end-logo sub-footer_innercntrdesk-view">
                          <Image
                            alt="RPSG MEDIA"
                            loading="lazy"
                            decoding="async"
                            data-nimg="fill"
                            width={1000}
                            height={1000}
                            sizes="100vw"
                            src={rpsgmediaimage}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="col-md-2 d-flex align-items-center justify-content-end"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default WomenInEntertainment;

export async function getStaticProps() {
  const meta = {
    title: "Women in Entertainment Power List 2025 | Women’s Day | THR India",
    description:
      "Celebrate Women's Day with Hollywood Reporter India’s Power List 2025, honoring the most influential women shaping the entertainment industry today.",
    keywords: [],
    og: {
      title: "Women in Entertainment Power List 2025 | Women’s Day | THR India",
      description:
        "Celebrate Women's Day with Hollywood Reporter India’s Power List 2025, honoring the most influential women shaping the entertainment industry today.",
      image: Const.ClientLink + "/women/assets/THR Anupama Thumbnail.jpg",
    },
    twitter: {
      title: "Women in Entertainment Power List 2025 | Women’s Day | THR India",
      description:
        "Celebrate Women's Day with Hollywood Reporter India’s Power List 2025, honoring the most influential women shaping the entertainment industry today.",
      image: Const.ClientLink + "/women/assets/THR Anupama Thumbnail.jpg",
    },
    author: "THR INDIA",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
