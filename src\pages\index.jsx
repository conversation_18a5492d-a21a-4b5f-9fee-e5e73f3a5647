import React, { Suspense } from "react";
import Layout from "@/components/layout/Layout";
import SeoHeader from "@/components/seo/SeoHeader";
import OrganizationSchema from "@/components/seo/OrganizationSchema";
import Hero from "@/components/home/<USER>";
import TopStory from "@/components/home/<USER>";
import HeatVision from "@/components/home/<USER>";
import LiveFeed from "@/components/home/<USER>";
import ReadNow from "@/components/home/<USER>";
import Reviews from "@/components/home/<USER>";
import AwardsSeason from "@/components/home/<USER>";
import FeaturedVideos from "@/components/home/<USER>";
import MostPopular from "@/components/home/<USER>";
import LifeStyle from "@/components/home/<USER>";
import FeaturedPodcasts from "@/components/home/<USER>";
import HighlightMagazine from "@/components/home/<USER>";
// import WrappedBanner2024 from "@/components/wrapped/WrappedBanner2024";
import { getFlaggedPage } from "@/pages/api/HomeApi";
import { Ads } from "@/components/ads/Ads";
// import LeaderboardAd from "@/components/ads/LeaderboardAd";
// import TakeoverAd from "@/components/ads/TakeoverAd";

// Homepage
const Home = ({ meta, data }) => {
  const heroData = data[0]?.stories?.length > 0 ? data[0]?.stories[0] : {};
  const top_four_story =
    data[0]?.stories?.length > 1 ? data[0]?.stories.slice(1) : [];

  return (
    <Layout>
      <SeoHeader meta={meta} />
      <OrganizationSchema />
      {/* <TakeoverAd src="/video-ads/billboard-ads-video.gif" /> */}
      <Suspense fallback={"...Loading"}>
        <Hero data={heroData} />
      </Suspense>
      <Ads
        id="div-gpt-ad-homepage-top"
        adUnits={[
          {
            adUnit: "/23290324739/THRI-Desktop-Top-970",
            sizes: [[970, 90]],
            sizeMapping: [
              {
                viewport: [0, 0],
                sizes: [[970, 90]],
              },
            ],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Top-320",
            sizes: [[320, 50]],
            sizeMapping: [
              {
                viewport: [1023, 0],
                sizes: [[320, 50]],
              },
            ],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Top-320",
            sizes: [[320, 50]],
            sizeMapping: [
              {
                viewport: [767, 0],
                sizes: [[320, 50]],
              },
            ],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
        targeting={{
          section: ["home"],
          "sub-section": [null],
        }}
      />
      {/* <LeaderboardAd
        adUnits={[
          {
            adImagePath: "/video-ads/super-leaderboard-video.mp4",
            adSize: [970, 90],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            adImagePath: "/video-ads/super-leaderboard-video.mp4",
            adSize: [728, 90],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            adImagePath: "/video-ads/medium-rectange-video.mp4",
            adSize: [300, 250],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
      /> */}
      <Suspense fallback={"...Loading"}>
        <ReadNow data={top_four_story} />
      </Suspense>

      {/* <WrappedBanner2024 /> */}

      <Suspense fallback={"...Loading"}>
        <Reviews
          reviews={data[1]?.stories ?? []}
          featured_voices={data[2]?.stories ?? []}
          reviewSeeAll={data[1]?.seeAll ?? "/reviews"}
          featuredVoicesSeeAll={data[2]?.seeAll ?? ""}
        />
      </Suspense>
      <Ads
        id="div-gpt-ad-homepage-middle"
        adUnits={[
          {
            adUnit: "/23290324739/THRI-Desktop-Middle-300",
            sizes: [[300, 350]],
            sizeMapping: [
              {
                viewport: [0, 0],
                sizes: [[300, 350]],
              },
            ],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Middle-300",
            sizes: [[300, 250]],
            sizeMapping: [
              {
                viewport: [1023, 0],
                sizes: [[300, 250]],
              },
            ],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Middle-300",
            sizes: [[300, 250]],
            sizeMapping: [
              {
                viewport: [767, 0],
                sizes: [[300, 250]],
              },
            ],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
        targeting={{
          section: ["home"],
          "sub-section": [null],
        }}
      />
      {/* <DynamicAd
        adUnits={[
          {
            divId: `div-gpt-ad-homepage-${generateRandomNumber()}`,
            adUnitPath: "/23290324739/THRI-Desktop-Middle-300",
            adSize: [300, 250],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            divId: `div-gpt-ad-homepage-${generateRandomNumber()}`,
            adUnitPath: "/23290324739/THRI-Mobile-Middle-300",
            adSize: [300, 250],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            divId: `div-gpt-ad-homepage-${generateRandomNumber()}`,
            adUnitPath: "/23290324739/THRI-Mobile-Middle-300",
            adSize: [300, 250],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
        targeting={{
          section: ["home"],
          "sub-section": [null],
        }}
      /> */}

      <Suspense fallback={"...Loading"}>
        <HeatVision
          data={data[3]?.stories ?? []}
          seeAll={data[3]?.seeAll ?? ""}
        />
      </Suspense>

      <Suspense fallback={"...Loading"}>
        <MostPopular
          data={data[4]?.stories ?? []}
          seeAll={data[4]?.seeAll ?? ""}
        />
      </Suspense>

      <Ads
        id="div-gpt-ad-homepage-bottom"
        adUnits={[
          {
            adUnit: "/23290324739/THRI-Desktop-Bottom-300",
            sizes: [[300, 350]],
            sizeMapping: [
              {
                viewport: [0, 0],
                sizes: [[300, 350]],
              },
            ],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Bottom-300",
            sizes: [[300, 250]],
            sizeMapping: [
              {
                viewport: [1023, 0],
                sizes: [[300, 250]],
              },
            ],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            adUnit: "/23290324739/THRI-Mobile-Bottom-300",
            sizes: [[300, 250]],
            sizeMapping: [
              {
                viewport: [767, 0],
                sizes: [[300, 250]],
              },
            ],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
        targeting={{
          section: ["home"],
          "sub-section": [null],
        }}
      />

      <Suspense fallback={"...Loading"}>
        <TopStory
          showhead={1}
          data={data[5]?.stories ?? []}
          seeAll={data[5]?.seeAll ?? "/thr-video"}
        />
      </Suspense>

      <Suspense fallback={"...Loading"}>
        <LifeStyle
          data={data[6]?.stories ?? []}
          seeAll={data[6]?.seeAll ?? "/lifestyle"}
        />
      </Suspense>

      <Suspense fallback={"...Loading"}>
        <AwardsSeason
          title={data[7]?.title ?? "Global"}
          description={
            data[7]?.description ??
            "Stay updated with cinema happenings across the world."
          }
          data={data[7]?.stories ?? []}
          seeAll={data[7]?.seeAll ?? "/global"}
        />
      </Suspense>

      {/* <Suspense fallback={"...Loading"}>
        <FeaturedVideos
          data={data[sections[5]]?.data ?? []}
          seeAll={data[sections[5]]?.seeAll ?? ""}
        />
      </Suspense> */}

      {/* <LeaderboardAd
        adUnits={[
          {
            adImagePath: "/ad2.jpg",
            adSize: [970, 250],
            minWidth: 1024,
            maxWidth: Infinity,
          },
          {
            adImagePath: "/ad2.jpg",
            adSize: [970, 250],
            minWidth: 768,
            maxWidth: 1023,
          },
          {
            adImagePath: "/leaderboard-ad.png",
            adSize: [300, 250],
            minWidth: 0,
            maxWidth: 767,
          },
        ]}
      /> */}

      <Suspense fallback={"...Loading"}>
        <FeaturedPodcasts
          data={data[8]?.stories ?? []}
          seeAll={data[8]?.seeAll ?? ""}
        />
      </Suspense>

      {/* <Suspense fallback={"...Loading"}>
        <LiveFeed
          data={data[sections[10]]?.data ?? []}
          seeAll={data[sections[10]]?.seeAll ?? ""}
        />
      </Suspense> */}

      {/* <Suspense fallback={"...Loading"}>
        <HighlightMagazine data={data[sections[9]]?.data ?? []} />
      </Suspense> */}
    </Layout>
  );
};

export default Home;

export async function getServerSideProps() {
  try {
    const response = await getFlaggedPage("/");
    if (response.status === "success") {
      const { sections, meta } = response?.data ?? {};
      return {
        props: {
          data: sections && sections.length > 0 ? sections : [],
          meta: meta ?? {},
        },
      };
    }
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        data: [],
        meta: {
          title: "The Hollywood Reporter",
          description: "Latest news from The Hollywood Reporter.",
          keywords:
            "Movie news, TV news, awards news, lifestyle news, business news",
          author: "THR",
        },
      },
    };
  }
}
