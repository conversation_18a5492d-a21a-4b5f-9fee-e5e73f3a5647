import React, { useRef } from "react";
import Link from "next/link";

const data = [
  {
    thumbnail: "/thr1.jpg",
    link: "/video1.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thr2.png",
    link: "/video2.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thumb2.png",
    link: "/video3.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thr3.jpg",
    link: "/video4.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thr4.jpg",
    link: "/video1.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thr4.jpg",
    link: "/video2.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thumb2.png",
    link: "/video3.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
  {
    thumbnail: "/thumb2.png",
    link: "/video4.mp4",
    logo: "/videologo.jpg",
    title: "Songs thats boost you into Good man and you can explore many more",
    author: "ComicVerse",
    views: "222k",
    publishDate: "1 days ago",
    timestamp: "25:00",
  },
];

const Suggested = () => {
  const videoRefs = useRef([]);

  const handleMouseEnter = (index) => {
    const video = videoRefs.current[index];
    if (video && video.play) {
      video.play();
    }
  };

  const handleMouseLeave = (index) => {
    const video = videoRefs.current[index];
    if (video && video.pause) {
      video.pause();
      video.currentTime = 0;
      video.load();
    }
  };
  
  return (
    <section id="suggested-videos">
      <div className="container">
        <div className="row">
          <div className="col-md-12 flex-spacebtn">
            <h2>Suggested for you</h2>
            <div>
              <Link href={"/thrtv"} className="btn btn-outline out-btn">
                <div className="middle-btn"></div>
                SEE ALL
              </Link>
            </div>
          </div>
        </div>
        <div className="row scrollable-div">
          {data.map((item, i) => {
            return (
              <>
                <div className="col-md-3 rn-card" key={`top-videos-${i}`}>
                  <div className="card-wrapper">
                    <div
                      className="feature-box"
                      onMouseEnter={() => handleMouseEnter(i)}
                      onMouseLeave={() => handleMouseLeave(i)}
                    >
                      <div className="video-timestamp">{item.timestamp}</div>
                      <video
                        ref={(el) => (videoRefs.current[i] = el)}
                        poster={item.thumbnail}
                        src={item.link}
                        loop
                        muted
                        playsInline
                      />
                    </div>
                    <div className="content-sec">
                      <h3 className="card-title">{item.title}</h3>
                      <span className="view-timeline">{`${item.views} views . ${item.publishDate}`}</span>
                    </div>
                  </div>
                </div>
              </>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Suggested;
