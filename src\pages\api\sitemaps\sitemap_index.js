import { Const } from "@/utils/Constants";
import { convertToISTISOString } from "@/utils/Util";

export default async function handler(req, res) {
  res.setHeader("Content-Type", "application/xml");
  const sitemaps = [
    {
      loc: `${Const.ClientLink}/post-sitemap.xml`,
      lastmod: "2024-09-26T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/video-sitemap.xml`,
      lastmod: "2024-10-29T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/tag-sitemap.xml`,
      lastmod: "2024-09-26T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/category-sitemap.xml`,
      lastmod: "2024-09-26T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/video-category-sitemap.xml`,
      lastmod: "2024-10-29T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/author-sitemap.xml`,
      lastmod: "2024-09-26T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/page-sitemap.xml`,
      lastmod: "2024-09-26T11:32:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/google-news-sitemap.xml`,
      lastmod: "2024-12-04T16:10:33.054Z",
    },
    {
      loc: `${Const.ClientLink}/gallery-sitemap.xml`,
      lastmod: "2025-05-30T10:37:03.443Z",
    },
  ];

  const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
      <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${sitemaps
          .map(
            (sitemap) => `
              <sitemap>
                <loc>${sitemap?.loc || ""}</loc>
                <lastmod>${convertToISTISOString(sitemap?.lastmod || "")}</lastmod>
              </sitemap>`
          )
          .join("")}
      </sitemapindex>`;

  // Send the response
  res.status(200).send(sitemapIndex);
}
