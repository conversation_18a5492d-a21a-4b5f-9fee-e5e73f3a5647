import React from "react";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";

const EditorialStaff = ({ meta }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div id="content-wrapper" className="lrv-a-wrapper lrv-u-margin-tb-2">
        <div className="lrv-a-grid a-cols3@desktop u-grid-gap-3@desktop-xl">
          <div className="a-span2@desktop">
            <h1 className="lrv-u-font-family-secondary lrv-u-font-size-50 lrv-u-line-height-small lrv-u-font-weight-normal">
              Editorial-Staff
            </h1>
            <div className="a-content lrv-u-line-height-copy lrv-u-font-family-body lrv-u-font-size-16 lrv-u-font-size-18@desktop">
              <div>
                <strong
                  className="contact-subhead"
                  style={{ color: "#d92128", marginTop: "15px" }}
                >
                  The Hollywood Reporter India
                </strong>
                <br />
                {/* <div>
                <p className="contact-p" style={{margin:'0'}}>Business Media Pvt Ltd, Thapar House, Central </p>
                <p className="contact-p" style={{margin:'0'}}>Wing, 3rd floor, Janpath Lane, New Delhi - 110001.</p>
                <p className="contact-p" style={{margin:'0', }}>Mail: <a style={{color:'rgb(217,  33,  40)'}} href="mailto:<EMAIL>"><EMAIL></a></p>
                <p className="contact-p">Phone: +91-11-23486700</p>
                </div> */}
              </div>
              {/* <div>
                <a href="mailto:<EMAIL>">
                  <strong
                    className="contact-subhead"
                    style={{ color: "#d92128", marginTop: "15px" }}
                  >
                    Advertisement
                  </strong>
                </a>
                <br />
                <p className="contact-p">
                  Click the link above to talk to us about reaching The
                  Hollywood Reporter India's large, influential and informed
                  audience.
                </p>
              </div>
              <div>
                <a href="mailto:<EMAIL>">
                  <strong
                    className="contact-subhead"
                    style={{ color: "#d92128", marginTop: "15px" }}
                  >
                    Contact the Editor
                  </strong>
                </a>
                <br />
                <p className="contact-p">
                  The Hollywood Reporter India editorial team welcomes feedback
                  from our online readers. Please use the email link above.
                  While we can’t respond to every email, we read everything. If
                  you’d like to submit a letter to the editor, please include
                  your full name, city, and phone number. Please note that we do
                  not provide contact information for individuals.
                </p>
              </div> */}
            </div>
          </div>

          <aside className="lrv-a-grid-item lrv-a-space-children-vertical lrv-a-space-children--2 u-margin-b-2@mobile-max">
            {/* You can add content here for the aside */}
          </aside>
        </div>
      </div>
    </Layout>
  );
};

export default EditorialStaff;

export async function getStaticProps() {
  const meta = {
    title: "Editorial Staff | The Hollywood Reporter India",
    description:
      "Meet The Hollywood Reporter India editorial team—dedicated professionals bringing you the latest entertainment news, reviews, and exclusive Bollywood insights.",
    keywords: [],
    author: "THR INDIA",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
