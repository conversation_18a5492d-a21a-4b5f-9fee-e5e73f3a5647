import React, { useEffect } from "react";

const LeaderboardAd = () => {
 
  useEffect(() => {
    if (window.googletag && googletag.apiReady) { 

      googletag.cmd.push(function() {
        googletag.defineSlot('/23172162065/RPSG_THR_Desktop/RPSG_THR_HalfPage', [300, 600], 'div-gpt-ad-1725617854197-0').addService(googletag.pubads());
    

        googletag.pubads().enableSingleRequest();
        googletag.enableServices();
        googletag.display("div-gpt-ad-1725617854197-0");
      });
    }
  }, []);

  return (
    <>
    <div className="ad-parent-halfpage">
      <div
        id={"div-gpt-ad-1725617854197-0"}
        style={{ minWidth: "300px", minHeight: "600px" }}
      ></div>
      </div>
    </>
  );
};

export default LeaderboardAd;
