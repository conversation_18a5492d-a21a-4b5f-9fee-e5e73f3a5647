import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Heading from "@/components/result/Heading";
import MainSection from "@/components/result/MainSection";
import Layout from "@/components/layout/Layout2";
import Sort from "@/components/result/Sort";
import { getResults, getFilters } from "@/pages/api/ResultApi";
import { capitalizeFirstLetter, convertSlugOrTitle } from "@/utils/Util";

const defaultFilter = {
  sortBy: -1,
  dateRange: 0,
  subcategoryIds: [],
  tagIds: [],
  writerIds: [],
};
const Result = ({
  meta,
  data = [],
  totalCounts = 0,
  query = "",
  pageNo = 1,
  filterData = {},
  resultsLimit = 10,
}) => {
  const router = useRouter();
  const isInitialMount = useRef(true);
  const [text, setText] = useState(query);
  const [newFilterData, setNewFilterData] = useState(filterData);
  const [newTotalCounts, setNewTotalCounts] = useState(totalCounts);
  const [newData, setNewData] = useState(data);
  const [selectedFilter, setSelectedFilter] = useState(defaultFilter);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      try {
        const queryText = convertSlugOrTitle(text);
        const [results, filters] = await Promise.all([
          getResults(queryText, pageNo - 1, resultsLimit, selectedFilter),
          getFilters(queryText, selectedFilter),
        ]);

        setNewData(results?.data ?? []);
        setNewTotalCounts(results?.totalCounts ?? 0);
        setNewFilterData(filters?.data ?? []);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [pageNo, selectedFilter]);

  const handleSearch = (pageIndex = 1) => {
    const querySlug = convertSlugOrTitle(text, false);
    if (!querySlug) {
      router.push("/result");
      return;
    }
    router.push(`/result/${querySlug}/${pageIndex}`);
  };

  const isPrevious = pageNo > 1;
  const isNext = pageNo * resultsLimit < newTotalCounts;

  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <section id="all-tab" className="result-page-main-div">
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <Heading
                title={text}
                resultText={`Showing results ${
                  newTotalCounts === 0 ? 0 : (pageNo - 1) * resultsLimit + 1
                } - ${Math.min(
                  pageNo * resultsLimit,
                  newTotalCounts
                )} of ${newTotalCounts} for`}
                query={convertSlugOrTitle(query, true)}
                pageNo={pageNo}
                setText={setText}
                handleSearch={handleSearch}
              />
            </div>
          </div>
          <div className="row">
            <div className="col-md-4 border-b sort-main-tag">
              <Sort
                filters={newFilterData}
                defaultFilter={defaultFilter}
                selectedFilter={selectedFilter}
                setSelectedFilter={setSelectedFilter}
              />
            </div>
            <div className="col-md-12 ln-cont mainsection-tag">
              <MainSection
                data={newData}
                pageNo={pageNo}
                defaultFilter={defaultFilter}
                isPrevious={isPrevious}
                isNext={isNext}
                isLoading={isLoading}
                setSelectedFilter={setSelectedFilter}
                handleSearch={handleSearch}
              />
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Result;

// Server-side data fetching
export async function getServerSideProps(context) {
  const resultsLimit = 10;
  const [query, pageNoRaw] = context.params.slug;
  const pageNo = parseInt(pageNoRaw, 10) ?? 1;
  const pageIndex = pageNo - 1;
  if (!query || !pageNoRaw) {
    return {
      notFound: true,
    };
  }
  const queryText = convertSlugOrTitle(query);
  const capitalizeQueryText = capitalizeFirstLetter(queryText);
  try {
    // Fetch results and filters in parallel for better performance
    const [response, filterData] = await Promise.all([
      getResults(queryText, pageIndex, resultsLimit, { sortBy: -1 }),
      getFilters(queryText, {}),
    ]);

    const metaData = {
      title: `${capitalizeQueryText} Latest News, Updates and Trends | THR India`,
      description: `Latest ${capitalizeQueryText} News, updates on Bollywood, Telegu, Tamil and Kannada cinema, entertainment news, celebrity interviews, film reviews, and industry trends.`,
      og: {
        title: `${capitalizeQueryText} Latest News, Updates and Trends | THR India`,
        description: `Latest ${capitalizeQueryText} News, updates on Bollywood, Telegu, Tamil and Kannada cinema, entertainment news, celebrity interviews, film reviews, and industry trends.`,
      },
      twitter: {
        title: `${capitalizeQueryText} Latest News, Updates and Trends | THR India`,
        description: `Latest ${capitalizeQueryText} News, updates on Bollywood, Telegu, Tamil and Kannada cinema, entertainment news, celebrity interviews, film reviews, and industry trends.`,
      },
      keywords: capitalizeQueryText,
      robots: "noindex,follow",
    };

    return {
      props: {
        meta: metaData,
        data: response?.data ?? [],
        totalCounts: response?.totalCounts ?? 0,
        query: queryText || "",
        pageNo: pageNo ?? 1,
        filterData: filterData?.data || {},
        resultsLimit,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        meta: {
          title: `The Hollywood Reporter - Movie news, TV news, awards news, lifestyle news, business news and more.`,
          description: `Movie news, TV news, awards news, lifestyle news, business news from The Hollywood Reporter.`,
          og: {
            title: `The Hollywood Reporter - Movie news, TV news, awards news, lifestyle news, business news and more.`,
            description: `Movie news, TV news, awards news, lifestyle news, business news from The Hollywood Reporter.`,
          },
          twitter: {
            title: `The Hollywood Reporter - Movie news, TV news, awards news, lifestyle news, business news and more.`,
            description: `Movie news, TV news, awards news, lifestyle news, business news from The Hollywood Reporter.`,
          },
          keywords:
            "Movie news, TV news, awards news, lifestyle news, business news from THR.",
          robots: "noindex,follow",
        },
        data: [],
        totalCounts: 0,
        query: "",
        pageNo: pageNo ?? 1,
        filterData: {},
        resultsLimit,
      },
    };
  }
}
