import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";
import style from "./Author.module.css";
import Image from "next/image";
import HeroImage from "@/assets/images/category-hero.png";

const Hero = ({ title, coverImg, submenus }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <section id="authorhero" className={style.authorHero}>
      <div className="container-fluid">
        <div className="row">
          <div className="col-md-12 p-0" style={{ zIndex: "-1" }}>
            <div className={style.heroSection}>
              <div className={style.herocrAbs}>
                <div className={style.herocrText}>
                  <h1>{title}</h1>
                </div>
              </div>
              <div className={style.heroImgCont}>
                <div className="pos-rel-full">
                  <Image src={coverImg || HeroImage} alt="cover" fill />
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-12 p-0" id="cat-navtabs">
            <ul className="navs t3">
              {submenus.map((submenu, i) => {
                return (
                  <>
                    <li
                      className={`nav-items ${
                        pathname === submenu.slug ? "active" : ""
                      }`}
                    >
                      <Link href={submenu.slug}>{submenu.name}</Link>
                    </li>
                  </>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
