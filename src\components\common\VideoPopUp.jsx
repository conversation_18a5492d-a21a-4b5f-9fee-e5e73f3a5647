import React, { useEffect, useState } from "react";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { IoMdClose } from "react-icons/io";
import dynamic from "next/dynamic";

const ReactPlayer = dynamic(() => import("react-player"), { ssr: false });

const VideoPopup = ({ open, setOpen, videoarray }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  let goback = () => {
    if (open == 0) {
      setOpen(videoarray.length - 1);
    } else {
      setOpen(open - 1);
    }
  };
  let gonext = () => {
    if (open == videoarray.length - 1) {
      setOpen(0);
    } else {
      setOpen(open + 1);
    }
  };
  return (
    <div
      className="popup"
      style={{
        transform: open == null ? `translateY(-100%)` : "translateY(0%)",
      }}
    >
      <div className="video-cont flex-all">
        {open != null && videoarray[open].src && (
          <>
            { (
              <div className="opdi">
                {isClient && (
                  <ReactPlayer
                    url={videoarray[open].src}
                    width={"100%"}
                    height={"100%"}
                    controls={true}
                  />
                )}
              </div>
            )}
          </>
        )}
      </div>
      <div className="video-exts flex-all">
        <div className="vid-action flex-all t3" onClick={goback}>
          <FaChevronLeft />
        </div>
        <div className="vid-mid flex-all t3" onClick={() => setOpen(null)}>
          <IoMdClose />
        </div>
        <div className="vid-action flex-all t3" onClick={gonext}>
          <FaChevronRight />
        </div>
      </div>
    </div>
  );
};

export default VideoPopup;
