import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Category List
export const getCategory = async (body) => {
  const res = await fetch(Const.Link + `api/tenant/category-list?slug=${body.slug}&limit=${body.limit}&offset=${body.offset}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

export const getSubmenus = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/category-submenus?slug=${slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
}

export const getCategoryLatest = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/category-latest?slug=${slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

export const getCategoryVideos = async (body) => {
  const res = await fetch(Const.Link + `api/tenant/category-video?slug=${body.slug}&shorts=${body.shorts}&limit=${body.limit}&offset=${body.offset}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Category Sitemap
export const getCategorySitemap = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/category-sitemap", new Headers("GET"));
  return ProcessAPI(res);
};

// Get Video Category Sitemap
export const getVideoCategorySitemap = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/video-category-sitemap", new Headers("GET"));
  return ProcessAPI(res);
};
