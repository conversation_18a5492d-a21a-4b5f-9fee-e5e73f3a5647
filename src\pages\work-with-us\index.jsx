import React from "react";
import Layout from "@/components/layout/Layout2";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";

const WorkWithUs = ({ meta }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div id="content-wrapper" className="lrv-a-wrapper lrv-u-margin-tb-2">
        <div className="lrv-a-grid a-cols3@desktop u-grid-gap-3@desktop-xl">
          <div className="a-span2@desktop">
            <h1 className="lrv-u-font-family-secondary lrv-u-font-size-50 lrv-u-line-height-small lrv-u-font-weight-normal">
              Work with us
            </h1>
            <div className="a-content lrv-u-line-height-copy lrv-u-font-family-body lrv-u-font-size-16 lrv-u-font-size-18@desktop">
              <div>
                <p className="contact-p">
                  If you are interested in joining our team, please send us your
                  CV and a cover letter describing your experience, skills and
                  motivations. Your application will be carefully evaluated and
                  we will contact you for an interview if your profile matches
                  our needs.
                </p>
                <p className="contact-p">
                  If you would like more information about our available
                  vacancies, please do not hesitate to contact us. We are always
                  looking for motivated and ambitious talents.
                </p>
                <a href="mailto:<EMAIL>">
                  <span className="contact-p">
                    Email id:{" "}
                    <span style={{ color: "#3475de" }}><EMAIL></span>
                  </span>
                </a>
              </div>
            </div>
          </div>

          <aside className="lrv-a-grid-item lrv-a-space-children-vertical lrv-a-space-children--2 u-margin-b-2@mobile-max">
            {/* You can add content here for the aside */}
          </aside>
        </div>
      </div>
    </Layout>
  );
};

export default WorkWithUs;

export async function getStaticProps() {
  const meta = {
    title: "Careers & Job Listings | The Hollywood Reporter India",
    description:
      "Join The Hollywood Reporter India team! Explore exciting career opportunities and job listings in entertainment journalism, marketing, and more. Apply today!",
    keywords: [],
    author: "THR INDIA",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
