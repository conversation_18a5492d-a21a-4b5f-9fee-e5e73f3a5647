import React from 'react';
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { useSwiper } from 'swiper/react';

const SlideNavButton = ({prev, next}) => {
    // const swiper = useSwiper();
  return (
    <div className="swiper-nav">
      <div className="d-flex btn-gap">
        <button className={`btn btn-swiper ${prev}`}>
          <FiChevronLeft className="icon" />
        </button>
        <button className={`btn btn-swiper ${next}`}>
          <FiChevronRight className="icon" />
        </button>
      </div>
    </div>
  )
}

export default SlideNavButton