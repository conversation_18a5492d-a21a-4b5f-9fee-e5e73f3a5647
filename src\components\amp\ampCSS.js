export const webStoryDetailCSS = `
				/* AMP Web Stories Font Configuration */

				@font-face {
					font-family: "NeueHaasDisplayBold";
					src: url(/Assets/NeueHaasDisplayBold.ttf);
					font-display: swap;
				}
				@font-face {
					font-family: "Bitter";
					src: url(/Assets/Bitter-VariableFont_wght.ttf);
					font-display: swap;
				}
                body{
                    display: block
                }
                .logo-gradient-overlay {
                width: 100%;
                height: 50px;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2) 5%, transparent 20%);
                position: absolute;
                top: 0;
                left: 0;
                }
				.brand-logo {
					position: absolute;
					top: -2rem;
					left: 15px;
					color: white;
					text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
					z-index: 10;
                   
				}
				
				.back-button {
					position: absolute;
					top: 20px;
					right: 20px;
					z-index: 10;
				}

				.back-link {
					color: white;
					text-decoration: none;
					background: rgba(0, 0, 0, 0.5);
					padding: 8px 12px;
					border-radius: 20px;
					font-size: 14px;
					text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
					transition: background 0.3s ease;
					border: none;
					cursor: pointer;
					font-family: "Bitter", serif;
				}
        .next-story-preview{
          padding-bottom: 4rem;
        }
        .next-story-preview h2{
          text-align: center;
          font-size: 24px;
          letter-spacing: 1px;
					font-family: "NeueHaasDisplayBold", sans-serif;
					font-weight: 700;
        }
				.back-link:hover {
					background: rgba(0, 0, 0, 0.7);
				}
				.story-content {
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					padding: 15px;
					background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 1) 100%);
				}
				.story-text {
					color: white;
					text-align: left;
				}
				/* Headlines use primary font (NeueHaasDisplayBold) */
				.story-text h1 {
					font-size: 24px;
					font-family: "NeueHaasDisplayBold", sans-serif;
					margin-bottom: 10px;
					font-weight: 700;
          letter-spacing: 1px;
          text-align: center;
					line-height: 1.1;
				}
				/* Body text uses secondary font (Bitter) */
				.story-text div {
					font-family: "Bitter", serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 400;
          text-align:center;
				}
				.story-text p {
					font-family: "Bitter", serif;
					font-size: 16px;
					line-height: 1.3;
					font-weight: 400;
          text-align:center;
				}
        .story-text p a{
        color: #fff;
        }
				.story-text small {
					display: block;
					margin-top: 8px;
					opacity: 0.8;
					font-family: "Bitter", serif;
					font-size: 12px;
					font-weight: 300;
          text-align:center;
				}
                [template=vertical]{
                align-content: end;
                }

        .next-story-preview {
          color: #fff
        }
				/* AMP story navigation buttons - removed :global() as it's not valid AMP CSS */
				.amphtml-story-button-container {
					display: block;
					visibility: visible;
					opacity: 1;
				}

				.amphtml-story-button-move {
					display: block;
					visibility: visible;
					opacity: 1;
				}

				@media (max-width: 768px) {
					.amphtml-story-button-container {
						display: block;
						visibility: visible;
						opacity: 1;
					}
				}
			
			`;
