import React from "react";
import Link from "next/link";
import Image from "next/image";
import style from "./Home.module.css";
import { formatDateAndTime, getAuthorText } from "@/utils/Util";
import Button from "../common/Button";

const LifeStyle = ({ data, seeAll }) => {
  return (
    <>
      {data && data.length > 0 && (
        <>
          <section id="lifestyle" className={`${style.lifeStyle}`}>
            <div className="container" style={{ marginBottom: "20px" }}>
              <div className="row" style={{ marginBottom: "20px" }}>
                <div className="col-md-12 d-flex align-items-center justify-content-between">
                  <h2 style={{ marginBottom: "0" }}>Lifestyle</h2>
                  {/* <Link href={seeAll} className="btn btn-outline out-btn">
                    <div className="middle-btn"></div>
                    SEE ALL
                  </Link> */}
                  <Button href={seeAll}>SEE ALL</Button>
                </div>
              </div>
              <div className="row scrollable-div">
                {data.map((item, i) => {
                  return (
                    <>
                      <div className="col-md-3 rn-card" key={`lifestyle-${i}`}>
                        <Link href={item?.slug ?? "#"}>
                          <div className="card-wrapper">
                            <div className="feature-box">
                              <Image
                                src={
                                  item?.croppedImg
                                    ? item?.croppedImg
                                    : item?.coverImg
                                    ? item?.coverImg
                                    : ""
                                }
                                alt={item?.altName ?? ""}
                                fill
                              />
                            </div>
                            <div className="content-sec">
                              <div className="d-flex flex-columnn align-items-center gap-3">
                                <span className="category mb-1">
                                  {item?.category ?? ""}
                                </span>
                                <span className="timeline mb-1">
                                  {formatDateAndTime(item?.timestamp ?? "")}
                                </span>
                              </div>
                              <h3 className="card-title">{item?.title}</h3>
                              <p className="card-subtitle">
                                {item?.excerpt?.replace(/(<([^>]+)>)/gi, "") ??
                                  ""}
                              </p>
                              <span className="author">
                                {getAuthorText(
                                  "BY",
                                  item?.author,
                                  item?.contributor
                                )}
                              </span>
                            </div>
                          </div>
                        </Link>
                      </div>
                    </>
                  );
                })}
              </div>
            </div>
          </section>
        </>
      )}
    </>
  );
};

export default LifeStyle;
