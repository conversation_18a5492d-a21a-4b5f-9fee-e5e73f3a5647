import React, { useEffect, useState } from "react";
import Button from "../common/Button";
import dynamic from "next/dynamic";
const ReactPlayer = dynamic(() => import("react-player"), { ssr: false });

const Trailar = ({ data }) => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  if (data.length === 0) {
    return null;
  }
  return (
    <div className="chart-vedio-wrapper">
      <div className="container">
        <div className="chart--vedio--cntr">
          <div className="youtube--vedio--cntr">
            {isClient && (
              <ReactPlayer
                url={data[0]?.src}
                width={"100%"}
                height={"100%"}
                controls={true}
              />
            )}
          </div>
          <div className="chart--text-cntr">
            <h2 className="chart--video--heading">{data[0]?.title ?? ""}</h2>
            <p>{data[0]?.excerpt ?? ""}</p>
            <div
              className="chart--video--btn"
              style={{ display: "flex", justifyContent: "flex-start" }}
            >
              <Button href={data[0]?.slug ?? "#"}>Watch Now</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Trailar;
