import React, { useEffect } from "react";
import style from "./Home.module.css";
import Image from "next/image";

import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import Link from "next/link";
import { getAuthorText } from "@/utils/Util";
const Hero = ({ data }) => {
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".hcr-img-cont", {
      // y: window.innerWidth < 426 ? "5vh" : "15vh",
      scrollTrigger: {
        trigger: ".home-carousal",
        start: "top 300px",
        scrub: 1,
        end: "bottom top",
        // markers: true,
      },
    });
  }, []);
  return (
    <section id="herosection" className={style.herosection}>
      <div className="container-fluid">
        <div className="row">
          <div className="col-md-12 p-0">
            <Link href={data?.slug ?? "#"}>
              <div className="home-carousal">
                <div className="home-cr-abs">
                  <div className="hcr-text d-xxl-block d-xl-block d-lg-block d-md-block d-none">
                    <h1 className="hcr-title">{data?.title ?? ""}</h1>
                    {/* <div className="hcr-p">
                      Janhvi Kapoor, continues to enthrall her fans with her
                      impeccable fashion choices.
                    </div> */}
                    <span className="hcr-author">
                      {getAuthorText("BY", data?.author, data?.contributor)}
                    </span>
                  </div>
                </div>
                <div className="hcr-img-cont">
                  <div className="pos-rel-full">
                    <Image
                      src={data?.coverImg ?? ""}
                      alt={data?.altName ?? "top story"}
                      fill
                    />
                  </div>
                </div>
              </div>
            </Link>
          </div>
          <div className="col-sm-12 d-block d-md-none">
            <Link href={data?.slug ?? "#"} className="mob-hero-text">
              <h1 className="hcr-title">{data?.title ?? ""}</h1>
              {/* <div className="hcr-p">
                      Janhvi Kapoor, continues to enthrall her fans with her
                      impeccable fashion choices.
                    </div> */}
              <span className="hcr-author">
                {getAuthorText("BY", data?.author, data?.contributor)}
              </span>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
