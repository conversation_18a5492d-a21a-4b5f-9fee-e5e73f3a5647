import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { gsap } from "gsap/dist/gsap";
import { IoIosArrowBack } from "react-icons/io";
import { IoIosArrowForward } from "react-icons/io";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import { getAuthorText } from "@/utils/Util";
import Loader from "@/components/common/Loader";

const MainSection = ({
  data = [],
  pageNo = 1,
  isPrevious,
  isNext,
  handleSearch,
  isLoading,
}) => {
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent",
        start: "top 60%",
        // markers: true,
      },
    });
  }, []);

  if (data && data.length === 0) return;
  return (
    <>
      <div className="row secmt ln-cont">
        <div className="col-md-12">
          {isLoading ? (
            <Loader className={"result-loader-div"} />
          ) : (
            <>
              {data?.map((item) => {
                return (
                  <Link href={item?.slug ?? "#"} className="cat-card">
                    <div className="image">
                      <Image
                        className="imgcover"
                        src={
                          item?.croppedImg
                            ? item?.croppedImg
                            : item?.coverImg
                            ? item?.coverImg
                            : ""
                        }
                        alt={item?.altName ?? ""}
                        fill
                      />
                    </div>
                    <div className="content">
                      <div className="d-flex flex-columnn align-items-center gap-3 mb-2">
                        <span className="category">
                          {item?.subcategory?.name ?? ""}
                        </span>
                        <span className="timeline">{item?.timeline ?? ""}</span>
                      </div>
                      <h2>{item?.title ?? ""}</h2>
                      <p>{item?.excerpt ?? ""}</p>
                      <span className="author">
                        {getAuthorText("BY", item?.writer, item?.contributor)}
                      </span>
                    </div>
                  </Link>
                );
              })}
            </>
          )}
        </div>
        <div className="col-md-12">
          <div className="paginator">
            <div>
              {isPrevious && (
                <p
                  className="paginator-p"
                  onClick={() => handleSearch(pageNo - 1)}
                >
                  <IoIosArrowBack /> Previous
                </p>
              )}
            </div>
            <div>
              {isNext && (
                <p
                  className="paginator-p"
                  onClick={() => handleSearch(pageNo + 1)}
                >
                  Next <IoIosArrowForward />
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MainSection;
