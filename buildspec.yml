version: 0.2

env:
  variables:
    AWS_REGION: "ap-south-1"
    REPOSITORY_URI: "060795917914.dkr.ecr.ap-south-1.amazonaws.com/dev-ecs"
    ECS_CLUSTER_NAME: "Thr-Stage"
    ECS_SERVICE_NAME: "stage-dev-ec2"
    ECS_CONTAINER_NAME: "dev-ec2"  # 🔹 Update to match the correct container name

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:-latest}

  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -t dev-ecs .
      - docker tag dev-ecs:latest $REPOSITORY_URI:$IMAGE_TAG

  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image to ECR...
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...
      - printf '[{"name":"%s","imageUri":"%s"}]' "$ECS_CONTAINER_NAME" "$REPOSITORY_URI:$IMAGE_TAG" > imagedefinitions.json

artifacts:
  files:
    - imagedefinitions.json
#dev##