import React from "react";
import { usePathname } from "next/navigation";
import AMPTiptapRendered from "@/components/common/AMPTiptapRenderer";

const AMPMainContent = ({ _id, content, note, tag }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  return (
    <div>
      <div className="row">
        <div className="col-md-12">
          <div className={"mainContent"}>
            <AMPTiptapRendered
              _id={_id}
              content={content?.content ?? []}
              router={router}
            />
            {/*{note && (
            <p style={{ textAlign: "left", marginTop: "10px" }}>
              <span
                style={{ color: "rgb(148, 148, 148)", fontStyle: "italic" }}
              >
                {note}
              </span>
            </p>
          )}*/}
          </div>
        </div>
        {/* 
        {tag && tag.length > 0 && (
          <div style={{ width: "100%", padding: "0px 12px" }}>
            <div className={"readMore"}>
              <h3 style={{ fontSize: "14px" }}>READ MORE ABOUT:</h3>
              <div className={"breadcumSec"}>
                <ol className={"breadcrumb"}>
                  {tag.map((item, i) => {
                    return (
                      <>
                        <li className={"breadcrumb-item"} key={`tags-${i}`}>
                          <Link
                            style={{
                              textDecoration: "none",
                              color: "#db242a",
                              fontFamily: "Karla, sans-serif",
                            }}
                            href={item?.slug ?? "#"}
                          >
                            {item?.name ?? ""}
                          </Link>
                        </li>
                      </>
                    );
                  })}
                </ol>
              </div>
            </div>
          </div>
        )} */}
        {/* <div className="col-md-6"></div> */}
      </div>
    </div>
  );
};

export default AMPMainContent;
