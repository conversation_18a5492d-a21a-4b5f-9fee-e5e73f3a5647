import React, { useState } from "react";
import { useRouter } from "next/router";
import style from "./Stories.module.css";
import Image from "next/image";
import Link from "next/link";
import { toast } from "react-hot-toast";
import {
	htmlParser,
	dateFormateWithTimeShort,
	hasHtmlTags,
	dateFormatterWithIST,
	formatDateToLong,
	formatMinutesToDuration,
} from "@/utils/Util";
import { LuCopyPlus } from "react-icons/lu";
import { RiWhatsappFill } from "react-icons/ri";
import { BsTwitterX } from "react-icons/bs";
import { Ads } from "@/components/ads/Ads";
import AuthorPopUp from "./AuthorPopUp";
import MultiAuthorPopUp from "./MultiAuthorPopup";
import { FaFacebookF, FaLink } from "react-icons/fa";
import SponsoredTag from "../common/SponsoredTag";

const Hero = ({
	_id,
	breadcrumbs,
	title,
	description,
	author,
	timeline,
	coverImage,
	caption,
	courtesy,
	contributor,
	altName,
	readTime,
	articleUrl,
	promotional,
}) => {
	const router = useRouter();
	const [open, setOpen] = useState(null);
	const [copy, setCopy] = useState("Copy link");

	const copyToClipboard = () => {
		navigator.clipboard
			.writeText(articleUrl)
			.then(() => {
				// toast.success("Copied to clipboard");
				setCopy("Link copied");
				setTimeout(() => {
					setCopy("Copy link");
				}, 1000);
			})
			.catch((err) => {
				console.log(err);
				toast.error("Cannot copy to clipboard") + err;
			});
	};

	return (
		<>
			<section id="storiesherosec" className={style.storiesHeroSec}>
				<div className="container">
					<div className="row d-flex align-items-center justify-content-start">
						<div className="col-md-8">
							<div className={style.breadcumSec}>
								<ol className={style.breadcrumb}>
									{breadcrumbs &&
										breadcrumbs.length > 0 &&
										breadcrumbs.map((breadcrumb, i) => {
											return (
												<li className={style["breadcrumb-item"]} key={`breadcrumb-${i}`}>
													<Link href={breadcrumb?.slug ?? "#"}>{breadcrumb?.name ?? ""}</Link>
												</li>
											);
										})}
									{promotional && <SponsoredTag customStyle={{ display: "inline" }} />}
								</ol>
							</div>
							<div className={style.contentSec}>
								<h1>{htmlParser(title)}</h1>
								<p>{description ? htmlParser(description) : htmlParser(description)}</p>
								{/* <div className={style.authorSec}>
                  <span className={style.author}>BY {author}</span>
                  <span className={style.timeline}>
                    {dateFormateWithTime(timeline)}
                  </span>
                </div> */}
								{author && author.length > 1 && (
									<>
										<div className={style.author_wrapper}>
											<div className={style.author_right}>
												<div
													style={{
														display: "flex",
														alignItems: "center",
														gap: "10px",
														flexWrap: "wrap",
													}}
												>
													{(author || contributor) && (
														<span className={style.author_name}>
															By{" "}
															{[
																...author.map((item, i) => (
																	<span
																		key={`author-${i}`}
																		onMouseEnter={() => setOpen(i)}
																		// onMouseLeave={() => setOpen(null)} // Optional: Reset on mouse leave
																		style={{ cursor: "pointer" }}
																	>
																		{item?.name}
																	</span>
																)),
																...contributor.map((el, i) => (
																	<span
																		key={`contributor-${i}`}
																		// Optional: Reset on mouse leave
																		style={{ cursor: "pointer" }}
																	>
																		{el}
																	</span>
																)),
															].reduce((prev, curr) => [prev, ", ", curr])}
														</span>
													)}

													<span>
														<LuCopyPlus className={style.copysvg} />
													</span>
												</div>
												{open !== null && (
													<MultiAuthorPopUp index={open} setOpen={setOpen} data={author} />
												)}
											</div>
										</div>
										<span className={style.timeline}>
											LAST UPDATED: {dateFormatterWithIST(timeline)}
										</span>
										<span className={style.timeline} style={{ marginLeft: "8px" }}>
											|
										</span>
										<span className={style.timeline} style={{ marginLeft: "8px" }}>
											{readTime} min read
										</span>
									</>
								)}
								{author && author.length == 1 && (
									<>
										{author.map((item, i) => {
											if (i === 0) {
												return (
													<>
														<div className={style.author_wrapper}>
															<div className={style.author_left}>
																<Image
																	className={style.author_image}
																	width={50}
																	height={50}
																	src={item?.image || "/placeholder/author_placeholder.jpg"}
																	alt={item?.name || "Author Image"}
																/>
															</div>
															<div className={style.author_right}>
																<div
																	style={{
																		display: "flex",
																		alignItems: "center",
																		gap: "10px",
																		flexWrap: "wrap",
																	}}
																>
																	{(author || contributor) && (
																		<span className={style.author_name}>
																			By{" "}
																			{[
																				...author.map((item, i) => (
																					<span
																						key={`author-${i}`}
																						onMouseEnter={() => setOpen(i)}
																						// onMouseLeave={() => setOpen(null)} // Optional: Reset on mouse leave
																						style={{ cursor: "pointer" }}
																					>
																						{item?.name}
																					</span>
																				)),
																				...contributor.map((el, i) => (
																					<span
																						key={`contributor-${i}`}
																						// Optional: Reset on mouse leave
																						style={{ cursor: "pointer" }}
																					>
																						{el}
																					</span>
																				)),
																			].reduce((prev, curr) => [prev, ", ", curr])}
																		</span>
																	)}
																	<span>
																		<LuCopyPlus className={style.copysvg} />
																	</span>
																</div>
																{open !== null && (
																	<AuthorPopUp
																		index={i}
																		setOpen={setOpen}
																		name={item?.name ?? ""}
																		subheading={item?.subheading ?? ""}
																		aboutus={item?.aboutus ?? ""}
																		slug={item?.slug ?? "#"}
																		social={item?.social ?? ""}
																		related={item?.relatedStories ?? []}
																	/>
																)}
																<span className={style.timeline}>
																	LAST UPDATED: {dateFormatterWithIST(timeline)}
																</span>
																<span className={style.timeline} style={{ marginLeft: "8px" }}>
																	|
																</span>
																<span className={style.timeline} style={{ marginLeft: "8px" }}>
																	{readTime} min read
																</span>
															</div>
														</div>
													</>
												);
											} else {
												return (
													<>
														<div
															className={style.author_wrapper}
															onMouseEnter={() => {
																setOpen(i);
															}}
														>
															<div className={style.author_right}>
																<div
																	style={{
																		display: "flex",
																		alignItems: "center",
																		gap: "10px",
																		flexWrap: "wrap",
																	}}
																>
																	<span className={style.author_name}>
																		{author.length > 1 && i === 0 ? "By " : ""}
																		{item?.name ?? ""}
																	</span>
																	<span>
																		<LuCopyPlus className={style.copysvg} />
																	</span>
																</div>
																{open !== null && (
																	<AuthorPopUp
																		index={i}
																		setOpen={setOpen}
																		name={item?.name ?? ""}
																		subheading={item?.subheading ?? ""}
																		aboutus={item?.aboutus ?? ""}
																		slug={item?.slug ?? "#"}
																		social={item?.social ?? ""}
																		related={item?.relatedStories ?? []}
																	/>
																)}
															</div>
														</div>
														<span className={style.timeline}>
															LAST UPDATED: {dateFormatterWithIST(timeline)}
														</span>
														<span className={style.timeline} style={{ marginLeft: "8px" }}>
															|
														</span>
														<span className={style.timeline} style={{ marginLeft: "8px" }}>
															{readTime} min read
														</span>
													</>
												);
											}
										})}
									</>
								)}
							</div>
						</div>
						<div className="col-md-4 d-flex align-items-end justify-content-end">
							<ul className={style.followus}>
								<Link target="_blank" href={`https://wa.me/?text=${articleUrl}`}>
									<li className={style.items}>
										<span className={style.tooltipText}>Share on Whatsapp</span>
										<RiWhatsappFill className={style.icons} />
									</li>
								</Link>
								<Link
									target="_blank"
									href={`https://www.facebook.com/dialog/share?app_id=145634995501895&display=popup&href=${articleUrl}&redirect_uri=${articleUrl}`}
								>
									<li className={style.items}>
										<span className={style.tooltipText}>Share on Facebook</span>

										<FaFacebookF className={style.icons} />
									</li>
								</Link>
								<Link target="_blank" href={`https://twitter.com/intent/tweet?text=${articleUrl}`}>
									<li className={style.items}>
										<span className={style.tooltipText}>Share on X</span>
										<BsTwitterX className={style.icons} />
									</li>
								</Link>
								<li className={style.items} onClick={copyToClipboard}>
									<span className={style.tooltipText}>{copy}</span>
									<FaLink className={style.icons} />
								</li>
							</ul>
						</div>
					</div>
					<div className="row">
						<div className="col-md-12">
							<Ads
								id={`div-gpt-ad-stories-top-${_id}`}
								adUnits={[
									{
										adUnit: "/23290324739/THRI-Desktop-Top-970",
										sizes: [[970, 90]],
										sizeMapping: [
											{
												viewport: [0, 0],
												sizes: [[970, 90]],
											},
										],
										minWidth: 1024,
										maxWidth: Infinity,
									},
									{
										adUnit: "/23290324739/THRI-Mobile-Top-320",
										sizes: [[320, 50]],
										sizeMapping: [
											{
												viewport: [1023, 0],
												sizes: [[320, 50]],
											},
										],
										minWidth: 768,
										maxWidth: 1023,
									},
									{
										adUnit: "/23290324739/THRI-Mobile-Top-320",
										sizes: [[320, 50]],
										sizeMapping: [
											{
												viewport: [767, 0],
												sizes: [[320, 50]],
											},
										],
										minWidth: 0,
										maxWidth: 767,
									},
								]}
								targeting={{
									section: [router?.query?.category || null],
									"sub-section": [router?.query?.subcatgory || null],
								}}
							/>
						</div>
					</div>
				</div>
			</section>
			{coverImage && (
				<>
					<section id="storiesherobanner" className={style.storiesHeroBanner}>
						<div className="container-fluid">
							<div className="row">
								<div className="col-md-12 p-0">
									<div className={style.heroSection}>
										<div className={style.heroImgCont}>
											<div className="pos-rel-full">
												<Image src={coverImage} alt={altName} fill />
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div className="container">
							<div className="row">
								<div className="col-md-12 text-start">
									<div className={style.mainCaption}>
										{caption && (
											<span className={style.caption}>
												{hasHtmlTags(caption) ? htmlParser(caption) : caption}
											</span>
										)}
										{courtesy && (
											<span className={style.courtesy}>
												{hasHtmlTags(courtesy) ? htmlParser(courtesy) : courtesy}
											</span>
										)}
									</div>
								</div>
							</div>
						</div>
					</section>
				</>
			)}
		</>
	);
};

export default Hero;
