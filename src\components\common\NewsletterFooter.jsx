import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BsCheckCircleFill } from "react-icons/bs";
import Link from "next/link";
import { addNewsLetterSubscription } from "@/pages/api/HomeApi";

const NewsletterFooter = () => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  //   const [isSubmitted, setIsSubmitted] = useState(false);
  const [response, setResponse] = useState({ type: "", message: "" });

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email || !/^\S+@\S+\.\S+$/.test(email)) {
      setResponse({
        type: "error",
        message: "Please enter a valid email",
      });
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    try {
      const data = await addNewsLetterSubscription({ email });
      setEmail("");
      setResponse({
        type: "success",
        message: "Thank you for subscribing to our newsletter!",
      });
    } catch (error) {
      setResponse({
        type: "error",
        message: "Something went wrong!",
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => {
        // setIsSubmitted(false);
        setResponse({
          type: "",
          message: "",
        });
      }, 5000);
    }
  };

  return (
    <div className="newsletter-footer">
      <div className="container">
        <div className="newsletter-content">
          <h2>Value delivered straight to your inbox</h2>
          <p>Every quarter, we deliver a newsletter packed with insights.</p>

          <div className="newsletter-features">
            <div className="feature-item">
              <BsCheck className="feature-icon" />
              <span>Exclusive guides</span>
            </div>
            <div className="feature-item">
              <BsCheck className="feature-icon" />
              <span>Trend reviews</span>
            </div>
            <div className="feature-item">
              <BsCheck className="feature-icon" />
              <span>New publications</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="newsletter-form">
            <div className="form-group">
              <input
                type="email"
                placeholder="Your e-mail address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="form-control"
                required
              />
              <button
                type="submit"
                className="btn sign-up-btn"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Signing up..." : "Sign up"}
              </button>
            </div>
            {/* {isSubmitted && (
              <div className="success-message">{response.message}</div>
            )} */}
            <div className="success-message">{response.message}</div>
          </form>

          <div className="newsletter-disclaimer">
            <p style={{ marginBottom: 0 }}>
              By subscribing, you agree to receive emails from us as per our{" "}
              <Link className="under-line" href="/terms">
                terms
              </Link>{" "}
              and{" "}
              <Link className="under-line" href="/privacy-policy">
                privacy policy
              </Link>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewsletterFooter;
