import React, { useState, useEffect, useRef } from "react";
import { menus } from "@/helpers/MenuData";
import { IoSearchOutline } from "react-icons/io5";
import Search from "./Search";
import { FaInstagram } from "react-icons/fa6";
import { SlSocialFacebook } from "react-icons/sl";
import { BsTwitterX } from "react-icons/bs";
import { PiYoutubeLogo } from "react-icons/pi";
import Link from "next/link";
import { IoPlayCircleOutline } from "react-icons/io5";
import Image from "next/image";
import {
  FaChevronLeft,
  FaChevronRight,
  FaFacebookF,
  FaTwitter,
  FaYoutube,
} from "react-icons/fa";
import { PiInstagramLogoFill } from "react-icons/pi";

import { useRouter } from "next/navigation";

const Header = ({ isAnimate = false }) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [expanded2, setExpanded2] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [active, setActve] = useState(false);
  const [mobmenu, setMobMenu] = useState(null);
  const [hover, setHover] = useState(null);
  const [highlightIndex, setHighlightIndex] = useState(0);

  const [menuElement, setMenuElement] = useState(0);
  const [showNavbar2, setShowNavbar2] = useState(false); // New state for showing navbar2

  useEffect(() => {
    if (isAnimate) {
    }
    const handleScroll = () => {
      if (window.scrollY > 80) {
        setScrolled(true);
        setShowNavbar2(true); // Show navbar2 when scrolled down
      } else {
        setScrolled(false);
        setShowNavbar2(false); // Show navbar1 when at the top
      }
    };

    document.addEventListener("scroll", handleScroll);

    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleNavigate = (link) => {
    setMobMenu(null);
    setActve(false);
    setExpanded(false);
    router.push(link);
  };
  const handleNavigate2 = (link) => {
    setMobMenu(null);
    setActve(false);
    setExpanded2(false);
    router.push(link);
  };

  return (
    <>
      <Search open={open} setOpen={setOpen} isSmall={true} />
      <div
        className={`tn-nav w-100 dis res-nav`}
        style={{ top: 0, borderRadius: "0px", opacity: "1" }}
      >
        <div className="tn-nav-rel">
          <div
            className="nav-parent"
            style={{ transform: `translateY(${active ? "0%" : "-100%"})` }}
          >
            <div
              className="mob-menu"
              style={{ marginLeft: `${mobmenu != null ? "-50%" : "0%"}` }}
            >
              {menus.map((item, i) => (
                <>
                  {item.submenus.length === 0 ? (
                    <>
                      <Link
                        href={item.link}
                        className={`mob-item ${
                          mobmenu == i ? "mob-open-0" : ""
                        }`}
                        key={i}
                      >
                        {item.name}
                      </Link>
                    </>
                  ) : (
                    <>
                      <div
                        className={`mob-item ${
                          mobmenu == i ? "mob-open-0" : ""
                        }`}
                        key={i}
                        onClick={() => setMobMenu(i)}
                      >
                        {item.name}
                        <FaChevronRight />
                      </div>
                    </>
                  )}
                </>
              ))}
              <div className="mbf">
                <div className="mob-follows">Follow us</div>
                <div className="mf-icons">
                  <Link
                    style={{ color: "black" }}
                    href={"https://instagram.com/hollywoodreporterindia"}
                    target="_blank"
                  >
                    <PiInstagramLogoFill />
                  </Link>
                  <Link
                    style={{ color: "black" }}
                    href={"https://twitter.com/thrindia_"}
                    target="_blank"
                  >
                    <FaTwitter />
                  </Link>
                  <Link
                    style={{ color: "black" }}
                    href={"https://www.youtube.com/@HollywoodReporterIndia"}
                    target="_blank"
                  >
                    <FaYoutube />
                  </Link>
                </div>
              </div>
              {/* <div className="mbf-thr-wrapped-container">
                <Link target="_blank" className="bo-right-wrapped" href="/wrapped-2024">
                  <video autoPlay muted loop playsInline src="/wrapped/vid.mp4"></video>
                  <div className="bo-right-wrapped-text">
                    <h2>THR India Wrapped 2024</h2>
                  </div>
                </Link>
                <Link className="bo-right-click-text-link" href="/wrapped-2024" target="_blank"><h6 className="bo-right-click-text">"Click" to open</h6></Link>
              </div> */}
              <div className="mbf-artboard-container">
                <Link
                  target="_blank"
                  className="bo-right-wrapped"
                  href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                >
                  <Image
                    src={"/Artboard-13.jpg"}
                    fill
                    alt="The Hollywood Reporter India cover Featuring Kareena Kapoor Khan and Vicky Kaushal"
                  />
                </Link>
                <Link
                  className="bo-right-click-text-link"
                  href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                  target="_blank"
                >
                  <h6 className="bo-right-click-text">Subscribe</h6>
                </Link>
              </div>
            </div>
            <div className="mob-submenu">
              <div className="mob-mbody">
                <div
                  className="mbody-head tb-head-trans"
                  onClick={() => setMobMenu(null)}
                >
                  <FaChevronLeft />
                  <span className="title">
                    {mobmenu !== null && menus[mobmenu].name}
                  </span>
                </div>
                {mobmenu !== null &&
                  menus[mobmenu].submenus.map((item, i) => (
                    <Link
                      href={item.link}
                      className="mob-item"
                      key={i}
                      onClick={() => {
                        setActve(false);
                      }}
                    >
                      {item.name}
                    </Link>
                  ))}
                <div className="m-body-main">
                  {/* <ul className="suggested-item"> */}
                  {/* {mobmenu !== null &&
                        menus[mobmenu].suggested.map(
                          (suggest, suggestionIndex) => (
                            <li
                              onClick={() => handleNavigate(`${suggest.link}`)}
                              key={`suggested-${suggestionIndex}`}
                            >
                              <div className="bor">
                                <div className="bor-img">
                                  <Image
                                    src={suggest.images}
                                    alt={suggest.alt}
                                    fill
                                  />
                                </div>
                                <div className="bor-text">{suggest.name}</div>
                              </div>
                            </li>
                          )
                        )} */}
                  {/* {mobmenu!==null &&  vid.map((el, i) => ( */}
                </div>
              </div>
              <div className="mbf">
                <div className="mob-follows">Follow us</div>
                <div className="mf-icons">
                  {/* <Link href={"#"} target="_blank">
                    <FaFacebookF />
                  </Link> */}
                  <Link
                    href={"https://instagram.com/hollywoodreporterindia"}
                    target="_blank"
                  >
                    <PiInstagramLogoFill />
                  </Link>
                  <Link href={"https://twitter.com/thrindia_"} target="_blank">
                    <FaTwitter />
                  </Link>
                  <Link
                    href={"https://www.youtube.com/@HollywoodReporterIndia"}
                    target="_blank"
                  >
                    <FaYoutube />
                  </Link>
                </div>
              </div>
              <div className="mbf-artboard-container">
                <Link
                  target="_blank"
                  className="bo-right-wrapped"
                  href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                >
                  <Image
                    src={"/Artboard-13.jpg"}
                    fill
                    alt="The Hollywood Reporter India cover Featuring Kareena Kapoor Khan and Vicky Kaushal"
                  />
                </Link>
                <Link
                  className="bo-right-click-text-link"
                  href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                  target="_blank"
                >
                  <h6 className="bo-right-click-text">Subscribe</h6>
                </Link>
              </div>
              {/* <div className="mbf-thr-wrapped-container">
                <Link
                  target="_blank"
                  className="bo-right-wrapped"
                  href="/wrapped-2024"
                >
                  <video
                    autoPlay
                    muted
                    loop
                    playsInline
                    src="/wrapped/vid.mp4"
                  ></video>
                  <div className="bo-right-wrapped-text">
                    <h2>THR India Wrapped 2024</h2>
                  </div>
                </Link>
                <Link
                  className="bo-right-click-text-link"
                  href="/wrapped-2024"
                  target="_blank"
                >
                  <h6 className="bo-right-click-text">"Click" to open</h6>
                </Link>
              </div> */}
            </div>
          </div>
          <div
            className={` ${
              expanded
                ? scrolled
                  ? "white-scrolled-expand"
                  : "white-expand2"
                : null
            } white-nav`}
          >
            <div
              className={`bottom-options2 ${scrolled ? "bt-open" : null}`}
              onMouseEnter={() => {
                setExpanded(true), setTouchNav(true);
              }}
              onMouseLeave={() => {
                setExpanded(false), setTouchNav(false);
              }}
            >
              <div className="bo-left">
                {menus[menuElement] &&
                  menus[menuElement].submenus &&
                  menus[menuElement].submenus.map((item, i) => (
                    <Link href={item.link} className="bo-items" key={i}>
                      <div className="bo-svg"></div>
                      {item.name}
                    </Link>
                  ))}
              </div>
              <div className="bo-right">
                {/* {menus[menuElement].suggested.map((item, i) => (
                  <Link href={item.link} className="bor" key={i}>
                    <div className="bor-img">
                      <Image src={item?.images ?? ""} alt={item.alt} fill />
                    </div>
                    <div className="bor-text">{item.name}</div>
                  </Link>
                ))} */}
                <div className="mbf-artboard-container">
                  <Link
                    target="_blank"
                    className="bo-right-wrapped"
                    href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                  >
                    <Image
                      src={"/Artboard-13.jpg"}
                      fill
                      alt="The Hollywood Reporter India cover Featuring Kareena Kapoor Khan and Vicky Kaushal"
                    />
                    <div className="bo-right-wrapped-text">
                      <h2>Subscribe</h2>
                    </div>
                  </Link>
                </div>
                {/* <Link
                  className="bo-right-wrapped"
                  target="_block"
                  href="/wrapped-2024"
                >
                  <video
                    autoPlay
                    muted
                    loop
                    playsInline
                    src="/wrapped/vid.mp4"
                  ></video>
                  <div className="bo-right-wrapped-text">
                    <h2>THR India Wrapped 2024</h2>
                  </div>
                </Link> */}
              </div>
            </div>
          </div>
          <div
            className="blur-nav nav-div t3"
            style={{ borderRadius: "0px", border: "0px" }}
          >
            {/* here */}
            <div className="tn t3">
              <div className="tn-left">
                <div
                  className="social-div-main"
                  onMouseEnter={() => {
                    setHover(1);
                  }}
                  onMouseLeave={() => {
                    setHover(null);
                  }}
                >
                  {hover == 1 ? (
                    <Link
                      href={"https://instagram.com/hollywoodreporterindia"}
                      target="_blank"
                      className="tn-elm"
                    >
                      <RiInstagramFill />
                    </Link>
                  ) : (
                    <Link
                      href={"https://instagram.com/hollywoodreporterindia"}
                      target="_blank"
                      className="tn-elm"
                    >
                      <FaInstagram />
                    </Link>
                  )}
                </div>
                <div
                  className="social-div-main"
                  onMouseEnter={() => {
                    setHover(2);
                  }}
                  onMouseLeave={() => {
                    setHover(null);
                  }}
                >
                  {hover == 2 ? (
                    <Link href={"#"} target="_blank" className="tn-elm">
                      <GrFacebookOption style={{ fontSize: "21px" }} />
                    </Link>
                  ) : (
                    <Link href={"#"} target="_blank" className="tn-elm">
                      <SlSocialFacebook />
                    </Link>
                  )}
                </div>
                <div
                  className="social-div-main"
                  onMouseEnter={() => {
                    setHover(3);
                  }}
                  onMouseLeave={() => {
                    setHover(null);
                  }}
                  style={{ position: "relative" }}
                >
                  <Link
                    href={"https://twitter.com/thrindia_"}
                    target="_blank"
                    className={hover == 3 ? "tn-elm" : "tn-elm visihidden"}
                    style={{ position: "absolute", left: "-7px" }}
                  >
                    <img src="/x.png" className="xsvg" alt="X Logo"></img>
                  </Link>

                  <Link
                    href={"https://twitter.com/thrindia_"}
                    target="_blank"
                    className={hover == 3 ? "tn-elm visihidden" : "tn-elm"}
                  >
                    <BsTwitterX style={{ fontSize: "18px" }} />
                  </Link>
                </div>
                <div
                  className="social-div-main"
                  onMouseEnter={() => {
                    setHover(4);
                  }}
                  onMouseLeave={() => {
                    setHover(null);
                  }}
                >
                  {hover == 4 ? (
                    <Link
                      href={"https://www.youtube.com/@HollywoodReporterIndia"}
                      target="_blank"
                      className="tn-elm"
                    >
                      <FaYoutube />
                    </Link>
                  ) : (
                    <Link
                      href={"https://www.youtube.com/@HollywoodReporterIndia"}
                      target="_blank"
                      className="tn-elm"
                    >
                      <PiYoutubeLogo />
                    </Link>
                  )}
                </div>

                <div className="tn-ham">
                  <div className="three col">
                    <div
                      className={`hamburger ${active ? "is-active" : null}`}
                      onClick={() => {
                        setActve(!active);
                        setMobMenu(null);
                      }}
                      id="hamburger-1"
                    >
                      <span className="line"></span>
                      <span className="line"></span>
                      <span className="line"></span>
                    </div>
                  </div>
                </div>
              </div>
              <Link href={"/"} className="tn-logo">
                <img
                  src="/Thr_logo_updated.png"
                  alt="The Hollywood Reporter India Logo"
                />
              </Link>

              <div className="tn-right t3">
                <div className="sdiv" onClick={() => setOpen(true)}>
                  <div className="tn-si flex-all">
                    <IoSearchOutline />
                  </div>
                  <div id="search">Search</div>
                </div>
                <div className="tn-ham">
                  <div className="three col">
                    <div
                      className={`hamburger ${active ? "is-active" : null}`}
                      onClick={() => {
                        setActve(!active);
                        setMobMenu(null);
                      }}
                      id="hamburger-1"
                    >
                      <span className="line"></span>
                      <span className="line"></span>
                      <span className="line"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="nav nav-menu-top t3">
              {menus.map((item, i) => (
                <>
                  {item.submenus.length === 0 ? (
                    <>
                      <Link href={item.link} key={i} className="nav-items">
                        {item.name}
                      </Link>
                    </>
                  ) : (
                    <>
                      <Link
                        href={item.submenus[0].link}
                        key={i}
                        className="nav-items"
                        onMouseOver={() => setMenuElement(i)}
                        onMouseEnter={() => setExpanded(true)}
                        onMouseLeave={() => setExpanded(false)}
                      >
                        {item.name}
                      </Link>
                    </>
                  )}
                </>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div
        style={{ pointerEvents: "none" }}
        className={` ${expanded2 ? "parent-nav blur" : "parent-nav"}`}
      >
        <div className={`tn-nav w-100 nav-small showmob trans`}>
          <div className="tn-nav-rel">
            <div
              className="nav-parent"
              style={{ transform: `translateY(${active ? "0%" : "-100%"})` }}
            >
              <div
                className="mob-menu"
                style={{ marginLeft: `${mobmenu != null ? "-50%" : "0%"}` }}
              >
                {menus.map((item, i) => (
                  <>
                    {item.submenus.length === 0 ? (
                      <>
                        <Link
                          href={item.link}
                          className={`mob-item ${
                            mobmenu == i ? "mob-open-0" : ""
                          }`}
                          key={i}
                        >
                          {item.name}
                        </Link>
                      </>
                    ) : (
                      <>
                        <div
                          className={`mob-item ${
                            mobmenu == i ? "mob-open-0" : ""
                          }`}
                          key={i}
                          onClick={() => setMobMenu(i)}
                        >
                          {item.name}
                          <FaChevronRight />
                        </div>
                      </>
                    )}
                  </>
                ))}
                <div className="mbf">
                  <div className="mob-follows">Follow us</div>
                  <div className="mf-icons">
                    <Link href={"#"} target="_blank">
                      <FaFacebookF />
                    </Link>
                    <Link
                      href={"https://instagram.com/hollywoodreporterindia"}
                      target="_blank"
                    >
                      <PiInstagramLogoFill />
                    </Link>
                    <Link
                      href={"https://twitter.com/thrindia_"}
                      target="_blank"
                    >
                      <FaTwitter />
                    </Link>
                    <Link
                      href={"https://www.youtube.com/@HollywoodReporterIndia"}
                      target="_blank"
                    >
                      <FaYoutube />
                    </Link>
                  </div>
                </div>
                <div className="mbf-artboard-container">
                  <Link
                    target="_blank"
                    className="bo-right-wrapped"
                    href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                  >
                    <Image
                      src={"/Artboard-13.jpg"}
                      fill
                      alt="The Hollywood Reporter India cover Featuring Kareena Kapoor Khan and Vicky Kaushal"
                    />
                  </Link>
                  <Link
                    className="bo-right-click-text-link"
                    href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                    target="_blank"
                  >
                    <h6 className="bo-right-click-text">Subscribe</h6>
                  </Link>
                </div>
                {/* <div className="mbf-thr-wrapped-container">
                  <Link
                    target="_blank"
                    className="bo-right-wrapped"
                    href="/wrapped-2024"
                  >
                    <video
                      autoPlay
                      muted
                      loop
                      playsInline
                      src="/wrapped/vid.mp4"
                    ></video>
                    <div className="bo-right-wrapped-text">
                      <h2>THR India Wrapped 2024</h2>
                    </div>
                  </Link>
                  <Link
                    className="bo-right-click-text-link"
                    href="/wrapped-2024"
                    target="_blank"
                  >
                    <h6 className="bo-right-click-text">"Click" to open</h6>
                  </Link>
                </div> */}
              </div>
              <div className="mob-submenu">
                <div className="mob-mbody">
                  <div
                    className="mbody-head tb-head-trans"
                    onClick={() => setMobMenu(null)}
                  >
                    <FaChevronLeft />
                    <span className="title">
                      {mobmenu !== null && menus[mobmenu].name}
                    </span>
                  </div>
                  {mobmenu !== null &&
                    menus[mobmenu].submenus.map((item, i) => (
                      <Link href={item.link} className="mob-item" key={i}>
                        {item.name}
                      </Link>
                    ))}
                  {/* <div className="m-body-main">
                    <ul className="suggested-item">
                      {mobmenu !== null &&
                        menus[mobmenu].suggested.map(
                          (suggest, suggestionIndex) => (
                            <li
                              onClick={() => handleNavigate2(`${suggest.link}`)}
                              key={`suggested-${suggestionIndex}`}
                            >
                              <div className="bor">
                                <div className="bor-img">
                                  <Image
                                    src={suggest.images}
                                    alt={suggest.alt}
                                    fill
                                  />
                                </div>
                                <div className="bor-text">{suggest.name}</div>
                              </div>
                            </li>
                          )
                        )}
                    </ul>
                  </div> */}
                </div>
                <div className="mbf">
                  <div className="mob-follows">Follow us</div>
                  <div className="mf-icons">
                    <Link href={"#"} target="_blank">
                      <FaFacebookF />
                    </Link>
                    <Link
                      href={"https://instagram.com/hollywoodreporterindia"}
                      target="_blank"
                    >
                      <PiInstagramLogoFill />
                    </Link>
                    <Link
                      href={"https://twitter.com/thrindia_"}
                      target="_blank"
                    >
                      <FaTwitter />
                    </Link>
                    <Link
                      href={"https://www.youtube.com/@HollywoodReporterIndia"}
                      target="_blank"
                    >
                      <FaYoutube />
                    </Link>
                  </div>
                </div>
                <div className="mbf-artboard-container">
                  <Link
                    target="_blank"
                    className="bo-right-wrapped"
                    href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                  >
                    <Image
                      src={"/Artboard-13.jpg"}
                      fill
                      alt="The Hollywood Reporter India cover Featuring Kareena Kapoor Khan and Vicky Kaushal"
                    />
                  </Link>
                  <Link
                    className="bo-right-click-text-link"
                    href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                    target="_blank"
                  >
                    <h6 className="bo-right-click-text">Subscribe</h6>
                  </Link>
                </div>
              </div>
            </div>
            <div
              className={` ${
                expanded2 ? "white-scrolled-expand" : null
              }  white-nav-small `}
            >
              <div
                className={`bottom-options bt-open`}
                onMouseEnter={() => setExpanded2(true)}
                onMouseLeave={() => setExpanded2(false)}
              >
                <div className="bo-left">
                  {menus[menuElement] &&
                    menus[menuElement].submenus &&
                    menus[menuElement].submenus.map((item, i) => (
                      <Link
                        href={item.link}
                        className="bo-items"
                        key={i}
                        onMouseEnter={() => {
                          setHighlightIndex(i);
                        }}
                      >
                        <div className="bo-svg"></div>
                        {item.name}
                      </Link>
                    ))}
                </div>
                <div className="bo-right">
                  {/* {menuData.length > 0 &&
                    menuData[menuElement].subCategory.map(
                      (item, i) =>
                        item.articleData &&
                        i === highlightIndex && (
                          <Link
                            href={
                              `${menuData[menuElement].slug}/${item?.slug}/${item?.articleData?.slug}` ??
                              "/"
                            }
                            className="bor"
                            key={i}
                          >
                            <div className="bor-img">
                              <Image
                                src={item.articleData.coverImg ?? ""}
                                alt="Image"
                                fill
                                onLoadingComplete={() => {
                                  // Update the corresponding index when the image is loaded
                                  const updatedImageLoaded = [...imageLoaded];
                                  updatedImageLoaded[i] = true;
                                  setImageLoaded(updatedImageLoaded);
                                }}
                              />
                            </div>
                            {imageLoaded[i] && (
                              <div className="bor-text">
                                {item.articleData.title}
                              </div>
                            )}
                          </Link>
                        )
                    )} */}
                  <div className="mbf-artboard-container">
                    <Link
                      target="_blank"
                      className="bo-right-wrapped"
                      href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                    >
                      <Image
                        src={"/Artboard-13.jpg"}
                        fill
                        alt="The Hollywood Reporter India cover Featuring Kareena Kapoor Khan and Vicky Kaushal"
                      />
                    </Link>
                    <Link
                      className="bo-right-click-text-link"
                      href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                      target="_blank"
                    >
                      <h6 className="bo-right-click-text">Subscribe</h6>
                    </Link>
                  </div>
                  {/* <Link
                    target="_blank"
                    className="bo-right-wrapped"
                    href="/wrapped-2024"
                  >
                    <video
                      autoPlay
                      muted
                      loop
                      playsInline
                      src="/wrapped/vid.mp4"
                    ></video>
                    <div className="bo-right-wrapped-text">
                      <h2>THR India Wrapped 2024</h2>
                    </div>
                  </Link>
                  <Link
                    className="bo-right-click-text-link"
                    href="/wrapped-2024"
                    target="_blank"
                  >
                    <h6 className="bo-right-click-text">"Click" to open</h6>
                  </Link> */}
                </div>
              </div>
            </div>
            <div
              className="blur-nav nav-div t3"
              style={{
                borderRadius: "0px",
                border: "0px",
                paddingBottom: "0px",
              }}
            >
              <div className="navsmall-main-div">
                <Link
                  href={"/"}
                  style={{ cursor: "pointer", visibility: "hidden" }}
                >
                  <img
                    className="small-logo"
                    src="/Thr_logo_updated.png"
                    alt="The Hollywood Reporter India Logo"
                  />
                </Link>
                <Link
                  href={"/"}
                  style={{ cursor: "pointer", position: "absolute" }}
                >
                  <img
                    style={{ height: "80px" }}
                    className="small-logo"
                    src="/Thr_logo_updated.png"
                    alt="The Hollywood Reporter India Logo"
                  />
                </Link>
                <div className="nav nav-menu-top t3" style={{ right: "12px" }}>
                  {menus.map((item, i) => (
                    <>
                      {item.submenus.length === 0 ? (
                        <>
                          <Link href={item.link} key={i} className="nav-items">
                            {item.name}
                          </Link>
                        </>
                      ) : (
                        <>
                          <Link
                            href={item.submenus[0].link}
                            key={i}
                            className="nav-items"
                            onMouseOver={() => setMenuElement(i)}
                            onMouseEnter={() => setExpanded2(true)}
                            onMouseLeave={() => setExpanded2(false)}
                          >
                            {item.name}
                          </Link>
                        </>
                      )}
                    </>
                  ))}
                </div>
                <div className="tn-right t3" style={{ top: "2px" }}>
                  <div className="sdiv" onClick={() => setOpen(true)}>
                    <div className="tn-si flex-all">
                      <IoSearchOutline />
                    </div>
                    <div id="search">Search</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;
