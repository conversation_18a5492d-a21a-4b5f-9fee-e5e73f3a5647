.shopping,
.highlightMagazine {
  padding: 10px 0px 50px;
}

.heatVision {
  padding: 30px 0px 50px;
}

/* .herosection{
  position: relative;
  margin-top: 116px;
} */
.readNow,
.liveFeed,
.reviews,
.lifeStyle {
  padding: 35px 0px 0px;
}

.topStories {
  padding: 90px 0px 5px;
  margin-bottom: 30px;
}

.mostPopular {
  padding: 0px 0px 30px;
}

.awardsSeason {
  padding: 45px 0px 0px;
}

.readNow .cardWrapper {
  position: relative;
  width: fit-content;
  overflow: hidden;
  transition: all 0.3s ease;
}

.readNow .cardWrapper:hover {
  transition: all 0.3s ease;
}

.readNow .cardWrapper:hover .contentSec p {
  color: var(--primary-color);
}

.readNow .cardWrapper .featureBox {
  width: 100%;
  height: 220px;
  margin-bottom: 0px;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.featureBox img {
  object-fit: cover;
  transition: all 0.5s ease;
}

.readNow .cardWrapper:hover .featureBox img {
  transform: scale(1.02);
  transition: all 0.4s ease-in-out;
}

.readNow .cardWrapper .featureBox img {
  width: 100%;
  height: inherit;
  object-fit: cover;
}

.readNow .cardWrapper .contentSec {
  padding: 5px 0px;
  text-align: left;
}

.readNow .cardWrapper span.category {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary) !important;
}

.readNow .cardWrapper .contentSec p {
  font-size: 23px;
  color: var(--color);
  font-weight: 700;
  line-height: 1;
  margin-bottom: 10px;
  letter-spacing: 0.00875rem;
  transition: all 0.3s ease;
}

.readNow .cardWrapper .contentSec p.timeline {
  color: var(--secondary-color);
  font-weight: 700;
  font-size: 12px;
  margin-bottom: 2px;
  font-family: var(--font-family-secondary) !important;
}

.imageSec {
  position: relative;
  width: 100%;
  height: 500px;
  overflow: hidden;
}

.contentSec {
  padding: 20px 36px;
  text-align: center;
}

.contentSec h3.cardtitle {
  color: #000;
  font-size: 24px !important;
  line-height: 1 !important;
  margin-bottom: 10px !important;
  transition: all 0.3s ease;
}

.contentSec h3 {
  color: #000;
  font-size: 50px;
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.hoverSec:hover.contentSec h3 {
  color: var(--primary-color);
}

.contentSec p {
  color: var(--secondary-color);
  font-size: 22px;
  /* width: 650px; */
  line-height: 1.2;
  margin: 5px auto;
  font-family: "kepler-std-display", serif !important;
  font-weight: 300;
  font-style: normal;
}

.ln-cont .card:first-child {
  margin: 0px 0px 20px;
}

.ln-cont .card {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  margin: 20px 0;
  transition: 0.5s ease;
}

.ln-cont .card:hover {
  transform: translateX(5px);
  cursor: pointer;
}

.card {
  background-color: #f3f4f5;
  /* border : 1px solid rgba(0, 0, 0, 0.175); */
  padding: 0px;
  margin-bottom: 15px;
  overflow: hidden;
}

.card .image {
  position: relative;
  overflow: hidden;
}

.card .image img {
  width: inherit;
  height: inherit;
  object-fit: cover;
  /* object-position: left; */
}

.card .content {
  padding: 10px 20px;
}

.card .content span.category {
  display: block;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--primary-color);
  font-family: var(--font-family-secondary);
}

.card .content span.timeline {
  display: block;
  font-size: 12px;
  color: var(--secondary-color);
  font-family: var(--font-family-secondary);
}

.card .content p {
  font-size: 17px !important;
  font-weight: 400;
  line-height: 1.2;
  text-align: left;
  font-family: "kepler-std-display", serif !important;
  font-weight: 300;
  font-style: normal;
}

.heading {
  color: #6442ac;
  font-weight: 600;
  margin-bottom: 0px;
  /* font-size: 28px; */
}

.subheading {
  font-size: 18px;
  font-style: italic;
  margin-bottom: 20px;
  font-family: "kepler-std", serif !important;
}

.featureBox {
  position: relative;
  width: 100%;
  height: 320px;
  background: rgba(163, 163, 163, 0.25);
  margin-bottom: 20px;
}

.hvGradientBorder,
.lfGradientBorder {
  position: relative;
}

.hvGradientBorder::after {
  content: "";
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  height: 6.5vw;
  border-width: 3px;
  border-style: solid;
  border-image: linear-gradient(to top, #6442ac, #fff) 1 1;
  z-index: -1;
}

.lfGradientBorder::after {
  content: "";
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  height: 6.5vw;
  border-width: 3px;
  border-style: solid;
  border-image: linear-gradient(to top, #008080, #fff) 1 1;
  z-index: -1;
}

.hvImageSec {
  position: relative;
  overflow: hidden;
  height: 447px;
}

.hvItems .hvContentSec:hover h3 {
  color: var(--primary-color) !important;
}

.hvItems .hvContentSec {
  padding: 20px 20px 20px;
  border-bottom: 1px dotted rgba(0, 0, 0, 1) !important;
  transition: all 0.3s ease;
}

.hvItems .hvContentSec:first-child {
  padding: 0px 20px 20px;
}

.hvItems .hvContentSec:last-child {
  padding: 20px 20px 0px;
  border-bottom: none !important;
}

.hvContentSec {
  text-align: left;
  padding: 20px;
}

.hvContentSec span.smallTime {
  display: block;
  margin: 2px 0px 12px;
  font-size: 13px;
  font-weight: 700;
  text-align: left;
  font-family: var(--font-family-secondary);
  color: #6442ac;
}

.lfGradientBorder .hvContentSec span.smallTime {
  color: #008080 !important;
}

.liveFeed .hvContentSec span.smallTime {
  color: #008080;
}

.hvContentSec h3 {
  color: #000;
  font-size: 42px;
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.hvContentSec h3.sideTitle {
  font-size: 35px !important;
}

.hvContentSec p {
  color: var(--secondary-color);
  font-size: 22px;
  line-height: 1.2;
  margin: 5px auto;
  font-family: "kepler-std", serif !important;
  font-weight: 300;
  font-style: normal;
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.awardsSeason h2 {
  color: var(--primary-color);
  font-size: 36px;
  font-weight: 700 !important;
  line-height: 1;
  margin-bottom: 10px !important;
}

.awardsSeason p {
  font-size: 16px;
  line-height: 1.2;
  margin-bottom: 25px;
  /* font-style: italic; */
  font-family: var(--font-family-secondary) !important;
  /* font-family: "kepler-std",serif!important; */
}

.awardsSeason .cardItem {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
}

.awardsSeason .cardItem .cardWrapper {
  width: fit-content;
  color: inherit !important;
}

.awardsSeason .cardItem .cardWrapper h3 {
  font-size: 22px !important;
  font-weight: 600 !important;
  line-height: 1.2;
  text-align: left;
  font-weight: 300;
  font-style: normal;
  transition: all 0.3s ease;
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* font-size: 18px;
  font-family: "kepler-std-display", serif!important;
  font-weight: 300;
  font-style: normal; */
}

.awardsSeason .cardItem .cardWrapper:hover {
  cursor: pointer;
  color: var(--primary-color) !important;
  /* color: rgba(0, 0, 0, 0.4)!important; */
}

.awardsSeason .cardItem .featureBox {
  width: 100%;
  height: 150px;
  margin-bottom: 15px;
}

.featuredVideo .cardWrapper {
  /* width: fit-content; */
  transition: all 0.3s ease;
  cursor: pointer;
}

.featuredVideo .cardWrapper .featureBox {
  width: 100%;
  height: 400px;
  margin-bottom: 15px;
}

.featuredVideo .cardWrapper h3 {
  font-size: 22px !important;
  font-weight: 600 !important;
  line-height: 1.2;
  text-align: left;
  font-weight: 300;
  font-style: normal;
  transition: all 0.3s ease;
}

.featuredVideo .cardWrapper:hover h3 {
  color: var(--primary-color) !important;
}

.reviewCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: rgba(0, 0, 0, 0.025);
  padding: 5px 10px;
  margin-bottom: 20px;
  overflow: hidden;
}

.reviewCard .imageAvtar {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  min-width: fit-content;
}

.reviewCard .imageAvtar img {
  width: auto;
  height: inherit;
}

.reviewCard .contentSec {
  padding: 5px 10px;
  text-align: left;
}

.reviewCard .contentSec .title {
  display: block;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary);
}

.reviewCard .contentSec .desc {
  font-size: 20px !important;
  font-weight: 500 !important;
  line-height: 1.2;
  text-align: left;
  font-weight: 300;
  font-style: normal;
}

.reviews .card {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.025);
  /* border : 1px solid rgba(0, 0, 0, 0.175); */
  padding: 0px;
  margin-bottom: 20px;
  overflow: hidden;
}

.reviews .card:last-child {
  margin-bottom: 0px;
}

.reviews .card .image {
  position: relative;
  width: 250px;
  min-width: 250px;
  height: 160px;
  overflow: hidden;
  /* padding: 20px; */
}

.reviews .card .image img {
  object-fit: cover;
}

.reviews .card .content {
  padding: 20px;
}

.reviews .card .content span.category {
  display: block;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary);
}

.reviews .card .content span.timeline {
  display: block;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--secondary-color);
  font-family: var(--font-family-secondary);
}

.reviews .card .content h3 {
  font-size: 24px !important;
  font-weight: 500 !important;
  line-height: 1.2;
  text-align: left;
  font-weight: 300;
  font-style: normal;
}

.lifeStyle .cardWrapper {
  position: relative;
  width: fit-content;
  overflow: hidden;
  transition: all 0.3s ease;
}

.lifeStyle .cardWrapper:hover {
  transition: all 0.3s ease;
}

.lifeStyle .cardWrapper .contentSec p {
  transition: all 0.3s ease;
}

.lifeStyle .cardWrapper:hover .contentSec p {
  color: var(--primary-color);
}

.lifeStyle .cardWrapper .featureBox:hover img {
  transform: scale(1.02);
  transition: all 0.4s ease-in-out;
}

.lifeStyle .cardWrapper .featureBox img {
  width: 100%;
  height: inherit;
  object-fit: cover;
}

.lifeStyle .cardWrapper .featureBox {
  position: relative;
  width: 100%;
  height: 300px;
  margin-bottom: 0px;
  overflow: hidden;
}

.lifeStyle .cardWrapper .contentSec {
  padding: 5px 0px;
  text-align: left;
}

.lifeStyle .cardWrapper span.category {
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-secondary) !important;
}

.lifeStyle .cardWrapper .contentSec p {
  font-size: 23px;
  color: var(--color);
  line-height: 1;
  font-weight: 700;
  margin-bottom: 5px;
  letter-spacing: 0.00875rem;
  margin: 5px auto;
  font-family: "kepler-std-display", serif !important;
  font-style: normal;
}

.lifeStyle .cardWrapper .contentSec p.timeline {
  color: var(--secondary-color);
  font-weight: 700;
  font-size: 12px;
  margin-bottom: 2px;
  font-family: var(--font-family-secondary) !important;
}

.featuredPodcasts h2 {
  color: var(--primary-color);
  font-size: 36px;
  font-weight: 700 !important;
  line-height: 1;
  margin-bottom: 10px !important;
}

.featuredPodcasts p {
  font-size: 16px;
  line-height: 1.2;
  margin-bottom: 25px;
  /* font-style: italic; */
  /* font-family: "kepler-std",serif!important; */
  font-family: var(--font-family-secondary) !important;
}

.featuredPodcasts .cardItem {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 20px;
}

.featuredPodcasts .cardItem .cardWrapper {
  width: fit-content;
  color: inherit !important;
}

.featuredPodcasts .cardItem .cardWrapper h3 {
  font-size: 22px !important;
  font-weight: 600 !important;
  line-height: 1.2;
  text-align: left;
  font-weight: 300;
  font-style: normal;
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: all 0.3s ease;
}

.featuredPodcasts .cardItem .cardWrapper h3:hover {
  color: var(--primary-color);
}

.featuredPodcasts .cardItem .featureBox {
  width: 100%;
  height: 150px;
  margin-bottom: 15px;
}

.shopping .banner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 550px;
  background: rgba(163, 163, 163, 0.25);
  margin-bottom: 20px;
}

.highlightMagazine .banner {
  display: flex;
  align-items: center;
  justify-content: center;
  /* gap: 20px; */
  width: 100%;
  height: 650px;
  /* background: rgba(163, 163, 163, 0.25); */
  margin-bottom: 20px;
}

.mainheadtopsection {
  font-size: 50px;
  font-weight: 500!important;
  color: white !important;
  transition: all 0.2s ease-in;
}

.mainheadtopsection a{
  color: unset!important;
}

.mainheadtopsection:hover {
  color: #d92128 !important;
}

.mostPopular .sliderItem {
  display: flex;
  width: 100%;
}

.mostPopular .sliderItem>.items {
  display: flex;
  border-right: 1px dotted rgba(0, 0, 0, 1);
  padding-left: 40px;
  padding-right: 10px;
  color: inherit !important;
}

.mostPopular .sliderItem>.items:first-child {
  padding-left: 0px;
  padding-right: 10px;
}

.mostPopular .sliderItem>.items:last-child {
  border-right: none;
  padding-right: 40px;
}

.mostPopular .sliderItem>.items h3.sequence {
  color: var(--primary-color);
  font-size: 46px;
  margin-bottom: 0px;
}

.mostPopular .sliderItem>.items p {
  font-size: 18px;
  line-height: 1.4;
  margin-bottom: 0px;
  padding: 5px 10px 0px 10px;
  /* font-family: "kepler-std",serif!important; */
  font-weight: 600;
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.featuredVideoSingle {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Compters and Big Screens Small Changes*/
@media screen and (min-width: 1600px) and (max-width: 1900px) {}

/* Compters and Big Screens */
@media screen and (min-width: 1600px) {}

/* Laptop and Tablets Small Changes*/
@media screen and (min-width: 901px) and (max-width: 1200px) {}

/* Tablets and Mobile */
@media screen and (max-width: 900px) {

  .readNow,
  .liveFeed,
  .reviews,
  .lifeStyle {
    padding: 30px 0px 0px;
  }

  /* .herosection{
    margin-top: 43px;
  } */
  .topStories {
    padding: 10px 0px;
  }

  .heatVision {
    padding: 10px 0px 10px;
  }

  .subheading {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .hvImageSec {
    height: 220px;
  }

  .hvContentSec {
    padding: 15px 5px;
  }

  .hvContentSec h3 {
    font-size: 35px;
    line-height: 1;
    text-align: left;
  }

  .hvContentSec p {
    font-size: 19px;
  }

  .hvItems .hvContentSec {
    padding: 15px 5px !important;
  }

  .awardsSeason .cardItem .featureBox {
    width: 100%;
    height: 220px;
    margin: 15px auto;
    margin-bottom: 0px;
  }

  .awardsSeason .cardItem .cardWrapper h3 {
    font-size: 27px !important;
    margin-top: 10px;
  }

  .featuredVideo .cardWrapper .featureBox {
    margin-bottom: 0px !important;
  }

  .featuredVideo .cardWrapper h3 {
    font-size: 27px !important;
    margin-top: 10px;
  }

  .mostPopular .sliderItem>.items {
    padding-left: 0px !important;
    padding-right: 0px !important;
  }

  .reviews .card .image {
    width: 110px;
    min-width: 110px;
    height: 110px !important;
    margin: 12px;
  }

  .reviews .card .content {
    padding: 12px 12px 12px 0px;
  }

  .reviews .card .content h3 {
    line-height: 1.1;
  }

  .featuredPodcasts .cardItem .featureBox {
    width: 100%;
    height: 220px;
    margin: 15px auto;
    margin-bottom: 0px;
  }

  .featuredPodcasts .cardItem .cardWrapper h3 {
    font-size: 27px !important;
    margin-top: 10px;
  }

  .reviews .card:last-child {
    margin-bottom: 55px;
  }

  .reviewCard .contentSec .desc {
    font-size: 24px !important;
    line-height: 1.2;
  }

  .highlightMagazine .banner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: fit-content;
    background-color: #fff;
    margin-bottom: 20px;
    gap: 20px;
  }
}

/* Tablets and Mobiles Small Changes */
@media screen and (min-width: 426px) and (max-width: 900px) {
  .awardsSeason .cardItem .featureBox {
    width: 100%;
    height: 240px;
    margin: 15px auto;
    margin-bottom: 0px;
  }

  .featuredVideo .cardWrapper .featureBox {
    height: 220px;
  }

  .hvImageSec {
    height: 285px;
  }
}

@media screen and (max-width: 425px) {
  .hvContentSec h3 {
    font-size: 24px;
    margin-bottom: 0px;
    text-align: center;
  }

  .hvContentSec {
    padding: 10px 0px;
    border-bottom: 1px dotted rgba(0, 0, 0, 1) !important;
    transition: all 0.3s ease;
  }

  .hvContentSec h3.sideTitle {
    font-size: 24px !important;
    line-height: 1.2;
    text-align: center !important;
  }

  .hvContentSec p {
    font-size: 16px;
    display: none;
  }

  .smallTime {
    text-align: center !important;
    margin: 0px auto 2px !important;
  }

  .awardsSeason .cardItem .cardWrapper h3 {
    font-size: 23px !important;
    font-weight: 500 !important;
  }

  .featuredVideo .cardWrapper h3 {
    font-size: 23px !important;
    font-weight: 500 !important;
  }

  .featuredPodcasts .cardItem .cardWrapper h3 {
    font-size: 23px !important;
    font-weight: 500 !important;
  }

  .reviewCard .contentSec .desc {
    font-size: 21px !important;
    display: -webkit-box;
    max-width: 300px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .reviews .card .content h3 {
    display: -webkit-box;
    max-width: 300px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .mostPopular .sliderItem {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .mostPopular .sliderItem>.items {
    border-right: none;
    border-bottom: 1px dotted rgba(0, 0, 0, 1);
  }

  .mostPopular .sliderItem>.items:last-child {
    border-bottom: none;
  }

  .mostPopular .sliderItem>.items p {
    padding: 0px 10px;
    margin: 15px 0px !important;
    display: -webkit-box;
    max-width: 300px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .shopping {
    padding: 30px 0px 25px;
  }

  .reviewCard .contentSec .title {
    font-size: 12px;
  }

  .reviews .card .content span.category {
    font-size: 12px;
  }

  .reviews .card .content span.timeline {
    font-size: 12px;
  }

  .awardsSeason .cardItem .featureBox {
    height: 180px;
  }

  .featuredVideo .cardWrapper .featureBox {
    height: 180px;
  }

  .featuredPodcasts .cardWrapper .featureBox {
    height: 180px;
  }

  .hvItems .hvContentSec {
    padding: 10px 0px !important;
  }
}