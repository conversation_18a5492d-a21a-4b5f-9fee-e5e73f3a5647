import React from "react";
import Image from "next/image";
import style from "./Author.module.css";
import Link from "next/link";
import { RiTwitterXLine, RiLinkedinLine } from "react-icons/ri";
import { SlSocialFacebook } from "react-icons/sl";
import { FaInstagram } from "react-icons/fa6";
import {} from "react-icons/ri";

const InfoSection = ({ data }) => {
  return (
    <>
      <div className="row">
        <div className="col-md-12 pad-author">
          <div className={style.infoSection}>
            <div className={style.imageBlock}>
              <Image
                src={data?.image || "/placeholder/author_placeholder.jpg"}
                alt={data?.name || "Author Image"}
                fill
              />
            </div>
            <div className={style.contentWrapper}>
              <div className={style.descriptionBlock}>
                <div className={style.titleGroup}>
                  <h1 className={style.title}>{data?.name || ""}</h1>
                  <h3 className={style.subheading}>{data?.subheading || ""}</h3>
                </div>
                <div className={style.followUs}>
                  {data?.social?.instagram && (
                    <Link
                      href={`https://www.instagram.com/${data?.social?.instagram}`}
                      target="_blank"
                    >
                      <FaInstagram />
                    </Link>
                  )}
                  {data?.social?.facebook && (
                    <Link
                      href={`https://www.facebook.com/${data?.social?.facebook}`}
                      target="_blank"
                    >
                      <SlSocialFacebook />
                    </Link>
                  )}
                  {data?.social?.twitterX && (
                    <Link
                      href={`https://twitter.com/${data?.social?.twitterX}`}
                      target="_blank"
                    >
                      <RiTwitterXLine />
                    </Link>
                  )}
                  {data?.social?.linkedIn && (
                    <Link
                      href={`https://in.linkedin.com/${data?.social?.linkedIn}`}
                      target="_blank"
                    >
                      <RiLinkedinLine />
                    </Link>
                  )}
                </div>
              </div>
              <div className={style.contentBlock}>
                <p>{data?.aboutus || ""}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default InfoSection;
