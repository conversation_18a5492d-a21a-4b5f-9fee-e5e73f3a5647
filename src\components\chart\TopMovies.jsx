import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { BsArrowDown } from "react-icons/bs";
import { BsArrowUp } from "react-icons/bs";

const TopMovies = ({ title, description, data }) => {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleSummary = (index) => {
    setOpenIndex((prevIndex) => (prevIndex === index ? null : index));
  };
  return (
    <>
      <div className="container">
        <div className="container-padding">
          <div className="Chart--heading-cntr">
            <h2 className="container-split">{title ?? ""}</h2>
            <p className="chart--small--title">{description ?? ""}</p>
          </div>
          {/* <div className="column-grid tablet--smaller is--flims">
            <div className="flims-list__title">rank</div>
            <div className="flims-list__title">film</div>
            <div className="flims-list__title">week</div>
            <div className="flims-list__title">Weekend Box Office</div>
            <div className="flims-list__title">Cumulative Box Office</div>
          </div> */}
          <div className="w-dyn-list">
            <div role="list" className="flims-list w-dyn-items">
              {data.map((item, index) =>
                item?.slug ? (
                  <Link
                    target="_blank"
                    href={item?.slug ?? "#"}
                    role="listitem"
                    className="flims-item list--view w-dyn-item"
                    key={`top-movies-${index}`}
                  >
                    <div className="line-w">
                      <div className="line-inner" />
                    </div>
                    <div className="flims-item__inner w-inline-block" title="">
                      <div
                        className="column-grid tablet--smaller is--flims"
                        // style={{
                        //   gridTemplateColumns:
                        //     "0.2fr 1.4fr 0.7fr 0.3fr 0.5fr 0.5fr",
                        // }}
                      >
                        <span className="rank--text">{index + 1}</span>
                        <div className="flims__left">
                          <div className="flims__col--img__w">
                            <Image
                              loading="lazy"
                              src={item?.coverImg ?? ""}
                              alt={item?.altName ?? ""}
                              width={500}
                              height={300}
                            />
                          </div>
                          <div className="flim--text-cntr">
                            <h3 className="text--large is--caps">
                              {item?.articleTitle ?? ""}
                            </h3>
                            {/* <h5 className="text--small is--caps">
                              {item?.articleSubheading ?? ""}
                            </h5> */}
                          </div>
                        </div>
                        <div
                          className="text--font--size text-week"
                          style={{
                            // display: "flex",
                            flexDirection: "column",
                            alignItems: "baseline",
                          }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.originalLanguage}
                          </p>
                          {item?.originalLanguage && (
                            <p className="subhead-charts">Original Language</p>
                          )}
                        </div>
                        <div
                          className="text--font--size text-week"
                          // style={{ display: 'flex', flexDirection: 'column', alignItems: 'baseline' }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.week}
                          </p>
                          {item?.week && <p className="subhead-charts">Week</p>}
                        </div>
                        <div
                          className="text--font--size text-weekend"
                          // style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.weekendBoxOffice ?? ""}
                          </p>
                          {item?.weekendBoxOffice && (
                            <p className="subhead-charts">Weekend Box Office</p>
                          )}
                        </div>
                        <div
                          className="text--font--size text-cumulative"
                          // style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', color: '#db242a'}}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.cumulativeBoxOffice ?? ""}
                          </p>
                          {item?.cumulativeBoxOffice && (
                            <p className="subhead-charts">
                              Cumulative Box Office
                            </p>
                          )}
                        </div>
                        <div
                          className="down-arrow"
                          onClick={(event) => {
                            event.preventDefault();
                            toggleSummary(index);
                          }}
                        >
                          {openIndex === index ? (
                            <BsArrowUp />
                          ) : (
                            <BsArrowDown />
                          )}
                        </div>
                      </div>
                      {openIndex === index && (
                        <div className="summary">
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              Original Language
                            </div>
                            <div className="text-week font--size">
                              {item?.originalLanguage}
                            </div>
                          </div>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              week
                            </div>
                            <div className="text-week font--size">
                              {item?.week}
                            </div>
                          </div>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              Weekend Box Office
                            </div>
                            <div className="text-week font--size">
                              {item?.weekendBoxOffice}
                            </div>
                          </div>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              cumulative Box Office
                            </div>
                            <div className="text-week font--size">
                              {item?.cumulativeBoxOffice}
                            </div>
                          </div>
                          <img
                            loading="lazy"
                            alt={item?.altName ?? ""}
                            src={item?.coverImg ?? ""}
                          />
                        </div>
                      )}
                    </div>
                  </Link>
                ) : (
                  <div
                    role="listitem"
                    className="flims-item list--view w-dyn-item chart-div"
                    key={`top-movies-${index}`}
                  >
                    <div className="line-w">
                      <div className="line-inner" />
                    </div>
                    <div className="flims-item__inner w-inline-block" title="">
                      <div
                        className="column-grid tablet--smaller is--flims"
                        // style={{
                        //   gridTemplateColumns:
                        //     "0.2fr 1.4fr 0.7fr 0.3fr 0.5fr 0.5fr",
                        // }}
                      >
                        <span className="rank--text">{index + 1}</span>
                        <div className="flims__left">
                          <div className="flims__col--img__w">
                            <Image
                              loading="lazy"
                              src={item?.coverImg ?? ""}
                              alt={item?.altName ?? ""}
                              width={500}
                              height={300}
                            />
                          </div>
                          <div className="flim--text-cntr">
                            <h3 className="text--large is--caps">
                              {item?.articleTitle ?? ""}
                            </h3>
                            {/* <h5 className="text--small is--caps">
                              {item?.articleSubheading ?? ""}
                            </h5> */}
                          </div>
                        </div>
                        <div
                          className="text--font--size text-week"
                          style={{
                            // display: "flex",
                            flexDirection: "column",
                            alignItems: "baseline",
                          }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.originalLanguage}
                          </p>
                          {item?.originalLanguage && (
                            <p className="subhead-charts">Original Language</p>
                          )}
                        </div>
                        <div
                          className="text--font--size text-week"
                          // style={{ display: 'flex', flexDirection: 'column', alignItems: 'baseline' }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.week}
                          </p>
                          {item?.week && <p className="subhead-charts">Week</p>}
                        </div>
                        <div
                          className="text--font--size text-weekend"
                          // style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.weekendBoxOffice ?? ""}
                          </p>
                          {item?.weekendBoxOffice && (
                            <p className="subhead-charts">Weekend Box Office</p>
                          )}
                        </div>
                        <div
                          className="text--font--size text-cumulative"
                          // style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', color: '#db242a'}}
                        >
                          <p
                            style={{
                              margin: "0",
                              fontSize: "25px",
                              fontWeight: "500",
                            }}
                          >
                            {item?.cumulativeBoxOffice ?? ""}
                          </p>
                          {item?.cumulativeBoxOffice && (
                            <p className="subhead-charts">
                              Cumulative Box Office
                            </p>
                          )}
                        </div>
                        <div
                          className="down-arrow"
                          onClick={(event) => {
                            event.preventDefault();
                            toggleSummary(index);
                          }}
                        >
                          {openIndex === index ? (
                            <BsArrowUp />
                          ) : (
                            <BsArrowDown />
                          )}
                        </div>
                      </div>
                      {openIndex === index && (
                        <div className="summary">
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              Original Language
                            </div>
                            <div className="text-week font--size">
                              {item?.originalLanguage}
                            </div>
                          </div>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              week
                            </div>
                            <div className="text-week font--size">
                              {item?.week}
                            </div>
                          </div>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              Weekend Box Office
                            </div>
                            <div className="text-week font--size">
                              {item?.weekendBoxOffice}
                            </div>
                          </div>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div className="flims-list__title text-responsive">
                              cumulative Box Office
                            </div>
                            <div className="text-week font--size">
                              {item?.cumulativeBoxOffice}
                            </div>
                          </div>
                          <img
                            loading="lazy"
                            alt={item?.altName ?? ""}
                            src={item?.coverImg ?? ""}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TopMovies;
