import React from "react";
import { IoMdSearch } from "react-icons/io";

const Heading = ({
  query = "",
  resultText = "",
  title = "",
  pageNo = 1,
  setText = null,
  handleSearch = null,
}) => {
  return (
    <>
      <section className="result-heading-section">
        <div className="tag-head">
          {resultText && <p className="tag-head-p-small">{resultText}</p>}
          <h2 className="tag-head-h2">{query || "Results"}</h2>
          <div className="search-tag">
            <div className="search-div-tag">
              <form
                style={{ width: "100%" }}
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSearch(pageNo);
                }}
              >
                <input
                  className="input-tag"
                  value={title}
                  onChange={(e) => setText(e.target.value)}
                />
              </form>
              <IoMdSearch
                style={{ cursor: "pointer", fontSize: "18px" }}
                onClick={() => handleSearch(pageNo)}
              />
            </div>
          </div>
          {/* <button className="save-filter-btn" onClick={handleFilterToggle}>
                  Show Filter
                </button> */}
        </div>
      </section>
    </>
  );
};

export default Heading;
