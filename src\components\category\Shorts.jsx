import React, { useRef } from "react";
import { Autoplay, Keyboard, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SlideNavButton from "@/components/common/SlideNavButton";
import "swiper/css";
import "swiper/css/navigation";
import Link from "next/link";

const Shorts = ({ data }) => {
  const videoRefs = useRef([]);

  const handleMouseEnter = (index) => {
    const video = videoRefs.current[index];
    if (video && video.play) {
      video.play();
    }
  };

  const handleMouseLeave = (index) => {
    const video = videoRefs.current[index];
    if (video && video.pause) {
      video.pause();
      video.currentTime = 0;
    }
  };

  return (
    <>
      {data && data.length > 0 && (
        <>
          <section id="shorts-videos">
            <div className="container">
              <div className="row">
                <div className="col-md-12 div-flex-between">
                  <h2>Shorts</h2>
                  <div>
                    <SlideNavButton prev={"shortPrev"} next={"shortNext"} />
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="col-md-12">
                  <Swiper
                    autoplay={{
                      delay: 2500,
                      disableOnInteraction: false,
                    }}
                    pagination={{
                      clickable: true,
                    }}
                    modules={[Navigation, Pagination, Keyboard]}
                    spaceBetween={20}
                    slidesPerView={5}
                    navigation={{
                      prevEl: ".shortPrev",
                      nextEl: ".shortNext",
                    }}
                    loop={true}
                    breakpoints={{
                      320: {
                        slidesPerView: 1,
                        spaceBetween: 10,
                      },
                      480: {
                        slidesPerView: 2,
                        spaceBetween: 10,
                      },
                      768: {
                        slidesPerView: 3,
                        spaceBetween: 15,
                      },
                      1024: {
                        slidesPerView: 5,
                        spaceBetween: 20,
                      },
                    }}
                  >
                    {data.map((item, i) => (
                      <SwiperSlide key={`shorts-${i}`}>
                        <Link href={data?.slug ?? "#"}>
                          <div className="card-video-wrapper">
                            <div
                              className="card-featured-box"
                              onMouseEnter={() => handleMouseEnter(i)}
                              onMouseLeave={() => handleMouseLeave(i)}
                            >
                              <video
                                ref={(el) => (videoRefs.current[i] = el)}
                                src={item.src}
                                loop
                                muted
                                playsInline
                              />
                            </div>
                            <div className="card-video-content">
                              <h3 className="card-video-title">{item.title}</h3>
                              <span className="card-video-views">{`111k views`}</span>
                            </div>
                          </div>
                        </Link>
                      </SwiperSlide>
                    ))}
                  </Swiper>
                </div>
              </div>
            </div>
          </section>
        </>
      )}
    </>
  );
};

export default Shorts;
