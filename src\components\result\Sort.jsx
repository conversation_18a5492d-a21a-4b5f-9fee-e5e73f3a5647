import React, { useEffect, useMemo } from "react";

const Sort = ({ filters, defaultFilter, selectedFilter, setSelectedFilter }) => {
  useEffect(() => {
    setSelectedFilter(selectedFilter);
  }, []);

  const handleFilter = (value, key, isSelect = false) => {
    setSelectedFilter((prev) => {
      let updated = { ...prev };

      if (isSelect) {
        const parsedValue = parseInt(value, 10);
        if (updated[key] === parsedValue) return updated;
        updated[key] = parsedValue;
      } else {
        const values = new Set(updated[key] || []);
        values.has(value) ? values.delete(value) : values.add(value);
        updated[key] = Array.from(values);
      }

      return updated;
    });
  };

  const handleClearFilter = (key) => {
    if (!key) return;
    const clearedValue = { [key]: defaultFilter[key] };
    setSelectedFilter((prev) => ({ ...prev, ...clearedValue }));
  };

  const filterData = useMemo(
    () => [
      {
        title: "Sort By",
        key: "sortBy",
        type: "select",
        items: [
          { value: 0, name: "Relevance" },
          { value: -1, name: "Published Date (Newest first)" },
          { value: 1, name: "Published Date (Oldest first)" },
        ],
      },
      {
        title: "Date",
        key: "dateRange",
        type: "select",
        items: [
          { value: 0, name: "All" },
          { value: 1, name: "Past 24 hours" },
          { value: 2, name: "Past 7 days" },
          { value: 3, name: "Past 30 days" },
          { value: 4, name: "Past 1 year" },
        ],
      },
      {
        title: "Category",
        key: "subcategoryIds",
        type: "checkbox",
        items:
          filters?.subcategories?.map((item) => ({
            value: item?._id || "",
            name: item?.name || "",
            count: item?.articleCount || 0,
          })) || [],
      },
      {
        title: "Tags",
        key: "tagIds",
        type: "checkbox",
        items:
          filters?.tags?.map((item) => ({
            value: item?._id || "",
            name: item?.name || "",
            count: item?.articleCount || 0,
          })) || [],
      },
      {
        title: "Authors",
        key: "writerIds",
        type: "checkbox",
        items:
          filters?.writers?.map((item) => ({
            value: item?._id || "",
            name: `${item?.firstname || ""} ${item?.lastname || ""}`,
            count: item?.articleCount || 0,
          })) || [],
      },
    ],
    [filters]
  );
  return (
    <div className="parent-div-filter">
      <>
        {filterData.map((filter, index) => {
          if (filter?.type === "select") {
            return (
              <div
                key={`${filter?.key}-${index}`}
                className={`sort-div${index === 0 ? "2" : ""}`}
              >
                <h3>{filter?.title || ""}</h3>
                <div className="tag-select-div">
                  <select
                    className="tag-select"
                    value={selectedFilter[filter?.key]}
                    onChange={(e) => {
                      handleFilter(
                        e.target.value,
                        filter?.key,
                        filter?.type === "select"
                      );
                    }}
                  >
                    {filter?.items?.map((item, idx) => (
                      <option
                        key={`${index?.key}-${idx}`}
                        className="tag-option"
                        value={item?.value || ""}
                      >
                        {item?.name || ""}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            );
          } else if (filter.type === "checkbox") {
            return (
              <div key={`${filter?.key}-${index}`} className="author-sort">
                <div className="sort-top">
                  <h3 className="filter-head-p">
                    {filter?.title || ""}
                    <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
                      filter?.items?.length ?? 0
                    })`}</span>
                  </h3>
                  <p
                    className="sort-clear"
                    onClick={() => handleClearFilter(filter?.key || null)}
                  >
                    CLEAR
                  </p>
                </div>
                <div className="filter-item-wrapper">
                  {filter?.items?.map((item, idx) => (
                    <div key={`${index?.key}-${idx}`}>
                      <div className="row-sort">
                        <div className="checkbox-filtername">
                          <input
                            type={filter?.type || "checkbox"}
                            checked={selectedFilter[filter?.key]?.includes(
                              item.value
                            )}
                            onChange={() =>
                              handleFilter(item.value, filter?.key)
                            }
                          />
                          <p className="filter-name-tag">{item?.name || ""}</p>
                        </div>
                        <p className="filter-qty-tag">{item?.count || 0}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          } else {
            return;
          }
        })}
      </>
    </div>
  );
};

export default Sort;
