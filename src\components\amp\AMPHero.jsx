import { dateFormatterWithIST, hasHtmlTags, htmlParser } from "@/utils/Util";
import Link from "next/link";
import React, { useState } from "react";
import { LuCopyPlus } from "react-icons/lu";
import AuthorPopUp from "../stories/AuthorPopUp";
import { RiWhatsappFill } from "react-icons/ri";
import { FaFacebookF, FaLink } from "react-icons/fa";
import { BsTwitterX } from "react-icons/bs";
import SponsoredTag from "../common/SponsoredTag";

const AMPHero = ({
  _id,
  breadcrumbs,
  title,
  description,
  author,
  timeline,
  coverImage,
  caption,
  courtesy,
  contributor,
  altName,
  readTime,
  articleUrl,
  promotional,
}) => {
  const [open, setOpen] = useState(null);
  const [copy, setCopy] = useState("Copy link");

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(articleUrl)
      .then(() => {
        // toast.success("Copied to clipboard");
        setCopy("Link copied");
        setTimeout(() => {
          setCopy("Copy link");
        }, 1000);
      })
      .catch((err) => {
        console.log(err);
        toast.error("Cannot copy to clipboard") + err;
      });
  };
  return (
    <div>
      <section id="storiesherosec" className="storiesHeroSec">
        <div className="container">
          <div className="row d-flex align-items-center justify-content-start">
            <div className="col-md-8">
              <div className="breadcumSec">
                <ol className={"breadcrumb"}>
                  {breadcrumbs &&
                    breadcrumbs.length > 0 &&
                    breadcrumbs.map((breadcrumb, i) => {
                      return (
                        <li
                          className={"breadcrumb-item"}
                          key={`breadcrumb-${i}`}
                        >
                          <Link
                            style={{
                              color: "#db242a",
                              textDecoration: "none",
                              fontSize: "14px",
                              fontWeight: 500,
                              fontFamily: "Karla, sans-serif",
                            }}
                            href={breadcrumb?.slug ?? "#"}
                          >
                            {breadcrumb?.name ?? ""}
                          </Link>
                        </li>
                      );
                    })}
                  {promotional && (
                    <SponsoredTag customStyle={{ display: "inline" }} />
                  )}
                </ol>
              </div>
              <div className="contentSec">
                <h1>{htmlParser(title)}</h1>
                <p>
                  {description
                    ? htmlParser(description)
                    : htmlParser(description)}
                </p>

                {author && author.length > 1 && (
                  <>
                    <div className={"author_wrapper"}>
                      <div className={"author_right"}>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "10px",
                            flexWrap: "wrap",
                          }}
                        >
                          {(author || contributor) && (
                            <span className={"author_name"}>
                              By{" "}
                              {[
                                ...author.map((item, i) => (
                                  <a
                                    href={item?.slug}
                                    key={`author-${i}`}
                                    onMouseEnter={() => setOpen(i)}
                                    // onMouseLeave={() => setOpen(null)} // Optional: Reset on mouse leave
                                    style={{
                                      cursor: "pointer",
                                      textDecoration: "none",
                                      fontWeight: 700,
                                      color: "#db242a",
                                    }}
                                  >
                                    {item?.name}
                                  </a>
                                )),
                                ...contributor.map((el, i) => (
                                  <a
                                    href={item?.slug}
                                    key={`contributor-${i}`}
                                    // Optional: Reset on mouse leave
                                    style={{
                                      cursor: "pointer",
                                      textDecoration: "none",
                                      fontWeight: 700,
                                      color: "#db242a",
                                    }}
                                  >
                                    {el}
                                  </a>
                                )),
                              ].reduce((prev, curr) => [prev, ", ", curr])}
                            </span>
                          )}

                          <span>
                            <LuCopyPlus className={"copysvg"} />
                          </span>
                        </div>
                        {open !== null && (
                          <MultiAuthorPopUp
                            index={open}
                            setOpen={setOpen}
                            data={author}
                          />
                        )}
                      </div>
                    </div>
                    <span className={"timeline"}>
                      LAST UPDATED: {dateFormatterWithIST(timeline)}
                    </span>
                    <span className={"timeline"} style={{ marginLeft: "8px" }}>
                      |
                    </span>
                    <span className={"timeline"} style={{ marginLeft: "8px" }}>
                      {readTime} min read
                    </span>
                  </>
                )}
                {author && author?.length == 1 && (
                  <>
                    {author.map((item, i) => {
                      if (i === 0) {
                        return (
                          <>
                            <div className={"author_wrapper"}>
                              <div className={"author_left"}>
                                <amp-img
                                  className="author_image"
                                  width="50"
                                  height="50"
                                  src={
                                    item?.image ||
                                    "/placeholder/author_placeholder.jpg"
                                  }
                                  alt={item?.name || "Author Image"}
                                  //   layout="cover"
                                />
                              </div>
                              <div className={"author_right"}>
                                <div
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "10px",
                                    flexWrap: "wrap",
                                  }}
                                >
                                  {(author || contributor) && (
                                    <span className={"author_name"}>
                                      By{" "}
                                      {[
                                        ...author.map((item, i) => (
                                          <a
                                            href={item?.slug}
                                            key={`author-${i}`}
                                            onMouseEnter={() => setOpen(i)}
                                            // onMouseLeave={() => setOpen(null)} // Optional: Reset on mouse leave
                                            style={{
                                              cursor: "pointer",
                                              textDecoration: "none",
                                              fontWeight: 700,
                                              color: "#db242a",
                                            }}
                                          >
                                            {item?.name}
                                          </a>
                                        )),
                                        ...contributor.map((el, i) => (
                                          <a
                                            href={item?.slug}
                                            key={`contributor-${i}`}
                                            // Optional: Reset on mouse leave
                                            style={{
                                              cursor: "pointer",
                                              textDecoration: "none",
                                              fontWeight: 700,
                                              color: "#db242a",
                                            }}
                                          >
                                            {el}
                                          </a>
                                        )),
                                      ].reduce((prev, curr) => [
                                        prev,
                                        ", ",
                                        curr,
                                      ])}
                                    </span>
                                  )}
                                  <span>
                                    <LuCopyPlus className={"copysvg"} />
                                  </span>
                                </div>
                                {open !== null && (
                                  <AuthorPopUp
                                    index={i}
                                    setOpen={setOpen}
                                    name={item?.name ?? ""}
                                    subheading={item?.subheading ?? ""}
                                    aboutus={item?.aboutus ?? ""}
                                    slug={item?.slug ?? "#"}
                                    social={item?.social ?? ""}
                                    related={item?.relatedStories ?? []}
                                  />
                                )}
                                <span className={"timeline"}>
                                  LAST UPDATED: {dateFormatterWithIST(timeline)}
                                </span>
                                <span
                                  className={"timeline"}
                                  style={{ marginLeft: "8px" }}
                                >
                                  |
                                </span>
                                <span
                                  className={"timeline"}
                                  style={{ marginLeft: "8px" }}
                                >
                                  {readTime} min read
                                </span>
                              </div>
                            </div>
                          </>
                        );
                      } else {
                        return (
                          <>
                            <div
                              className={"author_wrapper"}
                              onMouseEnter={() => {
                                setOpen(i);
                              }}
                            >
                              <div className={"author_right"}>
                                <div
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "10px",
                                    flexWrap: "wrap",
                                  }}
                                >
                                  <span className={"author_name"}>
                                    {author.length > 1 && i === 0 ? "By " : ""}
                                    {item?.name ?? ""}
                                  </span>
                                  <span>
                                    <LuCopyPlus className={"copysvg"} />
                                  </span>
                                </div>
                                {open !== null && (
                                  <AuthorPopUp
                                    index={i}
                                    setOpen={setOpen}
                                    name={item?.name ?? ""}
                                    subheading={item?.subheading ?? ""}
                                    aboutus={item?.aboutus ?? ""}
                                    slug={item?.slug ?? "#"}
                                    social={item?.social ?? ""}
                                    related={item?.relatedStories ?? []}
                                  />
                                )}
                              </div>
                            </div>
                            <span className={"timeline"}>
                              LAST UPDATED: {dateFormatterWithIST(timeline)}
                            </span>
                            <span
                              className={"timeline"}
                              style={{ marginLeft: "8px" }}
                            >
                              |
                            </span>
                            <span
                              className={"timeline"}
                              style={{ marginLeft: "8px" }}
                            >
                              {readTime} min read
                            </span>
                          </>
                        );
                      }
                    })}
                  </>
                )}
              </div>
            </div>
            <div className="follow-us-section">
              <ul className={"followus"}>
                <Link
                  style={{
                    color: "initial",
                    display: "flex",
                    // marginTop: "20px",
                  }}
                  target="_blank"
                  href={`https://wa.me/?text=${articleUrl}`}
                >
                  <li className={"items"}>
                    <span className={"tooltipText"}>Share on Whatsapp</span>
                    <RiWhatsappFill className={"icons"} />
                  </li>
                </Link>
                <Link
                  style={{
                    color: "initial",
                    display: "flex",
                    // marginTop: "20px",
                  }}
                  target="_blank"
                  href={`https://www.facebook.com/dialog/share?app_id=145634995501895&display=popup&href=${articleUrl}&redirect_uri=${articleUrl}`}
                >
                  <li className={"items"}>
                    <span className={"tooltipText"}>Share on Facebook</span>

                    <FaFacebookF className={"icons"} />
                  </li>
                </Link>
                <Link
                  style={{
                    color: "initial",
                    display: "flex",
                    // marginTop: "20px",
                  }}
                  target="_blank"
                  href={`https://twitter.com/intent/tweet?text=${articleUrl}`}
                >
                  <li className={"items"}>
                    <span className={"tooltipText"}>Share on X</span>
                    <BsTwitterX className={"icons"} />
                  </li>
                </Link>
                <li className={"items"} onClick={copyToClipboard}>
                  <span className={"tooltipText"}>{copy}</span>
                  <FaLink className={"icons"} />
                </li>
              </ul>
            </div>
          </div>
          {/* <div className="row">
            <div className="col-md-12">
              <amp-ad
                width="970"
                height="90"
                type="doubleclick"
                data-slot="/23290324739/THRI-Desktop-Top-970"
                data-multi-size="320x50"
                data-multi-size-validation="false"
              ></amp-ad>
            </div>
          </div> */}
        </div>
      </section>

      {coverImage && (
        <section id="storiesherobanner" className="storiesHeroBanner">
          <div className="container-fluid">
            <div className="row">
              <div className="col-md-12" style={{ padding: 0 }}>
                <div className="heroSection">
                  <div className="heroImgCont">
                    <div className="pos-rel-full">
                      <amp-img
                        src={coverImage}
                        alt={altName}
                        width="1200"
                        height="500"
                        layout="fill"
                        // object-fit="cover"
                      ></amp-img>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="container">
            <div className="row">
              <div className="col-md-12 text-start">
                <div className="mainCaption">
                  {caption && (
                    <span className={"caption"}>
                      {hasHtmlTags(caption) ? htmlParser(caption) : caption}
                    </span>
                  )}
                  {courtesy && (
                    <span className={"courtesy"}>
                      {hasHtmlTags(courtesy) ? htmlParser(courtesy) : courtesy}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default AMPHero;
