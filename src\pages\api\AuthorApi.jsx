import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Author List
export const getAuthor = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/author?slug=${slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Author Articles List
export const getAuthorStories = async (body) => {
  const res = await fetch(Const.Link + `api/tenant/author-stories-list?slug=${body.slug}&limit=${body.limit}&offset=${body.offset}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Latest Articles
export const getAuthorLatest = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/author-latest?slug=${slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Author Sitemap
export const getAuthorSitemap = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/author-sitemap", new Headers("GET"));
  return ProcessAPI(res);
};
