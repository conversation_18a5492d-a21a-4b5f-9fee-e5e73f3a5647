import React from "react";
import Outside<PERSON>lickHandler from "react-outside-click-handler";
import style from "@/components/stories/Stories.module.css";
const DeletePopup = ({ handleDelete, setVideopopup }) => {
  return (
    <div className={style.modal}>
      {/* <Toaster/> */}
      <OutsideClickHandler
        onOutsideClick={() => {
          setVideopopup(null);
        }}
      >
        <div className={style.card}>
          <iframe
            width="100%"
            height="315"
            src="https://www.youtube.com/embed/wm1rKeFLX14?si=pucTcFqCa3SJ-4mK"
            title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerpolicy="strict-origin-when-cross-origin"
            allowfullscreen
          ></iframe>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default DeletePopup;
