import React, { useEffect } from "react";
import Image from "next/image";
import Image1 from "@/assets/images/image1.png";

import { gsap } from "gsap/dist/gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import style from "./Home.module.css";
import Link from "next/link";
import { formatDateTimeHv } from "@/utils/Util";

const HeatVision = ({ data }) => {
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom2", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent2",
        start: "top 60%",
        // markers: true,
      },
    });
  }, []);
  return (
    <>
      {data && data.length > 0 && (
        <section id="heatvision" className={style.heatVision}>
          <div className="container">
            <div className="row">
              <div className="col-md-12">
                <div className="hv-image">
                  {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 700 65">
                    <path d="M85.6 25.2H65.3V2h-16v60.9h16V39.6h20.3v23.3h16.2V2H85.6zM119.2 62.9h41.4V48.7h-25.3v-9H159V25.5h-23.7v-9.4h25.3V2h-41.4zM194.5 2L176 62.9h15l3.2-10.4h18.9l3.2 10.4h16.9L213.8 2h-19.3zm9.5 20.9l5.4 17.3h-10.9l5.5-17.3zM237.8 16.1h17.3v46.8h16V16.1h17.2V2h-50.5zM348.5 40.7L337.4 2h-16.9l19.8 60.9h16.2L376.7 2h-16.8zM391.3 2h16v60.9h-16zM459.7 28.2l-13.6-5.7c-1.7-.7-2.9-1.4-3.7-2-.6-.5-.9-1-.9-1.8 0-.3 0-1.2 1.5-2.2 1.2-.7 2.8-1.1 4.8-1.1 2.8 0 4.8.6 5.9 1.7 1.2 1.2 2 2.6 2.3 4.3l.1.8H472l-.1-1.1c-.5-6.5-2.8-11.6-6.8-15.1S455.3.7 447.9.7c-7.2 0-13 1.6-17.3 4.8-4.3 3.2-6.5 7.6-6.5 12.8 0 4.5 1.3 8.1 3.8 10.7 2.4 2.5 5.8 4.8 10.2 6.6l12.9 5.6c2.2.9 3.6 1.6 4.4 2.3.6.5.9 1.3.9 2.4 0 .9-.3 1.5-.9 2-.7.6-1.6 1-2.8 1.2-1.3.3-2.6.4-4.1.4-1.8 0-3.4-.3-4.9-.8-1.4-.5-2.5-1.3-3.3-2.3-.8-1-1.2-2.3-1.2-3.8v-1h-15.9v1c.2 5 1.6 9.2 3.9 12.4 2.4 3.2 5.5 5.5 9.2 7 3.7 1.5 7.8 2.2 12.1 2.2 5.2 0 9.7-.7 13.4-2.1 3.8-1.4 6.7-3.5 8.7-6.3 2-2.8 3-6.3 3-10.5 0-3.9-1.2-7.3-3.5-10.1-2.1-2.6-5.6-5-10.3-7zM490.6 2h16v60.9h-16zM567.3 4.3C563 1.9 557.8.7 552 .7c-5.7 0-10.8 1.2-15.1 3.4-4.3 2.3-7.7 5.8-10.1 10.5-2.4 4.6-3.6 10.6-3.6 17.6 0 6.9 1.2 12.8 3.6 17.5 2.4 4.8 5.8 8.4 10.1 10.9 4.3 2.4 9.4 3.7 15.1 3.7 5.8 0 10.9-1.2 15.3-3.6 4.4-2.4 7.8-6 10.2-10.8 2.4-4.7 3.6-10.7 3.6-17.7 0-6.8-1.2-12.7-3.6-17.3-2.4-4.6-5.9-8.2-10.2-10.6zm-6.2 41c-2.3 3-5.2 4.5-9.1 4.5-3.7 0-6.6-1.5-8.9-4.5-2.3-3-3.5-7.4-3.5-13 0-5.3 1.2-9.6 3.5-12.6s5.2-4.4 8.9-4.4c3.8 0 6.8 1.4 9.1 4.4 2.3 3 3.5 7.2 3.5 12.5 0 5.6-1.2 10-3.5 13.1zM634.7 2v31.9L613.5 2h-16.2v60.9h16V29.5l22.3 33.4h15.1V2z"></path>
                  </svg> */}
                  <h2 className={style.heading}>SPOTLIGHT</h2>
                  <div className="hv-line"></div>
                </div>
                <p className={style.subheading}>
                  All that is unique and surprising
                </p>
              </div>
            </div>
            <div className="row">
              <div className="col-md-12 col-lg-7  zoom-cont">
                <Link href={data[0]?.slug ?? ""}>
                  <div
                    className={`${style.hvImageSec} pos-rel image-zoom-parent2`}
                  >
                    <Image
                      className="image-zoom2 imgcover "
                      src={
                        data[0]?.croppedImg
                          ? data[0]?.croppedImg
                          : data[0]?.coverImg
                          ? data[0]?.coverImg
                          : ""
                      }
                      alt={data[0]?.altName ?? ""}
                      fill
                    />
                  </div>
                  <div className={style.hvContentSec}>
                    <span className={style.smallTime}>
                      {formatDateTimeHv(data[0]?.timestamp ?? "")}
                    </span>
                    <h3>{data[0]?.title ?? ""}</h3>
                    <p>
                      {data[0]?.excerpt?.replace(/(<([^>]+)>)/gi, "") ?? ""}
                    </p>
                  </div>
                </Link>
              </div>
              <div className="col-md-12 col-lg-5">
                <div
                  className={style.hvItems}
                  // style={{ position: "relative", top: "-29px" }}
                >
                  {data.map(
                    (item, i) =>
                      i !== 0 && (
                        <Link
                          className={style.hvContentSec}
                          href={item?.slug ?? "#"}
                          key={`hv-${i}`}
                        >
                          <span className={style.smallTime}>
                            {formatDateTimeHv(item?.timestamp ?? "")}
                          </span>
                          <h3 className={style.sideTitle}>
                            {item?.title ?? ""}
                          </h3>
                          <p>
                            {item?.excerpt?.replace(/(<([^>]+)>)/gi, "") ?? ""}
                          </p>
                        </Link>
                      )
                  )}
                </div>
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default HeatVision;
