import React from "react";
import SeoHeader from "@/components/seo/SeoHeader";
import Layout from "@/components/layout/Layout2";
import Youtube from "@/components/thrtv/YoutubeSubcat";
import { Const } from "@/utils/Constants";
import { getCategoryVideos } from "@/pages/api/CategoryApi";

const VideoSubcategory = ({ meta, title, data }) => {
  return (
    <Layout>
      <SeoHeader meta={meta} />
      <div className="videoTopmargin">
        <Youtube heading={title ?? ""} data={data ?? []} />
      </div>
    </Layout>
  );
};

export default VideoSubcategory;

export async function getServerSideProps(context) {
  const url = `/thr-video/${context.params.subcategory}`;
  const payload = {
    slug: url,
    limit: Const.Limit,
    offset: Const.Offset,
    shorts: false,
  };
  try {
    const response = await getCategoryVideos(payload);
    if (response.status !== "success") {
      return {
        props: {
          notFound: true,
        },
      };
    }
    const { meta, title, data } = response?.data;
    return {
      props: {
        meta: meta ?? {},
        title: title ?? "",
        data: data && data.length > 0 ? data : [],
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
  }
}
