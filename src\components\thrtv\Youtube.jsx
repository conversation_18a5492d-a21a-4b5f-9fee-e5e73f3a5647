import React, { useRef, useState, useEffect } from "react";
import Link from "next/link";
import { FiPlay } from "react-icons/fi";
import Image from "next/image";
import {
  dateFormateWithTimeShort,
  getVideoDuration,
  getVideoCount,
} from "@/utils/Util";
import VideoPopup from "../common/VideoPopUp";
import Button from "../common/Button";

const TopVideos = ({ key, heading, data, seeAll }) => {
  const [open, setOpen] = useState(null);
  const [durations, setDurations] = useState([]);
  const [counts, setCounts] = useState([]);
  const videoRefs = useRef([]);
  useEffect(() => {
    const fetchDurations = async () => {
      const fetchedDurations = await Promise.all(
        data.map((item) => getVideoDuration(item?.src ?? ""))
      );
      setDurations(fetchedDurations);
    };

    const fetchCounts = async () => {
      const fetchedCounts = await Promise.all(
        data.map((item) => getVideoCount(item?.src ?? ""))
      );
      setCounts(fetchedCounts);
    };

    fetchDurations();
    fetchCounts();
  }, [data]);

  const handleMouseEnter = (index) => {
    const video = videoRefs.current[index];
    if (video && video.play) {
      video.play();
    }
  };

  const handleMouseLeave = (index) => {
    const video = videoRefs.current[index];
    if (video && video.pause) {
      video.pause();
      video.currentTime = 0;
    }
  };

  return (
    <>
      <section id="top-videos" key={key}>
        <div className="container">
          <div className="row">
            <div
              className="col-md-12 flex-spacebtn"
              style={{ marginBottom: "20px" }}
            >
              <h2 style={{ margin: "0px" }}>{heading}</h2>
              <div>
                <Button href={seeAll}>See All</Button>
              </div>
            </div>
          </div>
          <div className="row scrollable-div">
            {data.map((item, i) => (
              <div className="col-md-3 rn-card" key={`top-videos-${i}`}>
                <div className="card-wrapper">
                  <div className="feature-box" onClick={() => setOpen(i)}>
                    <FiPlay className="playbtnyt" />
                    <div className="video-timestamp">
                      {durations[i] || "Loading..."}
                    </div>
                    <Image
                      src={item?.coverImg ?? ""}
                      alt={item?.title ?? ""}
                      fill
                    />
                  </div>
                  <Link href={item?.slug ?? "#"} className="content-sec">
                    <h3 className="card-title">{item?.title ?? ""}</h3>
                    <span className="view-timeline" style={{color:'#db242a'}}>{`${
                      counts[i] || "Loading..."
                    } `}</span>
                    <span className="view-timeline">| {dateFormateWithTimeShort(
                      item?.timestamp ?? ""
                    )}</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      <VideoPopup open={open} setOpen={setOpen} videoarray={data} />
    </>
  );
};

export default TopVideos;
