import React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import style from "./Stories.module.css";
import TiptapRendered from "../common/TiptapRenderer";

const MainContent = ({ _id, content, note, tag }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  return (
    <div className="row">
      <div className="col-md-12">
        <div className={style.mainContent}>
          <TiptapRendered
            _id={_id}
            content={content?.content ?? []}
            router={router}
          />
          {note && (
            <p style={{ textAlign: "left", marginTop: "10px" }}>
              <span
                style={{ color: "rgb(148, 148, 148)", fontStyle: "italic" }}
              >
                {note}
              </span>
            </p>
          )}
        </div>
      </div>

      {tag && tag.length > 0 && (
        <div className="col-md-6" style={{ width: "100%" }}>
          <div className={style.readMore}>
            <h3>READ MORE ABOUT:</h3>
            <div className={style.breadcumSec}>
              <ol className={style.breadcrumb}>
                {tag.map((item, i) => {
                  return (
                    <>
                      <li
                        className={style["breadcrumb-item"]}
                        key={`tags-${i}`}
                      >
                        <Link href={item?.slug ?? "#"}>{item?.name ?? ""}</Link>
                      </li>
                    </>
                  );
                })}
              </ol>
            </div>
          </div>
        </div>
      )}
      <div className="col-md-6"></div>
    </div>
  );
};

export default MainContent;
