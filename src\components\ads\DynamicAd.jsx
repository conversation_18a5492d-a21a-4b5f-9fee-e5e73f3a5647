import React, { useEffect, useRef, useCallback, useState } from "react";
import { useRouter } from "next/router";

let gptScriptPromise = null;
const loadGPTScript = () => {
  if (typeof window === "undefined") return Promise.resolve();
  if (window.googletag?.apiReady) return Promise.resolve();

  if (!gptScriptPromise) {
    gptScriptPromise = new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = "https://securepubads.g.doubleclick.net/tag/js/gpt.js";
      script.async = true;
      script.onload = () => {
        const checkReady = setInterval(() => {
          if (window.googletag?.apiReady) {
            clearInterval(checkReady);
            resolve();
          }
        }, 100);
      };
      script.onerror = () => reject("GPT Script failed to load.");
      document.head.appendChild(script);
    });
  }

  return gptScriptPromise;
};

const DynamicAd = ({ adUnits = [], targeting = {}, style = {} }) => {
  const router = useRouter();
  const [currentAd, setCurrentAd] = useState(null);
  const renderedRef = useRef(false);
  const adContainerRef = useRef(null);
  const gptReadyRef = useRef(false); // Track GPT readiness

  const matchAdUnit = useCallback(() => {
    const width = window.innerWidth;
    const matched = adUnits.find(
      (ad) => width >= ad.minWidth && width <= ad.maxWidth
    );
    setCurrentAd(matched || null);
  }, [adUnits]);

  const destroyAds = useCallback(() => {
    if (window.googletag?.apiReady) {
      const googletag = window.googletag;
      googletag.cmd.push(() => {
        googletag.destroySlots();
      });
    }

    if (currentAd && adContainerRef.current) {
      adContainerRef.current.innerHTML = "";
    }
    renderedRef.current = false;
  }, [currentAd]);

  const renderAd = useCallback(() => {
    if (!currentAd || renderedRef.current || !gptReadyRef.current) return;
    const googletag = window.googletag;
    const adDiv = document.getElementById(currentAd.divId);
    if (!adDiv) return;

    googletag.cmd.push(() => {
      const slot = googletag
        .defineSlot(currentAd.adUnitPath, currentAd.adSize, currentAd.divId)
        ?.addService(googletag.pubads());

      if (slot) {
        Object.entries(targeting).forEach(([key, val]) => {
          slot.setTargeting(key, Array.isArray(val) ? val : [val]);
        });

        if (!window.__gptServicesEnabled) {
          googletag.pubads().enableSingleRequest();
          googletag.enableServices();
          window.__gptServicesEnabled = true;
        }

        googletag.display(currentAd.divId);
        renderedRef.current = true;
      }
    });
  }, [currentAd, targeting]);

  // Load GPT only once on mount
  useEffect(() => {
    loadGPTScript().then(() => {
      gptReadyRef.current = true;
      renderAd(); // render if ad already matched
    });
  }, []);

  // Match initial ad once
  useEffect(() => {
    matchAdUnit();
  }, []);

  // Re-render ad when currentAd changes and GPT is ready
  useEffect(() => {
    if (gptReadyRef.current) {
      setTimeout(() => {
        renderAd();
      }, 100);
    }
  }, [currentAd, renderAd]);

  // Handle route change & resize
  useEffect(() => {
    const handleChange = () => {
      destroyAds();
      matchAdUnit();
    };

    router.events.on("routeChangeComplete", handleChange);
    window.addEventListener("resize", handleChange);
    return () => {
      router.events.off("routeChangeComplete", handleChange);
      window.removeEventListener("resize", handleChange);
    };
  }, [router, destroyAds, matchAdUnit]);

  if (!currentAd) return null;

  return (
    <div className="ad-flex-all" ref={adContainerRef}>
      <div
        id={currentAd.divId}
        className="ad-text"
        style={{
          width: "100%",
          maxWidth: `${currentAd?.adSize?.[0]}px` || "300px",
          position: "relative",
          ...style,
        }}
      />
    </div>
  );
};

export default DynamicAd;
